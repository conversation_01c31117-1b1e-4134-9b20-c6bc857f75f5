
import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Search } from 'lucide-react';
import FullScreenNav from '@/components/navigation/FullScreenNav';
import SearchOverlay from '@/components/features/SearchOverlay';

const Header = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  // Check if we're on the homepage
  const isHomepage = location.pathname === '/';

  const handleLogoClick = () => {
    navigate('/');
  };

  const handleMenuToggle = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleMenuClose = () => {
    setIsMenuOpen(false);
  };

  const handleSearchToggle = () => {
    setIsSearchOpen(!isSearchOpen);
  };

  const handleSearchClose = () => {
    setIsSearchOpen(false);
  };

  // Homepage Header (transparent design with enhanced readability)
  const HomepageHeader = () => (
    <header className="absolute top-0 left-0 right-0 z-10 p-4 sm:p-6 md:p-8">
      {/* Transparent backdrop with subtle blur for better text readability */}
      <div
        className="absolute inset-0 "

      />

      <div className="relative flex justify-between items-center">
        {/* Left Icon - Hamburger Menu */}
        <button
          onClick={handleMenuToggle}
          className="text-white hover:text-gray-300 transition-colors p-2 rounded-full hover:bg-white/20 backdrop-blur-sm"
          style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.5)' }}
          aria-label="Menu"
        >
          <img
            src="/photos/menu.svg"
            alt="Menu"
            className="h-5 w-5 sm:h-6 sm:w-6 drop-shadow-lg"
          />
        </button>


        {/* Right Icon - Search */}
        <button
          onClick={handleSearchToggle}
          className="text-white hover:text-gray-300 transition-colors p-2 rounded-full hover:bg-white/20 backdrop-blur-sm"
          style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.5)' }}
          aria-label="Search"
        >
          <Search className="h-5 w-5 sm:h-6 sm:w-6 drop-shadow-lg" />
        </button>
      </div>
    </header>
  );

  // Other Pages Header (transparent glass morphism design)
  const OtherPagesHeader = () => (
    <header
      className="fixed top-0 left-0 right-0 z-50 transition-all duration-500"
      style={{
        background: 'rgba(44, 95, 93, 0.15)',
        backdropFilter: 'blur(20px) saturate(180%)',
        WebkitBackdropFilter: 'blur(20px) saturate(180%)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        boxShadow: '0 8px 32px rgba(44, 95, 93, 0.2)',
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20 py-3">
          {/* Left Side - Logo (Clickable) */}
          <div className="flex items-center space-x-3">
            <button
              onClick={handleLogoClick}
              className="transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white/50 rounded-lg p-2"
              aria-label="Go to homepage"
            >
              <img
                src="/photos/fulllogo.svg"
                alt="Warriors of Africa Safari"
                className="h-13 w-15 drop-shadow-lg"
              />
            </button>
          </div>

          {/* Right Side - Search and Menu Icons */}
          <div className="flex items-center space-x-4">
            <button
              onClick={handleSearchToggle}
              className="text-white hover:text-gray-300 transition-colors p-2 rounded-full hover:bg-white/20 backdrop-blur-sm"
              style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.5)' }}
              aria-label="Search"
            >
              <Search className="h-5 w-5 drop-shadow-lg" />
            </button>

            <button
              onClick={handleMenuToggle}
              className="text-white hover:text-gray-300 transition-colors p-2 rounded-full hover:bg-white/20 backdrop-blur-sm"
              style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.5)' }}
              aria-label="Menu"
            >
              <img
                src="/photos/menu.svg"
                alt="Menu"
                className="h-5 w-5 drop-shadow-lg"
              />
            </button>
          </div>
        </div>
      </div>
    </header>
  );

  return (
    <>
      {isHomepage ? <HomepageHeader /> : <OtherPagesHeader />}

      {/* Full Screen Navigation */}
      <FullScreenNav isOpen={isMenuOpen} onClose={handleMenuClose} />

      {/* Search Overlay */}
      {isSearchOpen && <SearchOverlay onClose={handleSearchClose} />}

      {/* Add padding for fixed header on non-homepage pages */}
      {!isHomepage && <div className="h-20" />}
    </>
  );
};

export default Header;


// Note: In production, this API key should be stored in environment variables
// and requests should go through a backend proxy to keep the key secure
const WEATHER_API_KEY = 'e09a23c291fa42f0973125501252906';
const WEATHER_API_BASE_URL = 'https://api.weatherapi.com/v1';

export interface WeatherLocation {
  name: string;
  region: string;
  country: string;
  lat: number;
  lon: number;
  tz_id: string;
  localtime: string;
}

export interface WeatherCondition {
  text: string;
  icon: string;
  code: number;
}

export interface CurrentWeather {
  last_updated: string;
  temp_c: number;
  temp_f: number;
  is_day: number;
  condition: WeatherCondition;
  wind_mph: number;
  wind_kph: number;
  wind_degree: number;
  wind_dir: string;
  pressure_mb: number;
  pressure_in: number;
  precip_mm: number;
  precip_in: number;
  humidity: number;
  cloud: number;
  feelslike_c: number;
  feelslike_f: number;
  vis_km: number;
  vis_miles: number;
  uv: number;
  gust_mph: number;
  gust_kph: number;
}

export interface ForecastDay {
  date: string;
  date_epoch: number;
  day: {
    maxtemp_c: number;
    maxtemp_f: number;
    mintemp_c: number;
    mintemp_f: number;
    avgtemp_c: number;
    avgtemp_f: number;
    maxwind_mph: number;
    maxwind_kph: number;
    totalprecip_mm: number;
    totalprecip_in: number;
    avgvis_km: number;
    avgvis_miles: number;
    avghumidity: number;
    daily_will_it_rain: number;
    daily_chance_of_rain: number;
    daily_will_it_snow: number;
    daily_chance_of_snow: number;
    condition: WeatherCondition;
    uv: number;
  };
  astro: {
    sunrise: string;
    sunset: string;
    moonrise: string;
    moonset: string;
    moon_phase: string;
    moon_illumination: string;
  };
  hour: Array<{
    time: string;
    temp_c: number;
    temp_f: number;
    condition: WeatherCondition;
    wind_mph: number;
    wind_kph: number;
    wind_degree: number;
    wind_dir: string;
    pressure_mb: number;
    pressure_in: number;
    precip_mm: number;
    precip_in: number;
    humidity: number;
    cloud: number;
    feelslike_c: number;
    feelslike_f: number;
    windchill_c: number;
    windchill_f: number;
    heatindex_c: number;
    heatindex_f: number;
    dewpoint_c: number;
    dewpoint_f: number;
    will_it_rain: number;
    chance_of_rain: number;
    will_it_snow: number;
    chance_of_snow: number;
    vis_km: number;
    vis_miles: number;
    gust_mph: number;
    gust_kph: number;
    uv: number;
  }>;
}

export interface WeatherResponse {
  location: WeatherLocation;
  current: CurrentWeather;
  forecast?: {
    forecastday: ForecastDay[];
  };
}

export class WeatherService {
  // Get current weather for a location
  static async getCurrentWeather(location: string): Promise<WeatherResponse> {
    try {
      const response = await fetch(
        `${WEATHER_API_BASE_URL}/current.json?key=${WEATHER_API_KEY}&q=${encodeURIComponent(location)}&aqi=no`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status} - ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching current weather:', error);
      // If CORS error or network error, provide helpful message
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Unable to connect to weather service. This may be due to CORS restrictions in development.');
      }
      throw error;
    }
  }

  // Get weather forecast for a location
  static async getForecast(location: string, days: number = 5): Promise<WeatherResponse> {
    try {
      const response = await fetch(
        `${WEATHER_API_BASE_URL}/forecast.json?key=${WEATHER_API_KEY}&q=${encodeURIComponent(location)}&days=${days}&aqi=no&alerts=no`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status} - ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching weather forecast:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Unable to connect to weather service. This may be due to CORS restrictions in development.');
      }
      throw error;
    }
  }

  // Get weather for multiple safari locations
  static async getSafariWeather(): Promise<WeatherResponse[]> {
    const safariLocations = [
      'Mikumi National Park, Tanzania',
      'Ngorongoro Crater, Tanzania',
      'Arusha National Park, Tanzania',
      'Lake Manyara, Tanzania',
      'Ruaha National Park, Tanzania'
    ];

    try {
      const weatherPromises = safariLocations.map(location => 
        this.getForecast(location, 3)
      );
      
      const results = await Promise.allSettled(weatherPromises);
      
      return results
        .filter((result): result is PromiseFulfilledResult<WeatherResponse> => 
          result.status === 'fulfilled'
        )
        .map(result => result.value);
    } catch (error) {
      console.error('Error fetching safari weather:', error);
      throw error;
    }
  }

  // Generate safari recommendation based on weather
  static generateSafariRecommendation(weather: CurrentWeather): string {
    const temp = weather.temp_c;
    const humidity = weather.humidity;
    const windSpeed = weather.wind_kph;
    const visibility = weather.vis_km;
    const precipitation = weather.precip_mm;

    if (precipitation > 5) {
      return 'Light rain - Great for photography with dramatic skies';
    }
    
    if (temp >= 20 && temp <= 30 && humidity < 70 && visibility > 8) {
      return 'Perfect conditions for game drives';
    }
    
    if (temp > 30) {
      return 'Hot weather - Early morning drives recommended';
    }
    
    if (temp < 15) {
      return 'Cool weather - Perfect for walking safaris';
    }
    
    if (windSpeed > 20) {
      return 'Windy conditions - Good for bird watching';
    }
    
    if (visibility < 5) {
      return 'Limited visibility - Consider indoor activities';
    }
    
    return 'Good conditions for safari activities';
  }

  // Convert weather condition code to icon
  static getWeatherIcon(condition: WeatherCondition, size: 'small' | 'medium' | 'large' = 'medium'): string {
    // WeatherAPI.com provides icon URLs, but we can also map to our own icons
    const iconSize = size === 'small' ? '32x32' : size === 'large' ? '128x128' : '64x64';
    return condition.icon.replace('64x64', iconSize);
  }

  // Format weather data for display
  static formatWeatherData(weatherResponse: WeatherResponse) {
    const { location, current, forecast } = weatherResponse;
    
    return {
      location: `${location.name}, ${location.region}`,
      coordinates: { lat: location.lat, lon: location.lon },
      temperature: Math.round(current.temp_c),
      condition: current.condition.text,
      description: current.condition.text,
      humidity: current.humidity,
      windSpeed: Math.round(current.wind_kph),
      visibility: current.vis_km,
      recommendation: this.generateSafariRecommendation(current),
      icon: current.condition.icon,
      forecast: forecast?.forecastday.slice(0, 5).map(day => ({
        date: new Date(day.date).toLocaleDateString('en-US', { 
          weekday: 'short', 
          month: 'short', 
          day: 'numeric' 
        }),
        temp: Math.round(day.day.avgtemp_c),
        condition: day.day.condition.text,
        description: day.day.condition.text,
        icon: day.day.condition.icon
      })) || []
    };
  }
}


import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';

const Cookies = () => {
  return (
    <div className="min-h-screen bg-[#16191D] overflow-x-hidden">
      <Header />

      {/* Luxury Hero Section - Fully Responsive */}
      <div className="relative overflow-hidden bg-[#16191D] pt-12 xs:pt-14 sm:pt-16 md:pt-20">
        {/* Elegant Background Pattern - Responsive */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                             radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
            backgroundSize: '30px 30px'
          }} />
        </div>

        {/* Hero Content - Mobile Optimized */}
        <div className="relative z-10 container mx-auto px-3 xs:px-4 sm:px-6 md:px-8 py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 xl:py-24 text-center">
          <div className="max-w-5xl mx-auto">
            {/* Luxury Badge - Mobile Responsive */}
            <div className="inline-flex items-center gap-1 xs:gap-1.5 sm:gap-2 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-2 xs:px-3 sm:px-4 md:px-6 py-1 xs:py-1.5 sm:py-2 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
              <Cookie className="w-2.5 h-2.5 xs:w-3 xs:h-3 sm:w-4 sm:h-4 text-[#D4C2A4]" />
              <span className="font-open-sans text-2xs xs:text-xs sm:text-sm text-[#D4C2A4] tracking-wider uppercase">Cookie Management</span>
            </div>

            {/* Main Title - Highly Responsive */}
            <h1 className="font-cormorant text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl text-[#F2EEE6] mb-3 xs:mb-4 sm:mb-5 md:mb-6 leading-tight">
              Cookie
              <span className="block text-[#D4C2A4] italic">Policy</span>
            </h1>

            {/* Description - Mobile Optimized */}
            <p className="font-open-sans text-[#F2EEE6]/70 text-xs xs:text-sm sm:text-base md:text-lg max-w-sm xs:max-w-md sm:max-w-xl md:max-w-2xl mx-auto leading-relaxed px-2 xs:px-3 sm:px-4">
              Transparent information about how we use cookies to enhance your browsing experience and improve our services.
            </p>

            {/* Decorative Element - Responsive */}
            <div className="mt-6 xs:mt-7 sm:mt-8 md:mt-10 lg:mt-12 flex justify-center">
              <div className="w-12 xs:w-14 sm:w-16 md:w-20 lg:w-24 h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Luxury Cookie Content - Fully Responsive */}
      <div className="relative bg-[#16191D] py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 xl:py-24">
        {/* Subtle Background Pattern - Mobile Optimized */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px)`,
            backgroundSize: '40px 40px'
          }}></div>
        </div>

        <div className="relative z-10 container mx-auto px-3 xs:px-4 sm:px-6 md:px-8">
          <div className="max-w-5xl mx-auto space-y-8 xs:space-y-10 sm:space-y-12 md:space-y-16 lg:space-y-20">

            {/* Essential Cookies Section - Mobile Responsive */}
            <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              {/* Section Header - Mobile Optimized */}
              <div className="flex items-center gap-2 xs:gap-3 sm:gap-4 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
                <div className="bg-[#D4C2A4]/20 p-1.5 xs:p-2 sm:p-3 rounded-md xs:rounded-lg sm:rounded-xl flex-shrink-0">
                  <Cookie className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-[#D4C2A4]" />
                </div>
                <h2 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-[#F2EEE6] leading-tight">
                  Essential Cookies
                </h2>
              </div>

              {/* Content - Mobile Responsive */}
              <div className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6">
                <div className="relative">
                  <span className="font-cormorant float-left text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl leading-none pt-0.5 xs:pt-1 pr-2 xs:pr-3 sm:pr-4 text-[#D4C2A4] opacity-80">
                    W
                  </span>
                  <p className="font-open-sans text-[#F2EEE6]/90 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                    e use cookies and similar tracking technologies to enhance your browsing experience on our safari website, analyze website traffic, and understand where our visitors are coming from. Essential cookies are necessary for the website to function properly and cannot be switched off in our systems. These cookies enable core functionality such as user authentication, security tokens, shopping cart contents, and basic website operations. They are usually only set in response to actions made by you which amount to a request for services, such as setting your privacy preferences, logging in, or filling in forms.
                  </p>
                </div>

                <div className="flex justify-center my-4 xs:my-5 sm:my-6 md:my-8">
                  <div className="w-16 xs:w-20 sm:w-24 md:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
                </div>

                <p className="font-open-sans text-[#F2EEE6]/80 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                  At Warrior of Africa Safari Tours, we are committed to transparency about how we use cookies and similar technologies. This Cookie Policy explains what cookies are, how we use them, and how you can control them. Cookies are small text files that are placed on your computer or mobile device when you visit our website. They help us provide you with a better experience by remembering your preferences and understanding how you use our site. We use different types of cookies for various purposes, and you have control over most of them through your browser settings.
                </p>
              </div>
            </div>
            {/* Analytics Tracking Section - Mobile Responsive */}
            <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              {/* Section Header - Mobile Optimized */}
              <div className="flex items-center gap-2 xs:gap-3 sm:gap-4 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
                <div className="bg-[#D4C2A4]/20 p-1.5 xs:p-2 sm:p-3 rounded-md xs:rounded-lg sm:rounded-xl flex-shrink-0">
                  <BarChart3 className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-[#D4C2A4]" />
                </div>
                <h2 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-[#F2EEE6] leading-tight">
                  Analytics Tracking
                </h2>
              </div>

              {/* Content - Mobile Responsive */}
              <div className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6">
                <div className="relative">
                  <span className="font-cormorant float-left text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl leading-none pt-0.5 xs:pt-1 pr-2 xs:pr-3 sm:pr-4 text-[#D4C2A4] opacity-80">
                    A
                  </span>
                  <p className="font-open-sans text-[#F2EEE6]/90 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                    nalytics cookies help us understand how visitors interact with our safari website by collecting and reporting information anonymously. These cookies track page views, user behavior patterns, popular content, and help us identify areas for improvement. We use services like Google Analytics to gather insights about our website performance and user preferences. This data helps us optimize your browsing experience and ensure our safari information is presented in the most helpful way. Functional cookies enable enhanced functionality and personalization features such as language preferences, chat widgets, and social media integrations that make your safari planning experience more convenient.
                  </p>
                </div>

                <div className="flex justify-center my-4 xs:my-5 sm:my-6 md:my-8">
                  <div className="w-16 xs:w-20 sm:w-24 md:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
                </div>

                <p className="font-open-sans text-[#F2EEE6]/80 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                  Marketing cookies are used to deliver personalized advertisements and track the effectiveness of our marketing campaigns. These cookies may be set by our advertising partners and help us show you relevant safari offers and content based on your interests. You can control cookie preferences through your browser settings, and most browsers allow you to refuse cookies or alert you when cookies are being sent. However, please note that disabling certain cookies may affect the functionality of our website and your ability to access some features. We are committed to transparency about our cookie usage and will always respect your privacy choices.
                </p>
              </div>
            </div>

            {/* Cookie Control Section - Mobile Responsive */}
            <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              {/* Section Header - Mobile Optimized */}
              <div className="flex items-center gap-2 xs:gap-3 sm:gap-4 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
                <div className="bg-[#D4C2A4]/20 p-1.5 xs:p-2 sm:p-3 rounded-md xs:rounded-lg sm:rounded-xl flex-shrink-0">
                  <Settings className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-[#D4C2A4]" />
                </div>
                <h2 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-[#F2EEE6] leading-tight">
                  Cookie Control
                </h2>
              </div>

              {/* Content - Mobile Responsive */}
              <div className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6">
                <div className="relative">
                  <span className="font-cormorant float-left text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl leading-none pt-0.5 xs:pt-1 pr-2 xs:pr-3 sm:pr-4 text-[#D4C2A4] opacity-80">
                    Y
                  </span>
                  <p className="font-open-sans text-[#F2EEE6]/90 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                    ou have control over how cookies are used on our safari website. Most web browsers automatically accept cookies, but you can modify your browser settings to decline cookies if you prefer. You can delete existing cookies from your device and set your browser to reject future cookies. However, please be aware that disabling cookies may impact your ability to use certain features of our website, such as staying logged in, maintaining your safari preferences, or accessing personalized content. Some essential cookies are necessary for basic website functionality and cannot be disabled while using our services.
                  </p>
                </div>

                <div className="flex justify-center my-4 xs:my-5 sm:my-6 md:my-8">
                  <div className="w-16 xs:w-20 sm:w-24 md:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
                </div>

                <p className="font-open-sans text-[#F2EEE6]/80 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                  If you have any questions about our Cookie Policy or how we use cookies on our safari website, please contact <NAME_EMAIL> or through our website contact form. We are committed to transparency and will be happy to explain our cookie practices in more detail. This Cookie Policy may be updated from time to time to reflect changes in our practices, technology, or legal requirements. We will notify you of any significant changes by posting the updated policy on our website with a new effective date. Your continued use of our website after any changes indicates your acceptance of the updated Cookie Policy.
                </p>
              </div>
            </div>

            {/* Premium Contact Section - Fully Mobile Responsive */}
            <div className="bg-gradient-to-br from-[#D4C2A4]/10 to-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/30 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              <div className="text-center">
                <Star className="w-6 h-6 xs:w-7 xs:h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 text-[#D4C2A4] mx-auto mb-3 xs:mb-4 sm:mb-5 md:mb-6" />
                <h3 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl text-[#F2EEE6] mb-2 xs:mb-3 sm:mb-4 leading-tight">
                  Cookie Questions?
                </h3>
                <p className="font-open-sans text-[#F2EEE6]/70 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed max-w-sm xs:max-w-md sm:max-w-lg md:max-w-xl lg:max-w-2xl mx-auto mb-4 xs:mb-5 sm:mb-6 md:mb-8 px-2">
                  Our team is here to explain our cookie practices and help you understand how to control your preferences.
                </p>
                <div className="inline-flex items-center gap-1.5 xs:gap-2 bg-[#D4C2A4]/20 backdrop-blur-sm border border-[#D4C2A4]/30 rounded-full px-3 xs:px-4 sm:px-5 md:px-6 py-1.5 xs:py-2 sm:py-2.5 md:py-3">
                  <span className="font-open-sans text-[#D4C2A4] text-xs xs:text-sm sm:text-base font-medium break-all xs:break-normal">
                    <EMAIL>
                  </span>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <Footer isDarkBackground={true} />
    </div>
  );
};

export default Cookies;



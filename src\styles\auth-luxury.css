/* Luxury Authentication Pages Styling */

/* Premium Floating Particles Animation */
@keyframes luxuryFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-40px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
    opacity: 0.6;
  }
}

.luxury-particle {
  animation: luxuryFloat 6s ease-in-out infinite;
}

/* Premium Glass Morphism Effects */
.luxury-glass-container {
  background: linear-gradient(135deg, 
    rgba(212, 194, 164, 0.1) 0%,
    rgba(212, 194, 164, 0.05) 50%,
    rgba(212, 194, 164, 0.08) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(212, 194, 164, 0.3);
  box-shadow: 
    0 8px 32px rgba(22, 25, 29, 0.3),
    0 0 0 1px rgba(212, 194, 164, 0.1),
    inset 0 1px 0 rgba(212, 194, 164, 0.2);
}

.luxury-glass-container:hover {
  background: linear-gradient(135deg, 
    rgba(212, 194, 164, 0.15) 0%,
    rgba(212, 194, 164, 0.08) 50%,
    rgba(212, 194, 164, 0.12) 100%);
  border-color: rgba(212, 194, 164, 0.5);
  box-shadow: 
    0 20px 40px rgba(22, 25, 29, 0.4),
    0 0 0 1px rgba(212, 194, 164, 0.3),
    inset 0 1px 0 rgba(212, 194, 164, 0.3);
}

/* Luxury Input Field Effects */
.luxury-input-field {
  position: relative;
  overflow: hidden;
}

.luxury-input-field::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.1), 
    transparent);
  transition: left 0.5s ease;
}

.luxury-input-field:focus-within::before {
  left: 100%;
}

/* Premium Button Hover Effects */
.luxury-button {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #D4C2A4 0%, #C4B294 100%);
  transition: all 0.3s ease;
}

.luxury-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  transition: left 0.6s ease;
}

.luxury-button:hover::before {
  left: 100%;
}

.luxury-button:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 10px 25px rgba(212, 194, 164, 0.4),
    0 0 0 1px rgba(212, 194, 164, 0.3);
}

/* VIP Glow Effects */
@keyframes luxuryGlow {
  0%, 100% {
    text-shadow: 
      0 0 5px rgba(212, 194, 164, 0.3),
      0 0 10px rgba(212, 194, 164, 0.2),
      0 0 15px rgba(212, 194, 164, 0.1);
  }
  50% {
    text-shadow: 
      0 0 10px rgba(212, 194, 164, 0.5),
      0 0 20px rgba(212, 194, 164, 0.3),
      0 0 30px rgba(212, 194, 164, 0.2);
  }
}

.luxury-glow-text {
  animation: luxuryGlow 3s ease-in-out infinite;
}

/* Premium Loading Animation */
@keyframes luxuryShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.luxury-loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(212, 194, 164, 0.1) 25%,
    rgba(212, 194, 164, 0.3) 50%,
    rgba(212, 194, 164, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: luxuryShimmer 2s infinite;
}

/* Elegant Border Animations */
@keyframes luxuryBorderGlow {
  0%, 100% {
    border-color: rgba(212, 194, 164, 0.3);
    box-shadow: 0 0 5px rgba(212, 194, 164, 0.2);
  }
  50% {
    border-color: rgba(212, 194, 164, 0.6);
    box-shadow: 0 0 15px rgba(212, 194, 164, 0.4);
  }
}

.luxury-border-glow {
  animation: luxuryBorderGlow 2s ease-in-out infinite;
}

/* Responsive Luxury Enhancements */
@media (max-width: 768px) {
  .luxury-glass-container {
    backdrop-filter: blur(15px) saturate(150%);
    -webkit-backdrop-filter: blur(15px) saturate(150%);
  }
  
  .luxury-particle {
    animation-duration: 4s;
  }
  
  .luxury-glow-text {
    animation: none;
    text-shadow: 0 0 8px rgba(212, 194, 164, 0.3);
  }
}

@media (max-width: 480px) {
  .luxury-glass-container {
    backdrop-filter: blur(10px) saturate(120%);
    -webkit-backdrop-filter: blur(10px) saturate(120%);
  }
  
  .luxury-button:hover {
    transform: translateY(-1px);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .luxury-glass-container {
    background: rgba(212, 194, 164, 0.2);
    border-color: rgba(212, 194, 164, 0.6);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .luxury-particle,
  .luxury-glow-text,
  .luxury-border-glow,
  .luxury-loading-shimmer {
    animation: none;
  }
  
  .luxury-button::before,
  .luxury-input-field::before {
    transition: none;
  }
  
  * {
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for accessibility */
.luxury-input-field:focus-within {
  outline: 2px solid rgba(212, 194, 164, 0.5);
  outline-offset: 2px;
}

.luxury-button:focus {
  outline: 2px solid rgba(212, 194, 164, 0.5);
  outline-offset: 2px;
}

/* Custom scrollbar for luxury feel */
.luxury-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.luxury-scrollbar::-webkit-scrollbar-track {
  background: rgba(22, 25, 29, 0.3);
  border-radius: 4px;
}

.luxury-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, 
    rgba(212, 194, 164, 0.6) 0%, 
    rgba(212, 194, 164, 0.4) 100%);
  border-radius: 4px;
  border: 1px solid rgba(212, 194, 164, 0.2);
}

.luxury-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, 
    rgba(212, 194, 164, 0.8) 0%, 
    rgba(212, 194, 164, 0.6) 100%);
}

/* Premium Typography Effects */
.luxury-typography {
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* VIP Status Indicators */
.vip-indicator {
  position: relative;
}

.vip-indicator::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #D4C2A4 0%, #C4B294 100%);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(212, 194, 164, 0.6);
  animation: luxuryGlow 2s ease-in-out infinite;
}

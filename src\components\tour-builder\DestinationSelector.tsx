
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';

interface Destination {
  id: string;
  name: string;
  description: string;
  image: string;
  bestFor: string[];
  minDays: number;
}

interface DestinationSelectorProps {
  selectedDestinations: string[];
  onDestinationsChange: (destinations: string[]) => void;
  duration: number;
  onDurationChange: (duration: number) => void;
}

const DestinationSelector: React.FC<DestinationSelectorProps> = ({
  selectedDestinations,
  onDestinationsChange,
  duration,
  onDurationChange
}) => {
  const destinations: Destination[] = [
    {
      id: 'serengeti',
      name: 'Serengeti National Park',
      description: 'Witness the Great Migration and endless plains teeming with wildlife',
      image: 'photo-1472396961693-142e6e269027',
      bestFor: ['Great Migration', 'Big Five', 'Photography'],
      minDays: 2
    },
    {
      id: 'ngorongoro',
      name: 'Ngorongoro Crater',
      description: 'Explore the world\'s largest intact volcanic caldera',
      image: 'photo-1466721591366-2d5fba72006d',
      bestFor: ['Big Five', 'Rhino Spotting', 'Scenic Views'],
      minDays: 1
    },
    {
      id: 'tarangire',
      name: 'Tarangire National Park',
      description: 'Famous for large elephant herds and ancient baobab trees',
      image: 'photo-1493962853295-0fd70327578a',
      bestFor: ['Elephants', 'Baobab Trees', 'Bird Watching'],
      minDays: 1
    },
    {
      id: 'manyara',
      name: 'Lake Manyara',
      description: 'Known for tree-climbing lions and diverse ecosystems',
      image: 'photo-1485833077593-4278bba3f11f',
      bestFor: ['Tree-climbing Lions', 'Flamingos', 'Diverse Wildlife'],
      minDays: 1
    }
  ];

  const handleDestinationToggle = (destinationId: string) => {
    if (selectedDestinations.includes(destinationId)) {
      onDestinationsChange(selectedDestinations.filter(id => id !== destinationId));
    } else {
      onDestinationsChange([...selectedDestinations, destinationId]);
    }
  };

  return (
    <div className="space-y-6">
      {/* Duration Selector */}
      <Card>
        <CardHeader>
          <CardTitle>Tour Duration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Label>Duration: {duration} days</Label>
            <Slider
              value={[duration]}
              onValueChange={(value) => onDurationChange(value[0])}
              max={14}
              min={3}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-gray-500">
              <span>3 days</span>
              <span>14 days</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Destination Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {destinations.map((destination) => (
          <Card 
            key={destination.id} 
            className={`cursor-pointer transition-all ${
              selectedDestinations.includes(destination.id) 
                ? 'ring-2 ring-orange-500 bg-orange-50' 
                : 'hover:shadow-lg'
            }`}
            onClick={() => handleDestinationToggle(destination.id)}
          >
            <div className="relative">
              <img
                src={`https://images.unsplash.com/${destination.image}?auto=format&fit=crop&w=400&h=200`}
                alt={destination.name}
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <div className="absolute top-4 right-4">
                <Checkbox
                  checked={selectedDestinations.includes(destination.id)}
                  onChange={() => {}} // Handled by card click
                />
              </div>
            </div>
            
            <CardContent className="p-4">
              <h3 className="text-lg font-semibold mb-2">{destination.name}</h3>
              <p className="text-gray-600 text-sm mb-3">{destination.description}</p>
              
              <div className="space-y-2">
                <div className="text-sm text-gray-500">
                  Minimum {destination.minDays} day{destination.minDays > 1 ? 's' : ''}
                </div>
                <div className="flex flex-wrap gap-1">
                  {destination.bestFor.map((item, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {item}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedDestinations.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <h4 className="font-semibold mb-2">Selected Destinations</h4>
            <div className="flex flex-wrap gap-2">
              {selectedDestinations.map((id) => {
                const destination = destinations.find(d => d.id === id);
                return destination ? (
                  <Badge key={id} className="bg-orange-600">
                    {destination.name}
                  </Badge>
                ) : null;
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DestinationSelector;

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { FirebaseService } from '@/services/firebase';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import UserProfilePreferences from '@/components/user/UserProfilePreferences';
import ProfileCompletionBanner from '@/components/user/ProfileCompletionBanner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar, MapPin, Heart, User, Settings, FileText, RefreshCw } from 'lucide-react';
import WishlistButton from '@/components/features/WishlistButton';
import { useWishlist } from '@/contexts/WishlistContext';
import { Booking } from '@/types/firebase';

interface DashboardBooking {
  id: string;
  tourId: string;
  tourTitle: string;
  startDate: string;
  endDate: string;
  duration: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  image: string;
  totalPrice: number;
  guests: number;
  customerName: string;
}

const UserDashboard = () => {
  const { currentUser, userProfile, updateUserProfile } = useAuth();
  const { wishlist } = useWishlist();
  const [activeTab, setActiveTab] = useState('trips');
  const [userBookings, setUserBookings] = useState<DashboardBooking[]>([]);
  const [loading, setLoading] = useState(false);
  const [profileForm, setProfileForm] = useState({
    displayName: userProfile?.displayName || '',
    phone: userProfile?.phone || '',
    country: userProfile?.country || ''
  });

  useEffect(() => {
    if (currentUser) {
      fetchUserBookings();
    }
    if (userProfile) {
      setProfileForm({
        displayName: userProfile.displayName || '',
        phone: userProfile.phone || '',
        country: userProfile.country || ''
      });
    }
  }, [currentUser, userProfile]);

  const fetchUserBookings = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      const bookingsData = await FirebaseService.getUserBookings(currentUser.uid);

      // Transform Firebase booking data to dashboard format
      const transformedBookings: DashboardBooking[] = await Promise.all(
        bookingsData.map(async (booking: Booking) => {
          // Get tour details for image and additional info
          let tourImage = 'https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=400&h=200';
          let duration = 'Multi-day';

          try {
            if (booking.tourId && !booking.tourId.startsWith('custom-')) {
              const tour = await FirebaseService.getTour(booking.tourId);
              if (tour) {
                tourImage = tour.images?.[0] || tourImage;
                duration = tour.duration || duration;
              }
            }
          } catch (error) {
            // Could not fetch tour details for booking, using defaults
          }

          // Calculate duration from dates if not available
          if (booking.bookingDetails?.startDate && booking.bookingDetails?.endDate) {
            const start = new Date(booking.bookingDetails.startDate);
            const end = new Date(booking.bookingDetails.endDate);
            const diffTime = Math.abs(end.getTime() - start.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            if (diffDays > 0) {
              duration = `${diffDays} day${diffDays > 1 ? 's' : ''}`;
            }
          }

          return {
            id: booking.id,
            tourId: booking.tourId,
            tourTitle: booking.tourTitle,
            startDate: booking.bookingDetails?.startDate || '',
            endDate: booking.bookingDetails?.endDate || '',
            duration: duration,
            status: booking.status as 'confirmed' | 'pending' | 'cancelled',
            image: tourImage,
            totalPrice: booking.pricing?.totalAmount || 0,
            guests: (booking.bookingDetails?.participants || 1) + (booking.bookingDetails?.childrenCount || 0),
            customerName: `${booking.customerInfo?.firstName || ''} ${booking.customerInfo?.lastName || ''}`.trim()
          };
        })
      );

      setUserBookings(transformedBookings);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setUserBookings([]);
    } finally {
      setLoading(false);
    }
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await updateUserProfile(profileForm);
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const upcomingTrips = userBookings.filter(booking => {
    if (!booking.startDate) return false;
    const startDate = new Date(booking.startDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    return (booking.status === 'confirmed' || booking.status === 'pending') && startDate >= today;
  });

  const pastTrips = userBookings.filter(booking => {
    if (!booking.startDate) return false;
    const startDate = new Date(booking.startDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    return booking.status === 'confirmed' && startDate < today;
  });

  const pendingTrips = userBookings.filter(booking => booking.status === 'pending');
  const cancelledTrips = userBookings.filter(booking => booking.status === 'cancelled');

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-[#16191D] flex items-center justify-center">
        <div className="text-center p-8">
          <h2 className="text-2xl md:text-3xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6] mb-6">Please log in to access your dashboard</h2>
          <Button
            onClick={() => window.location.href = '/login'}
            className="bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 transition-all duration-300 font-['Open_Sans'] font-medium px-8 py-3"
          >
            Go to Login
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#16191D]">
      <Header />
      <main className="">
        <div className="bg-gradient-to-br from-[#16191D] via-[#1a1e23] to-[#16191D] relative overflow-hidden">
          {/* Luxury background overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#D4C2A4]/5 to-transparent pointer-events-none"></div>

          <div className="container mx-auto px-4 pt-24 pb-12 md:pt-28 md:pb-16 relative">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6] mb-3 md:mb-4 tracking-wide">
              Welcome back, {userProfile?.displayName}!
            </h1>
            <p className="text-lg md:text-xl font-['Open_Sans'] text-[#D4C2A4] opacity-90">
              Manage your safari adventures
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-6 md:py-8 bg-[#16191D] min-h-screen">
          <div className="max-w-6xl mx-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="overflow-x-auto mb-6">
                <TabsList className="grid w-full min-w-[400px] md:min-w-0 grid-cols-4 h-auto gap-2 bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-lg p-1">
                  <TabsTrigger
                    value="trips"
                    className="text-xs md:text-sm py-3 px-2 md:px-4 flex-col sm:flex-row bg-transparent data-[state=active]:bg-[#D4C2A4]/20 data-[state=active]:text-[#D4C2A4] text-[#A9A9A9] hover:text-[#F2EEE6] transition-all duration-300 rounded-md font-['Open_Sans']"
                  >
                    <Calendar className="h-3 w-3 md:h-4 md:w-4 mb-1 sm:mb-0 sm:mr-1 md:mr-2" />
                    <span>Trips</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="wishlist"
                    className="text-xs md:text-sm py-3 px-2 md:px-4 flex-col sm:flex-row bg-transparent data-[state=active]:bg-[#D4C2A4]/20 data-[state=active]:text-[#D4C2A4] text-[#A9A9A9] hover:text-[#F2EEE6] transition-all duration-300 rounded-md font-['Open_Sans']"
                  >
                    <Heart className="h-3 w-3 md:h-4 md:w-4 mb-1 sm:mb-0 sm:mr-1 md:mr-2" />
                    <span>Wishlist</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="profile"
                    className="text-xs md:text-sm py-3 px-2 md:px-4 flex-col sm:flex-row bg-transparent data-[state=active]:bg-[#D4C2A4]/20 data-[state=active]:text-[#D4C2A4] text-[#A9A9A9] hover:text-[#F2EEE6] transition-all duration-300 rounded-md font-['Open_Sans']"
                  >
                    <User className="h-3 w-3 md:h-4 md:w-4 mb-1 sm:mb-0 sm:mr-1 md:mr-2" />
                    <span>Profile</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="documents"
                    className="text-xs md:text-sm py-3 px-2 md:px-4 flex-col sm:flex-row bg-transparent data-[state=active]:bg-[#D4C2A4]/20 data-[state=active]:text-[#D4C2A4] text-[#A9A9A9] hover:text-[#F2EEE6] transition-all duration-300 rounded-md font-['Open_Sans']"
                  >
                    <FileText className="h-3 w-3 md:h-4 md:w-4 mb-1 sm:mb-0 sm:mr-1 md:mr-2" />
                    <span>Docs</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="trips" className="space-y-4 md:space-y-6">
                {/* Profile Completion Banner */}
                <ProfileCompletionBanner showDismiss={false} compact={false} />


                {/* All Bookings Summary */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                  <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 transition-all duration-300">
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl md:text-3xl font-['Cormorant_Garamond'] font-light text-[#D4C2A4] mb-1">{upcomingTrips.length}</div>
                      <div className="text-xs md:text-sm font-['Open_Sans'] text-[#A9A9A9]">Upcoming</div>
                    </CardContent>
                  </Card>
                  <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 transition-all duration-300">
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl md:text-3xl font-['Cormorant_Garamond'] font-light text-[#D4C2A4] mb-1">{pendingTrips.length}</div>
                      <div className="text-xs md:text-sm font-['Open_Sans'] text-[#A9A9A9]">Pending</div>
                    </CardContent>
                  </Card>
                  <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 transition-all duration-300">
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl md:text-3xl font-['Cormorant_Garamond'] font-light text-[#D4C2A4] mb-1">{pastTrips.length}</div>
                      <div className="text-xs md:text-sm font-['Open_Sans'] text-[#A9A9A9]">Completed</div>
                    </CardContent>
                  </Card>
                  <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 transition-all duration-300">
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl md:text-3xl font-['Cormorant_Garamond'] font-light text-[#D4C2A4] mb-1">{cancelledTrips.length}</div>
                      <div className="text-xs md:text-sm font-['Open_Sans'] text-[#A9A9A9]">Cancelled</div>
                    </CardContent>
                  </Card>
                </div>

                {/* Upcoming & Pending Trips */}
                <div>
                  <div className="flex justify-between items-center mb-4 md:mb-6">
                    <h2 className="text-xl md:text-2xl lg:text-3xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6]">
                      Upcoming & Pending Trips ({upcomingTrips.length + pendingTrips.length})
                    </h2>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchUserBookings}
                      disabled={loading}
                      className="flex items-center gap-2 bg-transparent border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transition-all duration-300 font-['Open_Sans']"
                    >
                      <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                  </div>
                  {loading ? (
                    <div className="text-center py-12">
                      <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-[#D4C2A4]" />
                      <p className="text-[#A9A9A9] font-['Open_Sans']">Loading your bookings...</p>
                    </div>
                  ) : (upcomingTrips.length > 0 || pendingTrips.length > 0) ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                      {[...upcomingTrips, ...pendingTrips].map((trip) => (
                        <Card key={trip.id} className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 transition-all duration-300 overflow-hidden group">
                          <div className="relative">
                            <img
                              src={trip.image}
                              alt={trip.tourTitle}
                              className="w-full h-40 md:h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                            <Badge
                              className={`absolute top-3 right-3 text-xs backdrop-blur-sm ${
                                trip.status === 'confirmed' ? 'bg-[#D4C2A4]/90 text-[#16191D]' :
                                trip.status === 'pending' ? 'bg-[#D4C2A4]/70 text-[#16191D]' : 'bg-[#A9A9A9]/70 text-[#F2EEE6]'
                              }`}
                            >
                              {trip.status}
                            </Badge>
                          </div>
                          <CardContent className="p-4 md:p-5">
                            <h3 className="font-['Cormorant_Garamond'] font-medium text-[#F2EEE6] mb-3 text-base md:text-lg line-clamp-2">{trip.tourTitle}</h3>
                            <div className="flex items-center text-xs md:text-sm text-[#A9A9A9] mb-2 font-['Open_Sans']">
                              <Calendar className="h-3 w-3 md:h-4 md:w-4 mr-2 text-[#D4C2A4]" />
                              <span className="truncate">{trip.startDate} • {trip.duration}</span>
                            </div>
                            <div className="flex items-center text-xs md:text-sm text-[#A9A9A9] mb-3 font-['Open_Sans']">
                              <User className="h-3 w-3 md:h-4 md:w-4 mr-2 text-[#D4C2A4]" />
                              <span>{trip.guests} guest{trip.guests > 1 ? 's' : ''}</span>
                            </div>
                            <p className="text-lg md:text-xl font-['Cormorant_Garamond'] font-medium text-[#D4C2A4] mb-4">${trip.totalPrice}</p>
                            <Button className="w-full bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 transition-all duration-300 font-['Open_Sans'] font-medium">
                              View Details
                            </Button>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <Card className="bg-[#1a1e23]/30 backdrop-blur-sm border border-[#D4C2A4]/20">
                      <CardContent className="p-8 md:p-12 text-center">
                        <Calendar className="h-12 w-12 md:h-16 md:w-16 mx-auto text-[#D4C2A4]/50 mb-6" />
                        <h3 className="text-lg md:text-xl font-['Cormorant_Garamond'] font-medium text-[#F2EEE6] mb-3">No upcoming trips</h3>
                        <p className="text-[#A9A9A9] mb-6 font-['Open_Sans']">Book your next safari adventure</p>
                        <Button
                          onClick={() => window.location.href = '/tours'}
                          className="bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 transition-all duration-300 font-['Open_Sans'] font-medium px-8 py-3"
                        >
                          Browse Tours
                        </Button>
                      </CardContent>
                    </Card>
                  )}
                </div>

                <div>
                  <h2 className="text-xl md:text-2xl lg:text-3xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6] mb-4 md:mb-6">Past Trips ({pastTrips.length})</h2>
                  {pastTrips.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                      {pastTrips.map((trip) => (
                        <Card key={trip.id} className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 transition-all duration-300 overflow-hidden group">
                          <div className="relative">
                            <img
                              src={trip.image}
                              alt={trip.tourTitle}
                              className="w-full h-40 md:h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                            <Badge className="absolute top-3 right-3 bg-[#D4C2A4]/90 text-[#16191D] text-xs backdrop-blur-sm">
                              {trip.status}
                            </Badge>
                          </div>
                          <CardContent className="p-4 md:p-5">
                            <h3 className="font-['Cormorant_Garamond'] font-medium text-[#F2EEE6] mb-3 text-base md:text-lg line-clamp-2">{trip.tourTitle}</h3>
                            <div className="flex items-center text-xs md:text-sm text-[#A9A9A9] mb-4 font-['Open_Sans']">
                              <Calendar className="h-3 w-3 md:h-4 md:w-4 mr-2 text-[#D4C2A4]" />
                              <span className="truncate">{trip.startDate} • {trip.duration}</span>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex-1 text-xs bg-transparent border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transition-all duration-300 font-['Open_Sans']"
                              >
                                Review
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex-1 text-xs bg-transparent border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transition-all duration-300 font-['Open_Sans']"
                              >
                                Rebook
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <Card className="bg-[#1a1e23]/30 backdrop-blur-sm border border-[#D4C2A4]/20">
                      <CardContent className="p-8 text-center">
                        <p className="text-[#A9A9A9] font-['Open_Sans']">No past trips yet</p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="wishlist" className="space-y-4 md:space-y-6">
                <h2 className="text-xl md:text-2xl lg:text-3xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6] mb-4 md:mb-6">My Wishlist ({wishlist.length})</h2>
                {wishlist.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    {wishlist.map((item) => (
                      <Card key={item.id} className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 transition-all duration-300 overflow-hidden group">
                        <div className="relative">
                          <img
                            src={`https://images.unsplash.com/${item.image}?auto=format&fit=crop&w=400&h=200`}
                            alt={item.title}
                            className="w-full h-40 md:h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                          <Badge className="absolute top-3 right-3 capitalize text-xs bg-[#D4C2A4]/90 text-[#16191D] backdrop-blur-sm">
                            {item.type}
                          </Badge>
                        </div>
                        <CardContent className="p-4 md:p-5">
                          <h3 className="font-['Cormorant_Garamond'] font-medium text-[#F2EEE6] mb-3 text-base md:text-lg line-clamp-2">{item.title}</h3>
                          <p className="text-lg md:text-xl font-['Cormorant_Garamond'] font-medium text-[#D4C2A4] mb-4">${item.price}</p>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              className="flex-1 text-xs bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 transition-all duration-300 font-['Open_Sans'] font-medium"
                            >
                              Book Now
                            </Button>
                            <WishlistButton item={item} size="sm" />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card className="bg-[#1a1e23]/30 backdrop-blur-sm border border-[#D4C2A4]/20">
                    <CardContent className="p-8 md:p-12 text-center">
                      <Heart className="h-12 w-12 md:h-16 md:w-16 mx-auto text-[#D4C2A4]/50 mb-6" />
                      <h3 className="text-lg md:text-xl font-['Cormorant_Garamond'] font-medium text-[#F2EEE6] mb-3">Your wishlist is empty</h3>
                      <p className="text-[#A9A9A9] mb-6 font-['Open_Sans']">Start adding tours and destinations you'd like to visit</p>
                      <Button
                        onClick={() => window.location.href = '/tours'}
                        className="bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 transition-all duration-300 font-['Open_Sans'] font-medium px-8 py-3"
                      >
                        Explore Tours
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="profile" className="space-y-6">
                {/* Basic Profile Information */}
                <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20">
                  <CardHeader>
                    <CardTitle className="text-xl md:text-2xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6]">Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleProfileUpdate} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="displayName" className="text-[#D4C2A4] font-['Open_Sans'] font-medium">Full Name</Label>
                          <Input
                            id="displayName"
                            value={profileForm.displayName}
                            onChange={(e) => setProfileForm({...profileForm, displayName: e.target.value})}
                            className="bg-[#16191D]/50 border-[#D4C2A4]/30 text-[#F2EEE6] focus:border-[#D4C2A4] transition-all duration-300 font-['Open_Sans']"
                          />
                        </div>
                        <div>
                          <Label htmlFor="email" className="text-[#D4C2A4] font-['Open_Sans'] font-medium">Email</Label>
                          <Input
                            id="email"
                            value={currentUser?.email || ''}
                            disabled
                            className="bg-[#16191D]/30 border-[#A9A9A9]/20 text-[#A9A9A9] font-['Open_Sans']"
                          />
                        </div>
                        <div>
                          <Label htmlFor="phone" className="text-[#D4C2A4] font-['Open_Sans'] font-medium">Phone</Label>
                          <Input
                            id="phone"
                            value={profileForm.phone}
                            onChange={(e) => setProfileForm({...profileForm, phone: e.target.value})}
                            className="bg-[#16191D]/50 border-[#D4C2A4]/30 text-[#F2EEE6] focus:border-[#D4C2A4] transition-all duration-300 font-['Open_Sans']"
                          />
                        </div>
                        <div>
                          <Label htmlFor="country" className="text-[#D4C2A4] font-['Open_Sans'] font-medium">Country</Label>
                          <Input
                            id="country"
                            value={profileForm.country}
                            onChange={(e) => setProfileForm({...profileForm, country: e.target.value})}
                            className="bg-[#16191D]/50 border-[#D4C2A4]/30 text-[#F2EEE6] focus:border-[#D4C2A4] transition-all duration-300 font-['Open_Sans']"
                          />
                        </div>
                      </div>
                      <Button
                        type="submit"
                        disabled={loading}
                        className="bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 transition-all duration-300 font-['Open_Sans'] font-medium px-8 py-3"
                      >
                        {loading ? 'Updating...' : 'Update Profile'}
                      </Button>
                    </form>
                  </CardContent>
                </Card>

                {/* Safari Preferences */}
                <UserProfilePreferences
                  onSave={() => {
                    // Optionally show a success message or refresh data
                  }}
                  showCompletionProgress={true}
                />
              </TabsContent>

              <TabsContent value="documents" className="space-y-6">
                <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20">
                  <CardHeader>
                    <CardTitle className="text-xl md:text-2xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6]">Travel Documents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="border border-[#D4C2A4]/20 rounded-lg p-4 md:p-6 bg-[#16191D]/30 hover:border-[#D4C2A4]/40 transition-all duration-300">
                        <h4 className="font-['Cormorant_Garamond'] font-medium text-[#F2EEE6] text-lg mb-2">Passport</h4>
                        <p className="text-sm text-[#A9A9A9] mb-4 font-['Open_Sans']">Upload your passport for visa processing</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-transparent border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transition-all duration-300 font-['Open_Sans']"
                        >
                          Upload Document
                        </Button>
                      </div>
                      <div className="border border-[#D4C2A4]/20 rounded-lg p-4 md:p-6 bg-[#16191D]/30 hover:border-[#D4C2A4]/40 transition-all duration-300">
                        <h4 className="font-['Cormorant_Garamond'] font-medium text-[#F2EEE6] text-lg mb-2">Travel Insurance</h4>
                        <p className="text-sm text-[#A9A9A9] mb-4 font-['Open_Sans']">Required for all safari tours</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-transparent border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transition-all duration-300 font-['Open_Sans']"
                        >
                          Upload Document
                        </Button>
                      </div>
                      <div className="border border-[#D4C2A4]/20 rounded-lg p-4 md:p-6 bg-[#16191D]/30 hover:border-[#D4C2A4]/40 transition-all duration-300">
                        <h4 className="font-['Cormorant_Garamond'] font-medium text-[#F2EEE6] text-lg mb-2">Medical Certificates</h4>
                        <p className="text-sm text-[#A9A9A9] mb-4 font-['Open_Sans']">Yellow fever vaccination required</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-transparent border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transition-all duration-300 font-['Open_Sans']"
                        >
                          Upload Document
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>


            </Tabs>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default UserDashboard;

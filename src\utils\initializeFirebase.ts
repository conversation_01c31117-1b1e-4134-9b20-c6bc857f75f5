
import { 
  collection, 
  doc, 
  setDoc, 
  addDoc, 
  Timestamp,
  writeBatch,
  getDocs 
} from 'firebase/firestore';
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { db, auth } from '@/lib/firebase';

export const initializeFirebaseCollections = async () => {
  console.log('🚀 Starting Firebase collections initialization...');
  
  try {
    if (!db) {
      throw new Error('Firebase database not initialized');
    }

    console.log('✅ Firebase connection established');

    const batch = writeBatch(db);
    let operationsCount = 0;

    // Sample data
    const sampleTours = [
      {
        title: "Serengeti Wildlife Safari",
        description: "Experience the Great Migration and witness the Big Five in their natural habitat.",
        price: 2500,
        duration: "7 days",
        location: "Serengeti National Park, Tanzania",
        destinations: ["Serengeti", "Ngorongoro Crater"],
        activities: ["Game Drives", "Wildlife Photography", "Cultural Visits"],
        accommodations: ["Safari Lodge", "Luxury Tented Camp"],
        maxGroupSize: 12,
        minGroupSize: 2,
        difficulty: "moderate",
        includes: ["Accommodation", "All Meals", "Park Fees", "Professional Guide"],
        excludes: ["International Flights", "Visa Fees", "Personal Expenses"],
        images: [
          "https://images.unsplash.com/photo-1516426122078-c23e76319801",
          "https://images.unsplash.com/photo-1547036967-23d11aacaee0"
        ],
        featured: true,
        status: "active",
        rating: 4.8,
        reviewCount: 124,
        tourType: "standard",
        category: "Wildlife Safari",
        accommodationLevel: "Luxury",
        seasonality: {
          greenSeason: true,
          drySeason: true,
          bestMonths: ["June", "July", "August", "September", "October"]
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ];

    const sampleDestinations = [
      {
        name: "Serengeti National Park",
        description: "World-famous for the Great Migration and abundant wildlife",
        country: "Tanzania",
        region: "Northern Tanzania",
        coordinates: { lat: -2.3333, lng: 34.8333 },
        bestTimeToVisit: ["June", "July", "August", "September", "October"],
        climate: "Tropical savanna climate with distinct wet and dry seasons",
        wildlife: [
          {
            species: "African Lion",
            scientificName: "Panthera leo",
            category: "big-five",
            abundance: "common",
            bestSpottingTime: "Early morning and late afternoon",
            behavior: "Pride hunting and territorial marking",
            conservationStatus: "Vulnerable",
            photographyTips: "Use telephoto lens and maintain safe distance"
          }
        ],
        images: [
          "https://images.unsplash.com/photo-1516426122078-c23e76319801"
        ],
        activities: ["Game Drives", "Hot Air Balloon Safari", "Walking Safari"],
        accommodations: ["Luxury Lodge", "Tented Camp", "Mobile Camp"],
        featured: true,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ];

    // Add tours
    for (const tour of sampleTours) {
      const tourRef = doc(collection(db, 'tours'));
      batch.set(tourRef, tour);
      operationsCount++;
    }

    // Add destinations
    for (const destination of sampleDestinations) {
      const destRef = doc(collection(db, 'destinations'));
      batch.set(destRef, destination);
      operationsCount++;
    }

    // Initialize empty collections
    const collections = [
      'bookings', 'reviews', 'activities', 'guides', 'travelGuides',
      'packingLists', 'wildlifeSightings', 'chatMessages', 'weatherData',
      'virtualTours', 'notifications', 'paymentTransactions', 'contentPages',
      'wishlists', 'tourPackages', 'tourAvailability', 'accommodations',
      'blogPosts', 'contactMessages', 'users'
    ];

    for (const collectionName of collections) {
      const collRef = doc(collection(db, collectionName));
      batch.set(collRef, {
        _placeholder: true,
        createdAt: Timestamp.now(),
        note: `Placeholder document for ${collectionName} collection`
      });
      operationsCount++;
    }

    await batch.commit();
    console.log(`✅ Successfully created ${operationsCount} documents across collections`);

    return { 
      success: true, 
      message: `Firebase collections initialized successfully! Created ${operationsCount} documents.` 
    };
    
  } catch (error) {
    console.error('❌ Error initializing Firebase collections:', error);
    throw error;
  }
};

export const createAdminUser = async (email: string, password: string, displayName: string) => {
  try {
    console.log('👤 Creating admin user...');
    
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    await updateProfile(user, { displayName });
    
    const adminProfile = {
      uid: user.uid,
      email: user.email,
      displayName,
      role: 'admin',
      phone: '+1234567890',
      country: 'Tanzania',
      profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e',
      preferences: {
        accommodation: 'luxury',
        activities: ['Game Drives', 'Photography', 'Cultural Tours'],
        dietaryRestrictions: [],
        fitnessLevel: 'high',
        photographyInterest: true,
        birdingInterest: true
      },
      loyaltyPoints: 5000,
      pastBookings: [],
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };
    
    await setDoc(doc(db, 'users', user.uid), adminProfile);
    
    console.log('✅ Admin user created successfully!');
    return { success: true, user, profile: adminProfile };
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    throw error;
  }
};

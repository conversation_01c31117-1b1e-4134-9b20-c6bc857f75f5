import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useAnimation } from 'framer-motion';
import { ChevronLeft, ChevronRight, Search, User, Play } from 'lucide-react';

// --- DATA ---
interface Destination {
  id: string;
  name: string;
  location: string;
  description: string;
  backgroundImage: string;
  cardImage: string;
}

const defaultDestinations: Destination[] = [
  {
    id: '1',
    name: 'SERENGETI NATIONAL PARK',
    location: 'Arusha - Tanzania',
    description: '<PERSON>ce<PERSON> malesuada, erat vitae suscipit accumsan, lacus ipsum accumsan est, et porttitor lorem justo vel nisl. Quisque vitae justo enim.',
    backgroundImage: 'https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/NEW%20SHIT/sere.png?transform=width=1920,height=1080,resize=cover',
    cardImage: 'https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/NEW%20SHIT/sere.png?transform=width=400,height=600,resize=cover' // Biker
  },
  {
    id: '2',
    name: 'NAGANO PREFECTURE',
    location: 'Japan Alps',
    description: 'Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus. Praesent commodo cursus magna.',
    backgroundImage: 'https://images.unsplash.com/photo-1545569341-9eb8b30979d9?w=1920&h=1080&fit=crop&q=80',
    cardImage: 'https://images.unsplash.com/photo-1545569341-9eb8b30979d9?w=400&h=600&fit=crop&q=80' // Monkey
  },
  {
    id: '3',
    name: 'MARRAKECH MERZOUGA',
    location: 'Sahara Desert - Morocco',
    description: 'Donec id elit non mi porta gravida at eget metus. Sed posuere consectetur est at lobortis. Vivamus sagittis lacus vel augue laoreet.',
    backgroundImage: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=1920&h=1080&fit=crop&q=80',
    cardImage: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=600&fit=crop&q=80' // Desert
  },
  {
    id: '4',
    name: 'YOSEMITE NATIONAL PARK',
    location: 'Sierra Nevada - United States',
    description: 'Nullam id dolor id nibh ultricies vehicula ut id elit. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Aenean eu leo quam.',
    backgroundImage: 'https://images.unsplash.com/photo-1518395689523-559c7f17b3a2?w=1920&h=1080&fit=crop&q=80',
    cardImage: 'https://images.unsplash.com/photo-1518395689523-559c7f17b3a2?w=400&h=600&fit=crop&q=80' // Yosemite cliff
  },
  {
    id: '5',
    name: 'LOS LANCES BEACH',
    location: 'Tarifa - Spain',
    description: 'Vestibulum id ligula porta felis euismod semper. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.',
    backgroundImage: 'https://images.unsplash.com/photo-1590523277543-a94d2e4eb00b?w=1920&h=1080&fit=crop&q=80',
    cardImage: 'https://images.unsplash.com/photo-1590523277543-a94d2e4eb00b?w=400&h=600&fit=crop&q=80' // Kitesurfing
  },
  {
    id: '6',
    name: 'GÖREME VALLEY',
    location: 'Cappadocia - Turkey',
    description: 'Pellentesque ornare sem lacinia quam venenatis vestibulum. Etiam porta sem malesuada magna mollis euismod. Curabitur blandit tempus porttitor.',
    backgroundImage: 'https://images.unsplash.com/photo-1564594738330-38ad49459f5f?w=1920&h=1080&fit=crop&q=80',
    cardImage: 'https://images.unsplash.com/photo-1564594738330-38ad49459f5f?w=400&h=600&fit=crop&q=80' // Hot air balloons
  }
];

// --- PROPS INTERFACE ---
interface DestinationCarouselProps {
  destinations?: Destination[];
  autoScrollInterval?: number;
}

// --- MAIN COMPONENT ---
const DestinationCarousel: React.FC<DestinationCarouselProps> = ({
  destinations = defaultDestinations,
  autoScrollInterval = 5000,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const progressBarControls = useAnimation();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleNavigate = (newIndex: number) => {
    if (isAnimating || newIndex === activeIndex) return;
    stopAutoScroll(); // Stop scroll immediately on user interaction
    setIsAnimating(true);
    setActiveIndex(newIndex);
  };

  const nextSlide = () => {
    const newIndex = (activeIndex + 1) % destinations.length;
    handleNavigate(newIndex);
  };

  const prevSlide = () => {
    const newIndex = (activeIndex - 1 + destinations.length) % destinations.length;
    handleNavigate(newIndex);
  };
  
  const startAutoScroll = async () => {
    stopAutoScroll();
    await progressBarControls.set({ scaleX: 0 });
    await progressBarControls.start({
      scaleX: 1,
      transition: { duration: autoScrollInterval / 1000, ease: 'linear' },
    });
    timeoutRef.current = setTimeout(nextSlide, 0); // Use 0 to run after animation completes
  };
  
  const stopAutoScroll = () => {
    progressBarControls.stop();
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  useEffect(() => {
    // onExitComplete in AnimatePresence handles restarting the animation
    if (!isAnimating) {
        startAutoScroll();
    }
    return () => stopAutoScroll();
  }, [activeIndex, isAnimating]);

  const currentDestination = destinations[activeIndex];
  const visibleCards = Array.from({ length: 4 }, (_, i) => {
    return destinations[(activeIndex + i + 1) % destinations.length];
  });
  
  const backgroundVariants = {
    initial: { opacity: 0, scale: 1.05 },
    animate: { opacity: 1, scale: 1, transition: { duration: 1, ease: [0.4, 0, 0.2, 1] } },
    exit: { opacity: 0, scale: 1.05, transition: { duration: 0.6, ease: [0.4, 0, 0.2, 1] } },
  };

  const textVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.8, ease: "easeInOut", delay: 0.3 } },
    exit: { opacity: 0, y: -20, transition: { duration: 0.4, ease: "easeInOut" } },
  };
  
  const cardTransition = { duration: 0.8, ease: [0.4, 0, 0.2, 1] };
  const cardVariants = {
    initial: { opacity: 0, x: 50, scale: 0.8 },
    animate: { opacity: 1, x: 0, scale: 1, transition: cardTransition },
    exit: { opacity: 0, x: -50, scale: 0.8, transition: cardTransition },
  };

  return (
    <div
        className="relative w-full h-screen overflow-hidden bg-gray-900 text-white font-['Bubblegum_Sans']"
        onMouseEnter={stopAutoScroll}
        onMouseLeave={() => { if (!isAnimating) startAutoScroll(); }}
    >
      <AnimatePresence initial={false} onExitComplete={() => setIsAnimating(false)}>
        <motion.div
          key={activeIndex}
          variants={backgroundVariants}
          initial="initial"
          animate="animate"
          exit="exit"
          className="absolute inset-0"
        >
          <img src={currentDestination.backgroundImage} alt={currentDestination.name} className="w-full h-full object-cover"/>
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/20 to-transparent" />
        </motion.div>
      </AnimatePresence>

      {/* Main content container - starts from top to allow floating header */}
      <div className="relative z-10 flex flex-col h-full pt-32 pb-8 px-8 md:px-12">
        {/* Hero content divided into two sections */}
        <div className="flex-grow flex flex-col lg:flex-row">
          {/* Left section: Text content */}
          <div className="flex-1 flex items-center lg:pr-8 xl:pr-12 mb-8 lg:mb-0">
            <div className="max-w-lg w-full">
              <AnimatePresence mode="wait">
                <motion.div key={activeIndex} variants={textVariants} initial="initial" animate="animate" exit="exit">
                    <p className="text-sm md:text-base font-light tracking-widest uppercase text-gray-300">{currentDestination.location}</p>
                    <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-black uppercase tracking-tighter leading-none mt-2 -ml-1 break-words font-['Bubblegum_Sans']">
                        {currentDestination.name}
                    </h1>
                    <p className="mt-4 text-gray-300 text-sm md:text-base leading-relaxed">
                        {currentDestination.description}
                    </p>
                   
                </motion.div>
              </AnimatePresence>
            </div>
          </div>

          {/* Right section: Cards - Hidden on mobile, visible on larger screens */}
          <div className="hidden lg:flex flex-shrink-0 items-end justify-end">
            <div className="flex items-end gap-3 xl:gap-4 h-[260px]">
              <AnimatePresence initial={false}>
                  {visibleCards.map((destination) => {
                      const destinationGlobalIndex = destinations.findIndex(d => d.id === destination.id);
                      return (
                          <motion.div
                              key={destination.id}
                              layout
                              variants={cardVariants}
                              initial="initial"
                              animate="animate"
                              exit="exit"
                              onClick={() => handleNavigate(destinationGlobalIndex)}
                              whileHover={{
                                  y: -10,
                                  scale: 1.05,
                                  transition: { duration: 0.3 }
                              }}
                              className="relative w-32 xl:w-36 h-52 xl:h-60 rounded-xl overflow-hidden cursor-pointer"
                              style={{
                                  transformStyle: 'preserve-3d',
                                  transform: 'perspective(800px) rotateY(-5deg)',
                                  boxShadow: `
                                      0 25px 50px -12px rgba(0, 0, 0, 0.6),
                                      0 20px 25px -5px rgba(0, 0, 0, 0.4),
                                      0 10px 10px -5px rgba(0, 0, 0, 0.3),
                                      0 0 0 1px rgba(255, 255, 255, 0.1),
                                      inset 0 1px 0 rgba(255, 255, 255, 0.2)
                                  `
                              }}
                          >
                              <img src={destination.cardImage} alt={destination.name} className="w-full h-full object-cover"/>
                              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                              <div className="absolute bottom-4 left-4 right-4 text-left">
                                  <h3 className="font-bold text-sm xl:text-base leading-tight uppercase">{destination.name.split(' ')[0]}</h3>
                                  <p className="text-xs text-gray-300">{destination.location.split(',')[0].split('-')[0]}</p>
                              </div>
                          </motion.div>
                      );
                  })}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Navigation controls at bottom */}
        <footer className="flex w-full items-center justify-between mt-auto">
            {/* Left Group: Controls */}
            <div className="flex-shrink-0 max-w-xs flex flex-col gap-4">
                <div className="flex items-center gap-3">
                    <button onClick={prevSlide} disabled={isAnimating} className="w-10 h-10 rounded-full border border-white/20 flex items-center justify-center backdrop-blur-sm hover:bg-white/10 transition-colors disabled:opacity-50">
                        <ChevronLeft size={20} />
                    </button>
                     <button onClick={nextSlide} disabled={isAnimating} className="w-10 h-10 rounded-full border border-white/20 flex items-center justify-center backdrop-blur-sm hover:bg-white/10 transition-colors disabled:opacity-50">
                        <ChevronRight size={20} />
                    </button>
                </div>
                <div className="w-full h-0.5 bg-white/20 rounded-full">
                    <motion.div className="h-full bg-yellow-400 rounded-full" style={{ transformOrigin: 'left' }} animate={progressBarControls}/>
                </div>
            </div>

            {/* Right Group: Counter */}
            <div className="flex-shrink-0 text-6xl font-thin tracking-tighter w-20 text-right overflow-hidden">
                <AnimatePresence mode="wait">
                    <motion.span
                        key={activeIndex}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut", delay: 0.3 } }}
                        exit={{ opacity: 0, y: -20, transition: { duration: 0.3, ease: "easeIn" } }}
                        className="inline-block"
                    >
                        {String(activeIndex + 1).padStart(2, '0')}
                    </motion.span>
                </AnimatePresence>
            </div>
        </footer>
      </div>
    </div>
  );
};

export default DestinationCarousel;
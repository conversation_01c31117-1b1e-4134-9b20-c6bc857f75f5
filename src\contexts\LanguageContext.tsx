
import React, { createContext, useContext, ReactNode } from 'react';

interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  t: (key: string) => string;
}

// Simplified to just return English text
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const language = 'en';
  const setLanguage = () => {}; // No-op function

  const t = (key: string): string => {
    // Return simplified English translations
    const translations: Record<string, string> = {
      'nav.tours': 'Tours',
      'nav.destinations': 'Destinations',
      'nav.about': 'About',
      'nav.contact': 'Contact',
      'hero.title': 'Discover the Magic of Tanzania',
      'hero.subtitle': 'Experience unforgettable safari adventures',
      'search.placeholder': 'Search tours, destinations...',
      'wishlist.add': 'Add to Wishlist',
      'wildlife.spotted': 'Recently Spotted',
      'weather.check': 'Check Weather',
      'sustainability.impact': 'Eco Impact'
    };
    
    return translations[key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

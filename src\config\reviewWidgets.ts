// Configuration for review widgets in the footer
export const reviewWidgetConfig = {
  google: {
    // Replace with your actual Google Business ID
    businessId: 'YOUR_GOOGLE_BUSINESS_ID',
    // Current rating (update regularly)
    rating: 4.9,
    // Number of reviews (update regularly)
    reviewCount: 150,
    // Sample recent review for preview
    sampleReview: {
      text: "Amazing safari experience! Professional guides and incredible wildlife viewing. Highly recommended!",
      author: "<PERSON>",
      rating: 5
    }
  },
  tripadvisor: {
    // Replace with your actual TripAdvisor profile URL
    profileUrl: 'https://www.tripadvisor.com/YOUR_TRIPADVISOR_PROFILE',
    // Current rating level
    rating: 'Excellent' as const,
    // Certificate year
    certificateYear: 2024,
    // Number of reviews (update regularly)
    reviewCount: 120,
    // Sample recent review for preview
    sampleReview: {
      text: "Outstanding service from start to finish. The wildlife encounters were beyond our expectations!",
      author: "<PERSON>.",
      rating: 5
    }
  }
};

// Instructions for updating the configuration:
// 1. Replace YOUR_GOOGLE_BUSINESS_ID with your actual Google Business Profile ID
// 2. Replace YOUR_TRIPADVISOR_PROFILE with your actual TripAdvisor business profile URL
// 3. Update ratings and review counts regularly to reflect current status
// 4. Update sample reviews with recent positive feedback
// 5. Ensure certificate year is current for TripAdvisor

export type TripAdvisorRating = 'Excellent' | 'Very Good' | 'Average' | 'Poor' | 'Terrible';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { useAuth } from '@/contexts/AuthContext';
import {
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  User,
  Heart,
  Activity,
  Mountain,
  Camera,
  Binoculars,
  Utensils,
  Sparkles,
  Crown,
  Star
} from 'lucide-react';

interface UserOnboardingProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
}

const UserOnboarding: React.FC<UserOnboardingProps> = ({ isOpen, onClose, onComplete }) => {
  const { userProfile, updateUserProfile } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    phone: userProfile?.phone || '',
    country: userProfile?.country || '',
    accommodation: userProfile?.preferences?.accommodation || 'midrange',
    activities: userProfile?.preferences?.activities || [],
    fitnessLevel: userProfile?.preferences?.fitnessLevel || 'moderate',
    photographyInterest: userProfile?.preferences?.photographyInterest || false,
    birdingInterest: userProfile?.preferences?.birdingInterest || false,
    dietaryRestrictions: userProfile?.preferences?.dietaryRestrictions || [],
  });

  const steps = [
    {
      id: 'welcome',
      title: 'Welcome to Safari Warriors!',
      icon: Sparkles,
      description: 'Let\'s set up your profile to provide personalized safari recommendations'
    },
    {
      id: 'basic-info',
      title: 'Basic Information',
      icon: User,
      description: 'Help us reach you and understand your location'
    },
    {
      id: 'accommodation',
      title: 'Accommodation Preference',
      icon: Heart,
      description: 'Choose your preferred safari accommodation style'
    },
    {
      id: 'activities',
      title: 'Activity Interests',
      icon: Activity,
      description: 'Select activities you\'re most excited about'
    },
    {
      id: 'fitness-interests',
      title: 'Fitness & Interests',
      icon: Mountain,
      description: 'Tell us about your fitness level and special interests'
    },
    {
      id: 'complete',
      title: 'All Set!',
      icon: CheckCircle,
      description: 'Your profile is ready for personalized recommendations'
    }
  ];

  const accommodationOptions = [
    {
      value: 'budget',
      label: 'Budget Safari',
      description: 'Comfortable camping and budget lodges',
      icon: '🏕️'
    },
    {
      value: 'midrange',
      label: 'Mid-Range Safari',
      description: 'Comfortable lodges and tented camps',
      icon: '🏨'
    },
    {
      value: 'luxury',
      label: 'Luxury Safari',
      description: 'Premium lodges with exceptional service',
      icon: '✨'
    }
  ];

  const activityOptions = [
    'Game Drives', 'Walking Safaris', 'Hot Air Balloon', 'Cultural Village Visits',
    'Night Drives', 'Photography Workshops', 'Conservation Activities', 'Bird Watching',
    'Bush Breakfast', 'Sundowner', 'Big Five Safari', 'Great Migration'
  ];

  const fitnessLevelOptions = [
    {
      value: 'low',
      label: 'Low Fitness',
      description: 'Prefer minimal walking, vehicle-based activities',
      icon: '🚗'
    },
    {
      value: 'moderate',
      label: 'Moderate Fitness',
      description: 'Comfortable with some walking and light activities',
      icon: '🚶'
    },
    {
      value: 'high',
      label: 'High Fitness',
      description: 'Enjoy challenging walks and active adventures',
      icon: '🥾'
    }
  ];

  const handleActivityToggle = (activity: string) => {
    setFormData(prev => ({
      ...prev,
      activities: prev.activities.includes(activity)
        ? prev.activities.filter(a => a !== activity)
        : [...prev.activities, activity]
    }));
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    setLoading(true);
    try {
      await updateUserProfile({
        phone: formData.phone,
        country: formData.country,
        preferences: {
          accommodation: formData.accommodation as any,
          activities: formData.activities,
          dietaryRestrictions: formData.dietaryRestrictions,
          fitnessLevel: formData.fitnessLevel as any,
          photographyInterest: formData.photographyInterest,
          birdingInterest: formData.birdingInterest,
        }
      });
      
      // Mark onboarding as completed in localStorage
      localStorage.setItem('onboarding_completed', 'true');
      
      onComplete();
      onClose();
    } catch (error) {
      console.error('Error completing onboarding:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    const step = steps[currentStep];

    switch (step.id) {
      case 'welcome':
        return (
          <div className="text-center space-y-8 py-8">
            <div className="relative flex justify-center">

              <div className="absolute -top-2 -right-2 w-8 h-8 bg-[#D4C2A4] rounded-full flex items-center justify-center">
                <Star className="h-4 w-4 text-[#16191D]" />
              </div>
            </div>
            <div className="space-y-6">
              <div>
                <h2 className="font-['Cormorant_Garamond'] text-5xl font-regular text-[#F2EEE6] mb-4 leading-tight">
                  Your Safari Journey
                  <span className="block font-['Cormorant_Garamond'] italic text-[#D4C2A4]">Begins Here</span>
                </h2>
                <p className="font-['Open Sans'] text-xl text-[#A9A9A9] leading-relaxed max-w-2xl mx-auto">
                  We're delighted to craft a personalized safari experience that matches your dreams.
                  Let's discover your preferences to curate the perfect adventure.
                </p>
              </div>
              <div className="bg-[#16191D]/95 backdrop-blur-md border border-[#A9A9A9]/20 p-8 rounded-sm shadow-2xl">
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none rounded-sm"></div>
                <div className="relative">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <Crown className="h-6 w-6 text-[#D4C2A4]" />
                    <span className="font-['Cormorant_Garamond'] text-2xl font-regular text-[#D4C2A4]">Premium Experience</span>
                    <Crown className="h-6 w-6 text-[#D4C2A4]" />
                  </div>
                  <p className="font-['Open Sans'] text-[#A9A9A9] leading-relaxed">
                    This personalized setup takes just 3-4 minutes and ensures every recommendation
                    is tailored to your unique preferences and travel style.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'basic-info':
        return (
          <div className="space-y-8 py-6">
            <div className="text-center space-y-4">

              <div>
                <h3 className="font-['Cormorant_Garamond'] text-3xl font-regular text-[#F2EEE6] mb-3">Personal Details</h3>
                <p className="font-['Open Sans'] text-lg text-[#A9A9A9] leading-relaxed max-w-lg mx-auto">
                  Help us connect with you and understand your location for personalized recommendations
                </p>
              </div>
            </div>
            <div className="max-w-md mx-auto space-y-6">
              <div className="space-y-2">
                <Label htmlFor="phone" className="font-['Open Sans'] text-base font-regular text-[#F2EEE6]">
                  Phone Number
                </Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="Enter your phone number"
                  className="h-12 font-['Open Sans'] text-base border-[#A9A9A9]/30 focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20 bg-[#16191D]/50 backdrop-blur-sm text-[#F2EEE6] placeholder:text-[#A9A9A9]"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="country" className="font-['Open Sans'] text-base font-regular text-[#F2EEE6]">
                  Country
                </Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                  placeholder="Enter your country"
                  className="h-12 font-['Open Sans'] text-base border-[#A9A9A9]/30 focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20 bg-[#16191D]/50 backdrop-blur-sm text-[#F2EEE6] placeholder:text-[#A9A9A9]"
                />
              </div>
            </div>
          </div>
        );

      case 'accommodation':
        return (
          <div className="space-y-8 py-6">
            <div className="text-center space-y-4">

              <div>
                <h3 className="font-['Cormorant_Garamond'] text-3xl font-regular text-[#F2EEE6] mb-3">Accommodation Style</h3>
                <p className="font-['Open Sans'] text-lg text-[#A9A9A9] leading-relaxed max-w-lg mx-auto">
                  Select your preferred safari accommodation to match your comfort and budget preferences
                </p>
              </div>
            </div>
            <RadioGroup
              value={formData.accommodation}
              onValueChange={(value) => setFormData(prev => ({ ...prev, accommodation: value as 'budget' | 'midrange' | 'luxury' }))}
              className="space-y-6 max-w-2xl mx-auto"
            >
              {accommodationOptions.map((option) => (
                <div key={option.value} className="group relative">
                  <div className="flex items-start space-x-4 p-6 border border-[#A9A9A9]/20 rounded-sm hover:border-[#D4C2A4]/40 hover:bg-[#D4C2A4]/10 transition-all duration-300 cursor-pointer shadow-sm hover:shadow-lg bg-[#16191D]/50 backdrop-blur-sm">
                    <RadioGroupItem value={option.value} id={option.value} className="mt-2 border-[#D4C2A4] text-[#D4C2A4]" />
                    <div className="flex-1">
                      <Label htmlFor={option.value} className="flex items-center gap-3 font-['Open Sans'] font-regular text-lg text-[#F2EEE6] cursor-pointer group-hover:text-[#D4C2A4] transition-colors">
                        <span className="text-2xl">{option.icon}</span>
                        {option.label}
                      </Label>
                      <p className="font-['Open Sans'] text-[#A9A9A9] mt-2 leading-relaxed">{option.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>
        );

      case 'activities':
        return (
          <div className="space-y-8 py-6">
            <div className="text-center space-y-4">

              <div>
                <h3 className="font-['Cormorant_Garamond'] text-3xl font-regular text-[#F2EEE6] mb-3">Safari Activities</h3>
                <p className="font-['Open Sans'] text-lg text-[#A9A9A9] leading-relaxed max-w-lg mx-auto">
                  Choose the activities that excite you most - select as many as you'd like to experience
                </p>
              </div>
            </div>
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-h-80 overflow-y-auto pr-2">
                {activityOptions.map((activity) => (
                  <div key={activity} className="group">
                    <div className="flex items-center space-x-3 p-4 border border-[#A9A9A9]/20 rounded-sm hover:border-[#D4C2A4]/40 hover:bg-[#D4C2A4]/10 transition-all duration-300 cursor-pointer bg-[#16191D]/50 backdrop-blur-sm">
                      <Checkbox
                        id={activity}
                        checked={formData.activities.includes(activity)}
                        onCheckedChange={() => handleActivityToggle(activity)}
                        className="border-[#D4C2A4] data-[state=checked]:bg-[#D4C2A4] data-[state=checked]:border-[#D4C2A4] data-[state=checked]:text-[#16191D]"
                      />
                      <Label htmlFor={activity} className="font-['Open Sans'] text-sm font-regular text-[#F2EEE6] cursor-pointer group-hover:text-[#D4C2A4] transition-colors leading-tight">
                        {activity}
                      </Label>
                    </div>
                  </div>
                ))}
              </div>
              {formData.activities.length > 0 && (
                <div className="mt-8 p-6 bg-[#16191D]/95 backdrop-blur-md border border-[#A9A9A9]/20 rounded-sm shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none rounded-sm"></div>
                  <div className="relative">
                    <p className="font-['Open Sans'] font-regular text-[#F2EEE6] mb-4">Your Selected Activities:</p>
                    <div className="flex flex-wrap gap-2">
                      {formData.activities.map((activity) => (
                        <Badge key={activity} className="bg-[#D4C2A4] text-[#16191D] font-['Open Sans'] px-3 py-1 text-sm hover:bg-[#D4C2A4]/90">
                          {activity}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        );

      case 'fitness-interests':
        return (
          <div className="space-y-8 py-6">
            <div className="text-center space-y-4">

              <div>
                <h3 className="font-['Cormorant_Garamond'] text-3xl font-regular text-[#F2EEE6] mb-3">Fitness & Interests</h3>
                <p className="font-['Open Sans'] text-lg text-[#A9A9A9] leading-relaxed max-w-lg mx-auto">
                  Help us recommend activities that match your fitness level and special interests
                </p>
              </div>
            </div>

            <div className="max-w-2xl mx-auto space-y-8">
              <div>
                <Label className="font-['Cormorant_Garamond'] text-2xl font-regular text-[#F2EEE6] mb-6 block text-center">Fitness Level</Label>
                <RadioGroup
                  value={formData.fitnessLevel}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, fitnessLevel: value as 'low' | 'moderate' | 'high' }))}
                  className="space-y-4"
                >
                  {fitnessLevelOptions.map((option) => (
                    <div key={option.value} className="group">
                      <div className="flex items-start space-x-4 p-5 border border-[#A9A9A9]/20 rounded-sm hover:border-[#D4C2A4]/40 hover:bg-[#D4C2A4]/10 transition-all duration-300 cursor-pointer bg-[#16191D]/50 backdrop-blur-sm">
                        <RadioGroupItem value={option.value} id={option.value} className="mt-2 border-[#D4C2A4] text-[#D4C2A4]" />
                        <div className="flex-1">
                          <Label htmlFor={option.value} className="flex items-center gap-3 font-['Open Sans'] font-regular text-lg text-[#F2EEE6] cursor-pointer group-hover:text-[#D4C2A4] transition-colors">
                            <span className="text-2xl">{option.icon}</span>
                            {option.label}
                          </Label>
                          <p className="font-['Open Sans'] text-[#A9A9A9] mt-2 leading-relaxed">{option.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div className="space-y-6">
                <Label className="font-['Cormorant_Garamond'] text-2xl font-regular text-[#F2EEE6] block text-center">Special Interests</Label>
                <div className="space-y-4">
                  <div className="group p-6 border border-[#A9A9A9]/20 rounded-sm hover:border-[#D4C2A4]/40 hover:bg-[#D4C2A4]/10 transition-all duration-300 bg-[#16191D]/50 backdrop-blur-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-[#D4C2A4] rounded-full flex items-center justify-center">
                          <Camera className="h-6 w-6 text-[#16191D]" />
                        </div>
                        <div>
                          <Label className="font-['Open Sans'] font-regular text-lg text-[#F2EEE6] group-hover:text-[#D4C2A4] transition-colors">Photography</Label>
                          <p className="font-['Open Sans'] text-[#A9A9A9] leading-relaxed">
                            Interested in photography workshops and wildlife photography
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={formData.photographyInterest}
                        onCheckedChange={(checked) =>
                          setFormData(prev => ({ ...prev, photographyInterest: checked }))
                        }
                        className="data-[state=checked]:bg-[#D4C2A4]"
                      />
                    </div>
                  </div>

                  <div className="group p-6 border border-[#A9A9A9]/20 rounded-sm hover:border-[#D4C2A4]/40 hover:bg-[#D4C2A4]/10 transition-all duration-300 bg-[#16191D]/50 backdrop-blur-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-[#D4C2A4] rounded-full flex items-center justify-center">
                          <Binoculars className="h-6 w-6 text-[#16191D]" />
                        </div>
                        <div>
                          <Label className="font-['Open Sans'] font-regular text-lg text-[#F2EEE6] group-hover:text-[#D4C2A4] transition-colors">Bird Watching</Label>
                          <p className="font-['Open Sans'] text-[#A9A9A9] leading-relaxed">
                            Interested in bird watching and specialized birding tours
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={formData.birdingInterest}
                        onCheckedChange={(checked) =>
                          setFormData(prev => ({ ...prev, birdingInterest: checked }))
                        }
                        className="data-[state=checked]:bg-[#D4C2A4]"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'complete':
        return (
          <div className="text-center space-y-8 py-8">
            <div className="relative flex justify-center">

              <div className="absolute -top-2 -right-2 w-8 h-8 bg-[#D4C2A4] rounded-full flex items-center justify-center animate-pulse">
                <Star className="h-4 w-4 text-[#16191D]" />
              </div>
            </div>
            <div className="space-y-6">
              <div>
                <h2 className="font-['Cormorant_Garamond'] text-5xl font-regular text-[#F2EEE6] mb-4 leading-tight">
                  Welcome to the
                  <span className="block font-['Cormorant_Garamond'] italic text-[#D4C2A4]">Safari Warriors Family!</span>
                </h2>
                <p className="font-['Open Sans'] text-xl text-[#A9A9A9] leading-relaxed max-w-2xl mx-auto">
                  Your profile is now complete and ready for personalized safari recommendations
                  crafted specifically for your preferences and travel style.
                </p>
              </div>
              <div className="bg-[#16191D]/95 backdrop-blur-md border border-[#A9A9A9]/20 p-8 rounded-sm shadow-2xl">
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none rounded-sm"></div>
                <div className="relative">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <Crown className="h-6 w-6 text-[#D4C2A4]" />
                    <span className="font-['Cormorant_Garamond'] text-2xl font-regular text-[#D4C2A4]">Your Journey Begins</span>
                    <Crown className="h-6 w-6 text-[#D4C2A4]" />
                  </div>
                  <p className="font-['Open Sans'] text-[#A9A9A9] leading-relaxed">
                    🎉 You'll now discover tours and experiences perfectly tailored to your unique preferences,
                    ensuring every moment of your safari adventure exceeds your expectations!
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto bg-[#16191D] border-0 shadow-2xl">
        {/* Dark Header */}
        <DialogHeader className="pb-8 border-b border-[#A9A9A9]/20">
          <div className="relative">
            <DialogTitle className="text-center space-y-4">
              <div className="flex items-center justify-center mb-4">
                <img
                  src="/logo.png"
                  alt="Safari Warriors Logo"
                  className="w-20 h-20 object-contain"
                />
              </div>
              <div>
                <h1 className="font-['Cormorant_Garamond'] text-4xl font-regular text-[#F2EEE6] mb-2">
                  Welcome to Safari Warriors
                </h1>
                <p className="font-['Open Sans'] text-[#A9A9A9] text-lg font-regular">
                  Curating Your Perfect Safari Experience
                </p>
              </div>
              <div className="flex items-center justify-center gap-4">
                <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent flex-1 max-w-24"></div>
                <Badge variant="outline" className="font-['Open Sans'] text-sm px-4 py-2 border-[#D4C2A4]/30 text-[#D4C2A4] bg-[#D4C2A4]/10">
                  Step {currentStep + 1} of {steps.length}
                </Badge>
                <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent flex-1 max-w-24"></div>
              </div>
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className="space-y-8 px-2 py-6">
          {/* Dark Progress Bar */}
          <div className="relative px-4">
            <div className="h-1 bg-[#A9A9A9]/20 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-[#D4C2A4] to-[#F2EEE6] transition-all duration-700 ease-out rounded-full shadow-sm"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <div className="flex justify-between mt-2">
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index <= currentStep
                      ? 'bg-gradient-to-br from-[#D4C2A4] to-[#F2EEE6] shadow-md'
                      : 'bg-[#A9A9A9]/30'
                  }`}
                ></div>
              ))}
            </div>
          </div>

          {/* Step Content */}
          <div className="px-4">
            {renderStepContent()}
          </div>

          {/* Dark Navigation */}
          <div className="flex justify-between items-center pt-6 border-t border-[#A9A9A9]/20 px-4">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
              className="flex items-center gap-3 px-8 py-3 font-['Open Sans'] font-regular border-[#D4C2A4]/30 text-[#F2EEE6] hover:bg-[#D4C2A4]/10 hover:text-[#D4C2A4] transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed bg-transparent"
            >
              <ChevronLeft className="h-5 w-5" />
              Previous
            </Button>

            {currentStep === steps.length - 1 ? (
              <Button
                onClick={handleComplete}
                disabled={loading}
                className="flex items-center gap-3 px-8 py-3 font-['Open Sans'] font-regular bg-[#D4C2A4] hover:bg-[#D4C2A4]/90 text-[#16191D] shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
              >
                {loading ? 'Saving...' : 'Complete Setup'}
                <CheckCircle className="h-5 w-5" />
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                className="flex items-center gap-3 px-8 py-3 font-['Open Sans'] font-regular bg-[#D4C2A4] hover:bg-[#D4C2A4]/90 text-[#16191D] shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Next
                <ChevronRight className="h-5 w-5" />
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserOnboarding;

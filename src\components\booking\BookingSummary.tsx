
import React from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { MapPin, Calendar, Users, Star } from 'lucide-react';
import { format } from 'date-fns';

interface BookingData {
  tourId: string;
  startDate: Date | null;
  groupSize: number;
  childrenCount: number;
  travelers: Array<{
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: Date | null;
    passportNumber: string;
    nationality: string;
  }>;
  accommodation: string;
  addOns: string[];
  specialRequests: string;
  totalPrice: number;
}

interface BookingSummaryProps {
  bookingData: BookingData;
  customTour?: any;
}

const BookingSummary: React.FC<BookingSummaryProps> = ({ bookingData, customTour }) => {
  const basePrice = customTour ? customTour.price : 2500;
  const accommodationPrices = {
    budget: 0,
    midrange: 150,
    luxury: 400,
  };
  
  const addOnPrices = {
    photography: 75,
    cultural: 50,
    balloon: 550,
    'night-drive': 100,
  };

  const accommodationPrice = accommodationPrices[bookingData.accommodation as keyof typeof accommodationPrices] || 0;
  const addOnsPrice = bookingData.addOns.reduce((total, addon) => 
    total + (addOnPrices[addon as keyof typeof addOnPrices] || 0), 0
  );
  
  // Calculate pricing for adults and children separately
  const adultPrice = (basePrice + accommodationPrice) * bookingData.groupSize;
  const childrenPrice = (basePrice + accommodationPrice) * (bookingData.childrenCount || 0) * 0.5; // 50% discount for children
  const calculatedTotal = adultPrice + childrenPrice + addOnsPrice;

  return (
    <Card className="sticky top-4">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Star className="mr-2 h-5 w-5 text-orange-600" />
          Booking Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Tour Info */}
        <div>
          <h3 className="font-semibold text-lg">
            {customTour ? (customTour.title || 'Custom Safari Tour') : 'Safari Adventure'}
          </h3>
          {customTour && (
            <div className="mt-2 space-y-1">
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="mr-1 h-4 w-4" />
                {customTour.destinations?.join(', ') || 'Multiple Destinations'}
              </div>
              <p className="text-sm text-gray-600">{customTour.duration}</p>
            </div>
          )}
        </div>

        <Separator />

        {/* Booking Details */}
        <div className="space-y-3">
          {bookingData.startDate && (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                <span className="text-sm">Start Date</span>
              </div>
              <span className="text-sm font-medium">
                {format(bookingData.startDate, 'MMM dd, yyyy')}
              </span>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Users className="mr-2 h-4 w-4 text-gray-500" />
              <span className="text-sm">Adults</span>
            </div>
            <span className="text-sm font-medium">{bookingData.groupSize} {bookingData.groupSize === 1 ? 'adult' : 'adults'}</span>
          </div>

          {bookingData.childrenCount > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Users className="mr-2 h-4 w-4 text-gray-500" />
                <span className="text-sm">Children</span>
              </div>
              <span className="text-sm font-medium">{bookingData.childrenCount} {bookingData.childrenCount === 1 ? 'child' : 'children'}</span>
            </div>
          )}

          {bookingData.accommodation && (
            <div className="flex items-center justify-between">
              <span className="text-sm">Accommodation</span>
              <Badge variant="outline" className="text-xs">
                {bookingData.accommodation}
              </Badge>
            </div>
          )}

          {bookingData.addOns.length > 0 && (
            <div>
              <span className="text-sm font-medium">Add-ons:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {bookingData.addOns.map((addon) => (
                  <Badge key={addon} variant="secondary" className="text-xs">
                    {addon}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Price Breakdown */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Base Price ({bookingData.groupSize}x)</span>
            <span>${(basePrice * bookingData.groupSize).toLocaleString()}</span>
          </div>
          
          {accommodationPrice > 0 && (
            <div className="flex justify-between text-sm">
              <span>Accommodation ({bookingData.groupSize}x)</span>
              <span>+${(accommodationPrice * bookingData.groupSize).toLocaleString()}</span>
            </div>
          )}
          
          {addOnsPrice > 0 && (
            <div className="flex justify-between text-sm">
              <span>Add-ons ({bookingData.groupSize}x)</span>
              <span>+${(addOnsPrice * bookingData.groupSize).toLocaleString()}</span>
            </div>
          )}
          
          <Separator />
          
          <div className="flex justify-between font-semibold text-lg">
            <span>Total</span>
            <span className="text-orange-600">${calculatedTotal.toLocaleString()}</span>
          </div>
        </div>

        {/* Custom Tour Benefits */}
        {customTour && (
          <div className="bg-green-50 p-3 rounded-lg border border-green-200">
            <div className="text-sm">
              <div className="font-semibold text-green-800 mb-1">Custom Tour Benefits</div>
              <ul className="text-green-700 text-xs space-y-1">
                <li>• Personalized itinerary</li>
                <li>• Flexible scheduling</li>
                <li>• Expert guide included</li>
                <li>• 24/7 support</li>
              </ul>
            </div>
          </div>
        )}

        {/* Booking Notes */}
        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
          <p className="font-medium mb-1">Included in all bookings:</p>
          <ul className="space-y-1">
            <li>• Professional safari guide</li>
            <li>• Game drives & park fees</li>
            <li>• Transportation in safari vehicle</li>
            <li>• Meals as specified</li>
            <li>• Drinking water during drives</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default BookingSummary;

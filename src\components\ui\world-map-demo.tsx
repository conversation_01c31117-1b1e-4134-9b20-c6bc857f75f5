import { WorldMap } from "@/components/ui/world-map";
import { motion } from "framer-motion";

export function WorldMapDemo() {
  return (
    <div className="py-20 md:py-40 dark:bg-black bg-white w-full px-4">
      <div className="max-w-7xl mx-auto text-center">
        <p className="font-bold text-lg sm:text-xl md:text-4xl dark:text-white text-black">
          Tanzania{" "}
          <span className="text-neutral-400">
            {"Safari Tours".split("").map((word, idx) => (
              <motion.span
                key={idx}
                className="inline-block"
                initial={{ x: -10, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: idx * 0.04 }}
              >
                {word}
              </motion.span>
            ))}
          </span>
        </p>
        <p className="text-xs sm:text-sm md:text-lg text-neutral-500 max-w-2xl mx-auto py-2 md:py-4 px-4">
          Experience the ultimate safari adventure in Tanzania. From the Great Migration
          to the Big Five, discover the most spectacular wildlife destinations in East Africa.
        </p>
      </div>
      <WorldMap
        dots={[
          {
            start: { lat: 40.7128, lng: -74.0060 }, // New York, USA
            end: { lat: -2.153389, lng: 34.6857 }, // Serengeti, Tanzania
          },
          {
            start: { lat: 51.5074, lng: -0.1278 }, // London, UK
            end: { lat: -2.153389, lng: 34.6857 }, // Serengeti, Tanzania
          },
          {
            start: { lat: 48.8566, lng: 2.3522 }, // Paris, France
            end: { lat: -2.153389, lng: 34.6857 }, // Serengeti, Tanzania
          },
          {
            start: { lat: 35.6762, lng: 139.6503 }, // Tokyo, Japan
            end: { lat: -2.153389, lng: 34.6857 }, // Serengeti, Tanzania
          },
          {
            start: { lat: -33.8688, lng: 151.2093 }, // Sydney, Australia
            end: { lat: -2.153389, lng: 34.6857 }, // Serengeti, Tanzania
          },
          {
            start: { lat: 55.7558, lng: 37.6176 }, // Moscow, Russia
            end: { lat: -2.153389, lng: 34.6857 }, // Serengeti, Tanzania
          },
        ]}
        lineColor="#FF6200"
      />
    </div>
  );
}


import { db, storage } from '@/lib/firebase';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  setDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import {
  ref,
  uploadBytes,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import { Tour, Review, BlogPost, Booking, ContactMessage, UserProfile, Destination, GalleryImage, CustomTourRequest } from '@/types/firebase';

export class FirebaseService {
  // Firebase Storage Methods
  static async uploadImage(
    file: File,
    folder: string = 'images',
    onProgress?: (progress: number) => void
  ): Promise<string> {
    try {
      // Create a unique filename
      const timestamp = Date.now();
      const fileName = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
      const storageRef = ref(storage, `${folder}/${fileName}`);

      // Upload with progress tracking
      const uploadTask = uploadBytesResumable(storageRef, file);

      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            // Progress tracking
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            if (onProgress) {
              onProgress(progress);
            }
          },
          (error) => {
            console.error('Upload error:', error);
            reject(error);
          },
          async () => {
            // Upload completed successfully
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              resolve(downloadURL);
            } catch (error) {
              reject(error);
            }
          }
        );
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  }

  static async uploadMultipleImages(
    files: File[],
    folder: string = 'images',
    onProgress?: (progress: number) => void
  ): Promise<string[]> {
    try {
      const uploadPromises = files.map((file, index) =>
        this.uploadImage(file, folder, (fileProgress) => {
          if (onProgress) {
            const totalProgress = ((index * 100) + fileProgress) / files.length;
            onProgress(totalProgress);
          }
        })
      );

      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('Error uploading multiple images:', error);
      throw error;
    }
  }

  static async deleteImageFromStorage(imageUrl: string): Promise<void> {
    try {
      // Extract the path from the Firebase Storage URL
      const url = new URL(imageUrl);
      const pathMatch = url.pathname.match(/\/o\/(.+)\?/);

      if (pathMatch) {
        const imagePath = decodeURIComponent(pathMatch[1]);
        const imageRef = ref(storage, imagePath);
        await deleteObject(imageRef);
      }
    } catch (error) {
      console.error('Error deleting image from storage:', error);
      // Don't throw error for delete operations to avoid breaking the flow
    }
  }

  static validateImageFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Please select a valid image file (JPEG, PNG, or WebP)'
      };
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'Image size must be less than 10MB'
      };
    }

    return { isValid: true };
  }

  // Gallery Images
  static async getGalleryImages() {
    try {
      const querySnapshot = await getDocs(collection(db, 'galleryImages'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting gallery images:', error);
      throw error;
    }
  }

  static async createGalleryImage(imageData: Omit<GalleryImage, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'galleryImages'), {
        ...imageData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating gallery image:', error);
      throw error;
    }
  }

  static async updateGalleryImage(id: string, imageData: Partial<GalleryImage>) {
    try {
      await updateDoc(doc(db, 'galleryImages', id), {
        ...imageData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating gallery image:', error);
      throw error;
    }
  }

  static async deleteGalleryImage(id: string) {
    try {
      await deleteDoc(doc(db, 'galleryImages', id));
    } catch (error) {
      console.error('Error deleting gallery image:', error);
      throw error;
    }
  }

  // Blog Posts
  static async getBlogPosts() {
    try {
      const querySnapshot = await getDocs(collection(db, 'blogPosts'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting blog posts:', error);
      throw error;
    }
  }

  static async createBlogPost(postData: Omit<BlogPost, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'blogPosts'), {
        ...postData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating blog post:', error);
      throw error;
    }
  }

  static async updateBlogPost(id: string, postData: Partial<BlogPost>) {
    try {
      await updateDoc(doc(db, 'blogPosts', id), {
        ...postData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating blog post:', error);
      throw error;
    }
  }

  static async deleteBlogPost(id: string) {
    try {
      await deleteDoc(doc(db, 'blogPosts', id));
    } catch (error) {
      console.error('Error deleting blog post:', error);
      throw error;
    }
  }

  // Reviews
  static async getAllReviews() {
    try {
      const querySnapshot = await getDocs(collection(db, 'reviews'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting all reviews:', error);
      throw error;
    }
  }

  static async getReviews(tourId: string) {
    try {
      const q = query(collection(db, 'reviews'), where('tourId', '==', tourId));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting reviews:', error);
      throw error;
    }
  }

  static async createReview(reviewData: Omit<Review, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'reviews'), reviewData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating review:', error);
      throw error;
    }
  }

  static async updateReview(id: string, reviewData: Partial<Review>) {
    try {
      await updateDoc(doc(db, 'reviews', id), {
        ...reviewData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating review:', error);
      throw error;
    }
  }

  static async deleteReview(id: string) {
    try {
      await deleteDoc(doc(db, 'reviews', id));
    } catch (error) {
      console.error('Error deleting review:', error);
      throw error;
    }
  }

  // Tours
  static async getTours() {
    try {
      const querySnapshot = await getDocs(collection(db, 'tours'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting tours:', error);
      throw error;
    }
  }

  static async getTour(id: string): Promise<Tour | null> {
    try {
      const docSnap = await getDoc(doc(db, 'tours', id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Tour;
      }
      return null;
    } catch (error) {
      console.error('Error getting tour:', error);
      throw error;
    }
  }

  static async createTour(tourData: Omit<Tour, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'tours'), tourData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating tour:', error);
      throw error;
    }
  }

  static async updateTour(id: string, tourData: Partial<Tour>) {
    try {
      await updateDoc(doc(db, 'tours', id), {
        ...tourData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating tour:', error);
      throw error;
    }
  }

  static async deleteTour(id: string) {
    try {
      await deleteDoc(doc(db, 'tours', id));
    } catch (error) {
      console.error('Error deleting tour:', error);
      throw error;
    }
  }

  static async searchTours(searchTerm: string, filters?: any) {
    try {
      let q = collection(db, 'tours');
      const querySnapshot = await getDocs(q);
      let results = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      // Apply search term filter
      if (searchTerm) {
        results = results.filter(tour => 
          tour.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tour.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tour.destinations?.some((dest: string) => dest.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }

      // Apply other filters
      if (filters) {
        if (filters.category) {
          results = results.filter(tour => tour.category === filters.category);
        }
        if (filters.destination) {
          results = results.filter(tour => 
            tour.destinations?.includes(filters.destination) || 
            tour.location?.includes(filters.destination)
          );
        }
        if (filters.duration) {
          results = results.filter(tour => tour.duration?.includes(filters.duration));
        }
        if (filters.priceRange) {
          results = results.filter(tour => 
            tour.price >= filters.priceRange.min && 
            tour.price <= filters.priceRange.max
          );
        }
      }

      return results;
    } catch (error) {
      console.error('Error searching tours:', error);
      throw error;
    }
  }

  // Users
  static async getAllUsers() {
    try {
      const querySnapshot = await getDocs(collection(db, 'users'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting users:', error);
      throw error;
    }
  }

  static async getUsers() {
    return this.getAllUsers();
  }

  static async getUserProfile(uid: string) {
    try {
      const docSnap = await getDoc(doc(db, 'users', uid));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      }
      return null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw error;
    }
  }

  static async createUserProfile(uid: string, profileData: Partial<UserProfile>) {
    try {
      const userProfile: UserProfile = {
        uid,
        email: profileData.email || '',
        displayName: profileData.displayName || '',
        role: profileData.role || 'user',
        preferences: profileData.preferences || {
          accommodation: 'midrange',
          activities: [],
          dietaryRestrictions: [],
          fitnessLevel: 'moderate',
          photographyInterest: false,
          birdingInterest: false
        },
        loyaltyPoints: profileData.loyaltyPoints || 0,
        pastBookings: profileData.pastBookings || [],
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        ...profileData
      };
      await setDoc(doc(db, 'users', uid), userProfile);
      return userProfile;
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  static async updateUserProfile(uid: string, profileData: Partial<UserProfile>) {
    try {
      // First check if the document exists
      const userDoc = await getDoc(doc(db, 'users', uid));

      if (!userDoc.exists()) {
        // If document doesn't exist, create it
        return await this.createUserProfile(uid, profileData);
      } else {
        // If document exists, update it
        await updateDoc(doc(db, 'users', uid), {
          ...profileData,
          updatedAt: Timestamp.now()
        });
      }
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  static async deleteUser(uid: string) {
    try {
      await deleteDoc(doc(db, 'users', uid));
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // Bookings
  static async getAllBookings() {
    try {
      const querySnapshot = await getDocs(collection(db, 'bookings'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting bookings:', error);
      throw error;
    }
  }

  static async getBookings(userId?: string) {
    if (userId) {
      return this.getUserBookings(userId);
    }
    return this.getAllBookings();
  }

  static async getUserBookings(userId: string) {
    try {
      const q = query(collection(db, 'bookings'), where('userId', '==', userId));
      const querySnapshot = await getDocs(q);
      const bookings = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      // Sort by creation date (newest first)
      return bookings.sort((a, b) => {
        const dateA = a.createdAt?.toDate?.() || new Date(0);
        const dateB = b.createdAt?.toDate?.() || new Date(0);
        return dateB.getTime() - dateA.getTime();
      });
    } catch (error) {
      console.error('Error getting user bookings:', error);
      throw error;
    }
  }

  static async getBooking(id: string): Promise<Booking | null> {
    try {
      const docSnap = await getDoc(doc(db, 'bookings', id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Booking;
      }
      return null;
    } catch (error) {
      console.error('Error getting booking:', error);
      throw error;
    }
  }

  static async createBooking(bookingData: Omit<Booking, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'bookings'), {
        ...bookingData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return { id: docRef.id, ...bookingData };
    } catch (error) {
      console.error('Error creating booking:', error);
      throw error;
    }
  }

  static async updateBooking(id: string, bookingData: Partial<Booking>) {
    try {
      await updateDoc(doc(db, 'bookings', id), {
        ...bookingData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating booking:', error);
      throw error;
    }
  }

  // Messages
  static async getAllMessages() {
    try {
      const querySnapshot = await getDocs(collection(db, 'messages'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting messages:', error);
      throw error;
    }
  }

  static async getContactMessages() {
    return this.getAllMessages();
  }

  static async createMessage(messageData: Omit<ContactMessage, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'messages'), messageData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating message:', error);
      throw error;
    }
  }

  static async createContactMessage(messageData: Omit<ContactMessage, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'messages'), {
        ...messageData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating contact message:', error);
      throw error;
    }
  }

  static async updateContactMessage(id: string, messageData: Partial<ContactMessage>) {
    try {
      await updateDoc(doc(db, 'messages', id), {
        ...messageData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating contact message:', error);
      throw error;
    }
  }



  // Destinations
  static async getDestinations() {
    try {
      const querySnapshot = await getDocs(collection(db, 'destinations'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting destinations:', error);
      throw error;
    }
  }

  static async getDestination(id: string) {
    try {
      const docSnap = await getDoc(doc(db, 'destinations', id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      }
      return null;
    } catch (error) {
      console.error('Error getting destination:', error);
      throw error;
    }
  }

  

  static async createDestination(destinationData: Omit<Destination, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'destinations'), {
        ...destinationData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating destination:', error);
      throw error;
    }
  }

  static async searchDestinations(searchTerm: string) {
    try {
      const querySnapshot = await getDocs(collection(db, 'destinations'));
      let results = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      if (searchTerm) {
        results = results.filter(destination =>
          destination.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          destination.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          destination.region?.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      return results;
    } catch (error) {
      console.error('Error searching destinations:', error);
      throw error;
    }
  }

  // Notifications
  static async createNotification(notificationData: any) {
    try {
      const docRef = await addDoc(collection(db, 'notifications'), notificationData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Payment Transactions
  static async createPaymentTransaction(transactionData: any) {
    try {
      const docRef = await addDoc(collection(db, 'paymentTransactions'), transactionData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating payment transaction:', error);
      throw error;
    }
  }

  // Custom Tour Requests
  static async createCustomTourRequest(tourRequestData: Omit<CustomTourRequest, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'customTourRequests'), {
        ...tourRequestData,
        status: 'pending',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating custom tour request:', error);
      throw error;
    }
  }

  static async getCustomTourRequests() {
    try {
      const querySnapshot = await getDocs(collection(db, 'customTourRequests'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting custom tour requests:', error);
      throw error;
    }
  }

  static async getCustomTourRequest(id: string) {
    try {
      const docSnap = await getDoc(doc(db, 'customTourRequests', id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      }
      return null;
    } catch (error) {
      console.error('Error getting custom tour request:', error);
      throw error;
    }
  }

  static async updateCustomTourRequest(id: string, updateData: Partial<CustomTourRequest>) {
    try {
      await updateDoc(doc(db, 'customTourRequests', id), {
        ...updateData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating custom tour request:', error);
      throw error;
    }
  }

  static async deleteCustomTourRequest(id: string) {
    try {
      await deleteDoc(doc(db, 'customTourRequests', id));
    } catch (error) {
      console.error('Error deleting custom tour request:', error);
      throw error;
    }
  }

  // Newsletter Subscriptions
  static async getNewsletterSubscriptions() {
    try {
      const querySnapshot = await getDocs(collection(db, 'newsletter_subscriptions'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting newsletter subscriptions:', error);
      throw error;
    }
  }

  static async addNewsletterSubscription(subscriptionData: any) {
    try {
      const docRef = await addDoc(collection(db, 'newsletter_subscriptions'), subscriptionData);
      return docRef.id;
    } catch (error) {
      console.error('Error adding newsletter subscription:', error);
      throw error;
    }
  }

  static async createNewsletterSubscription(email: string, source: string = 'website') {
    try {
      const docRef = await addDoc(collection(db, 'newsletter_subscriptions'), {
        email,
        source,
        status: 'active',
        subscribedAt: Timestamp.now(),
        createdAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating newsletter subscription:', error);
      throw error;
    }
  }

  static async updateNewsletterSubscription(id: string, data: any) {
    try {
      await updateDoc(doc(db, 'newsletter_subscriptions', id), {
        ...data,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating newsletter subscription:', error);
      throw error;
    }
  }

  static async deleteNewsletterSubscription(id: string) {
    try {
      await deleteDoc(doc(db, 'newsletter_subscriptions', id));
    } catch (error) {
      console.error('Error deleting newsletter subscription:', error);
      throw error;
    }
  }
}


import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Shield, FileText, Scale, Star } from 'lucide-react';

const Terms = () => {
  return (
    <div className="min-h-screen bg-[#16191D] overflow-x-hidden">
      <Header />

      {/* Luxury Hero Section - Fully Responsive */}
      <div className="relative overflow-hidden bg-[#16191D] pt-12 xs:pt-14 sm:pt-16 md:pt-20">
        {/* Elegant Background Pattern - Responsive */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                             radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
            backgroundSize: '30px 30px'
          }} />
        </div>

        {/* Hero Content - Mobile Optimized */}
        <div className="relative z-10 container mx-auto px-3 xs:px-4 sm:px-6 md:px-8 py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 xl:py-24 text-center">
          <div className="max-w-5xl mx-auto">
            {/* Luxury Badge - Mobile Responsive */}
            <div className="inline-flex items-center gap-1 xs:gap-1.5 sm:gap-2 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-2 xs:px-3 sm:px-4 md:px-6 py-1 xs:py-1.5 sm:py-2 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
              <FileText className="w-2.5 h-2.5 xs:w-3 xs:h-3 sm:w-4 sm:h-4 text-[#D4C2A4]" />
              <span className="font-open-sans text-2xs xs:text-xs sm:text-sm text-[#D4C2A4] tracking-wider uppercase">Legal Framework</span>
            </div>

            {/* Main Title - Highly Responsive */}
            <h1 className="font-cormorant text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl text-[#F2EEE6] mb-3 xs:mb-4 sm:mb-5 md:mb-6 leading-tight">
              Terms &
              <span className="block text-[#D4C2A4] italic">Conditions</span>
            </h1>

            {/* Description - Mobile Optimized */}
            <p className="font-open-sans text-[#F2EEE6]/70 text-xs xs:text-sm sm:text-base md:text-lg max-w-sm xs:max-w-md sm:max-w-xl md:max-w-2xl mx-auto leading-relaxed px-2 xs:px-3 sm:px-4">
              Our comprehensive terms ensure transparency, protection, and exceptional service for your safari experience.
            </p>

            {/* Decorative Element - Responsive */}
            <div className="mt-6 xs:mt-7 sm:mt-8 md:mt-10 lg:mt-12 flex justify-center">
              <div className="w-12 xs:w-14 sm:w-16 md:w-20 lg:w-24 h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Luxury Terms Content - Fully Responsive */}
      <div className="relative bg-[#16191D] py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 xl:py-24">
        {/* Subtle Background Pattern - Mobile Optimized */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px)`,
            backgroundSize: '40px 40px'
          }}></div>
        </div>

        <div className="relative z-10 container mx-auto px-3 xs:px-4 sm:px-6 md:px-8">
          <div className="max-w-5xl mx-auto space-y-8 xs:space-y-10 sm:space-y-12 md:space-y-16 lg:space-y-20">

            {/* Booking Terms Section - Mobile Responsive */}
            <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              {/* Section Header - Mobile Optimized */}
              <div className="flex items-center gap-2 xs:gap-3 sm:gap-4 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
                <div className="bg-[#D4C2A4]/20 p-1.5 xs:p-2 sm:p-3 rounded-md xs:rounded-lg sm:rounded-xl flex-shrink-0">
                  <Shield className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-[#D4C2A4]" />
                </div>
                <h2 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-[#F2EEE6] leading-tight">
                  Booking Terms
                </h2>
              </div>

              {/* Content - Mobile Responsive */}
              <div className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6">
                <div className="relative">
                  <span className="font-cormorant float-left text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl leading-none pt-0.5 xs:pt-1 pr-2 xs:pr-3 sm:pr-4 text-[#D4C2A4] opacity-80">
                    A
                  </span>
                  <p className="font-open-sans text-[#F2EEE6]/90 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                    ll bookings with Warrior of Africa Safari Tours require a deposit to secure your reservation and guarantee your spot on our exclusive safari adventures. Full payment is due 30 days before departure to ensure all arrangements are finalized. We accept major credit cards, bank transfers, and other secure payment methods for your convenience. Please note that prices are subject to change until your booking is confirmed and payment is received. Currency exchange rate fluctuations may affect final pricing for international clients, and we will notify you of any changes before processing your payment.
                  </p>
                </div>

                <div className="flex justify-center my-4 xs:my-5 sm:my-6 md:my-8">
                  <div className="w-16 xs:w-20 sm:w-24 md:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
                </div>

                <p className="font-open-sans text-[#F2EEE6]/80 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                  By booking with Warrior of Africa Safari Tours, you agree to these terms and conditions which govern your safari experience with us. These terms are designed to ensure a smooth and enjoyable experience for all our guests while protecting both parties' interests. We recommend reading these terms carefully before making your booking. Our team is available to answer any questions you may have about these terms or your upcoming safari adventure. We are committed to providing you with an unforgettable African safari experience while maintaining the highest standards of service and safety.
                </p>
              </div>
            </div>
            {/* Cancellation Policy Section - Mobile Responsive */}
            <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              {/* Section Header - Mobile Optimized */}
              <div className="flex items-center gap-2 xs:gap-3 sm:gap-4 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
                <div className="bg-[#D4C2A4]/20 p-1.5 xs:p-2 sm:p-3 rounded-md xs:rounded-lg sm:rounded-xl flex-shrink-0">
                  <FileText className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-[#D4C2A4]" />
                </div>
                <h2 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-[#F2EEE6] leading-tight">
                  Cancellation Policy
                </h2>
              </div>

              {/* Content - Mobile Responsive */}
              <div className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6">
                <div className="relative">
                  <span className="font-cormorant float-left text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl leading-none pt-0.5 xs:pt-1 pr-2 xs:pr-3 sm:pr-4 text-[#D4C2A4] opacity-80">
                    C
                  </span>
                  <p className="font-open-sans text-[#F2EEE6]/90 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                    ancellations made 60 or more days before departure incur a 10% penalty fee. Cancellations made 30-59 days before departure incur a 50% penalty. Cancellations made 15-29 days before departure incur a 75% penalty, and cancellations made less than 15 days before departure result in 100% penalty with no refund. We strongly recommend comprehensive travel insurance to protect against unforeseen circumstances such as medical emergencies, family emergencies, or other situations beyond your control. Cancellations due to government travel restrictions or safety concerns may qualify for full refund at our discretion.
                  </p>
                </div>

                <div className="flex justify-center my-4 xs:my-5 sm:my-6 md:my-8">
                  <div className="w-16 xs:w-20 sm:w-24 md:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
                </div>

                <p className="font-open-sans text-[#F2EEE6]/80 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                  Warrior of Africa Safari Tours acts as an agent for various service providers including lodges, transportation companies, and activity operators. While we carefully select our partners and maintain high standards, we are not liable for their actions, omissions, or any incidents that may occur during your safari. Comprehensive travel insurance is strongly recommended and may be required for certain activities. All participants engage in safari activities at their own risk and must follow safety guidelines provided by our experienced guides. We reserve the right to modify itineraries due to weather, wildlife movements, or other circumstances beyond our control.
                </p>
              </div>
            </div>

            {/* Liability & Law Section - Mobile Responsive */}
            <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              {/* Section Header - Mobile Optimized */}
              <div className="flex items-center gap-2 xs:gap-3 sm:gap-4 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
                <div className="bg-[#D4C2A4]/20 p-1.5 xs:p-2 sm:p-3 rounded-md xs:rounded-lg sm:rounded-xl flex-shrink-0">
                  <Scale className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-[#D4C2A4]" />
                </div>
                <h2 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-[#F2EEE6] leading-tight">
                  Liability & Law
                </h2>
              </div>

              {/* Content - Mobile Responsive */}
              <div className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6">
                <div className="relative">
                  <span className="font-cormorant float-left text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl leading-none pt-0.5 xs:pt-1 pr-2 xs:pr-3 sm:pr-4 text-[#D4C2A4] opacity-80">
                    T
                  </span>
                  <p className="font-open-sans text-[#F2EEE6]/90 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                    hese terms and conditions are governed by the laws of Tanzania, and any disputes arising from your safari booking or experience will be resolved through binding arbitration in Arusha, Tanzania. By booking with us, you acknowledge that wildlife viewing is subject to natural conditions and animal behavior, and specific wildlife sightings cannot be guaranteed. Weather conditions, seasonal variations, and natural phenomena may affect planned itineraries, and we reserve the right to make necessary adjustments to ensure your safety and the best possible experience. All participants must have valid passports with at least six months validity and appropriate visas for entry into Tanzania and any other countries visited during the safari.
                  </p>
                </div>

                <div className="flex justify-center my-4 xs:my-5 sm:my-6 md:my-8">
                  <div className="w-16 xs:w-20 sm:w-24 md:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
                </div>

                <p className="font-open-sans text-[#F2EEE6]/80 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                  If you have any questions about these Terms and Conditions or need clarification on any aspect of your safari booking, please contact <NAME_EMAIL> or through our website contact form. We are committed to providing clear and transparent terms that protect both our guests and our business while ensuring an exceptional safari experience. These terms may be updated from time to time to reflect changes in our services, legal requirements, or industry standards. We will notify you of any material changes and the updated terms will be posted on our website with a new effective date. Your continued use of our services after any changes indicates your acceptance of the updated terms.
                </p>
              </div>
            </div>

            {/* Premium Contact Section - Fully Mobile Responsive */}
            <div className="bg-gradient-to-br from-[#D4C2A4]/10 to-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/30 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              <div className="text-center">
                <Star className="w-6 h-6 xs:w-7 xs:h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 text-[#D4C2A4] mx-auto mb-3 xs:mb-4 sm:mb-5 md:mb-6" />
                <h3 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl text-[#F2EEE6] mb-2 xs:mb-3 sm:mb-4 leading-tight">
                  Questions About Our Terms?
                </h3>
                <p className="font-open-sans text-[#F2EEE6]/70 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed max-w-sm xs:max-w-md sm:max-w-lg md:max-w-xl lg:max-w-2xl mx-auto mb-4 xs:mb-5 sm:mb-6 md:mb-8 px-2">
                  Our legal team is available to clarify any aspect of these terms and ensure you have complete understanding before your safari adventure.
                </p>
                <div className="inline-flex items-center gap-1.5 xs:gap-2 bg-[#D4C2A4]/20 backdrop-blur-sm border border-[#D4C2A4]/30 rounded-full px-3 xs:px-4 sm:px-5 md:px-6 py-1.5 xs:py-2 sm:py-2.5 md:py-3">
                  <span className="font-open-sans text-[#D4C2A4] text-xs xs:text-sm sm:text-base font-medium break-all xs:break-normal">
                    <EMAIL>
                  </span>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <Footer isDarkBackground={true} />
    </div>
  );
};

export default Terms;



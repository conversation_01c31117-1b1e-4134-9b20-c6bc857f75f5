import React, { useLayoutEffect, useRef, useState, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { GalleryImage } from '@/types/firebase';

// Register the GSAP plugin
gsap.registerPlugin(ScrollTrigger);

interface ScrollingImageGalleryProps {
  images: GalleryImage[];
  onImageClick?: (image: GalleryImage) => void;
}

const ScrollingImageGallery: React.FC<ScrollingImageGalleryProps> = ({
  images,
  onImageClick
}) => {
  const mainRef = useRef<HTMLDivElement>(null);
  const galleryRef = useRef<HTMLDivElement>(null);
  const colRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [isMobile, setIsMobile] = useState(false);

  // Responsive hook
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Organize images into columns (responsive: 2 on mobile, 3 on desktop)
  const organizeImagesIntoColumns = () => {
    const columnCount = isMobile ? 2 : 3;
    const columnArrays: GalleryImage[][] = Array(columnCount).fill(null).map(() => []);

    images.forEach((image, index) => {
      const columnIndex = index % columnCount;
      columnArrays[columnIndex].push(image);
    });

    return columnArrays;
  };

  const columnsData = organizeImagesIntoColumns();

  useLayoutEffect(() => {
    if (!images.length) return;

    // We use a GSAP context for safe cleanup
    const ctx = gsap.context(() => {
      const cols = colRefs.current.filter(col => col !== null);

      cols.forEach((col, i) => {
        if (!col) return;

        // We use the direct child divs of the column as our items
        const imageElements = gsap.utils.toArray('.scrolling-image', col) as HTMLElement[];

        imageElements.forEach((imageElement: HTMLElement) => {
          const columnHeight = col.clientHeight;
          const direction = i % 2 !== 0 ? "+=" : "-=";

          gsap.to(imageElement, {
            y: direction + Number(columnHeight / 2),
            duration: 20,
            repeat: -1,
            ease: "none",
            modifiers: {
              y: gsap.utils.unitize((y) => {
                if (direction === "+=") {
                  return parseFloat(y) % (columnHeight * 0.5);
                } else {
                  return parseFloat(y) % -Number(columnHeight * 0.5);
                }
              })
            }
          });
        });
      });
    }, mainRef); // Scope the context to our main component element

    // Cleanup function to kill all animations and ScrollTriggers
    return () => ctx.revert();
  }, [images, isMobile]);

  if (!images.length) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#16191D]">
        <div className="text-center">
          <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 bg-[#D4C2A4]/20 backdrop-blur-sm rounded-full flex items-center justify-center">
            <span className="text-[#D4C2A4] text-2xl sm:text-3xl">📸</span>
          </div>
          <p className="text-[#F2EEE6]/70 font-open-sans text-sm sm:text-base">No images to display</p>
        </div>
      </div>
    );
  }

  return (
    <div ref={mainRef} className="relative bg-[#16191D] overflow-hidden h-[70vh] sm:h-[80vh] md:h-[90vh] rounded-xl sm:rounded-2xl shadow-2xl border border-[#D4C2A4]/20">
      {/* Elegant Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                           radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
          backgroundSize: '40px 40px'
        }} />
      </div>

      {/* Gallery container */}
      <div
        ref={galleryRef}
        className="
          gallery absolute inset-0
          w-full h-full
          flex justify-center
          px-2 sm:px-4
        "
      >
        {columnsData.map((columnImages, colIndex) => (
          <div
            key={colIndex}
            ref={el => colRefs.current[colIndex] = el}
            className={`
              col flex flex-1 flex-col w-full
              ${colIndex === 1 ? 'self-end' : 'self-start'}
            `}
          >
            {/* We render the images twice for a seamless loop */}
            {[...columnImages, ...columnImages].map((image, imgIndex) => (
              <div
                key={`${image.id}-${imgIndex}`}
                className="scrolling-image w-full p-2 sm:p-3 group cursor-pointer"
                onClick={() => onImageClick?.(image)}
              >
                <div className="relative overflow-hidden bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 shadow-lg hover:shadow-2xl transition-all duration-500 group-hover:-translate-y-2 rounded-lg sm:rounded-xl">
                  <img
                    src={image.url}
                    alt={image.title}
                    className="w-full h-auto object-cover saturate-75 group-hover:saturate-100 transition-all duration-700 group-hover:scale-110 rounded-lg sm:rounded-xl"
                    loading="lazy"
                  />

                  {/* Luxury overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg sm:rounded-xl"></div>

                  {/* Elegant content overlay */}
                  <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 text-[#F2EEE6] transform translate-y-full group-hover:translate-y-0 transition-transform duration-500">
                    <h3 className="font-cormorant text-base sm:text-lg font-semibold mb-1 truncate">{image.title}</h3>
                    <p className="font-open-sans text-xs opacity-90 truncate mb-2">{image.location}</p>
                    <div className="flex items-center justify-between">
                      <span className="font-open-sans text-xs bg-[#D4C2A4]/30 backdrop-blur-sm text-[#F2EEE6] px-2 py-1 rounded-full border border-[#D4C2A4]/40">
                        {image.category}
                      </span>
                      <div className="w-5 h-5 sm:w-6 sm:h-6 border border-[#D4C2A4]/60 rounded-full flex items-center justify-center backdrop-blur-sm">
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full"></div>
                      </div>
                    </div>
                  </div>

                  {/* Decorative corner elements */}
                  <div className="absolute top-2 left-2 w-3 h-3 sm:w-4 sm:h-4 border-l-2 border-t-2 border-[#D4C2A4]/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute top-2 right-2 w-3 h-3 sm:w-4 sm:h-4 border-r-2 border-t-2 border-[#D4C2A4]/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute bottom-2 left-2 w-3 h-3 sm:w-4 sm:h-4 border-l-2 border-b-2 border-[#D4C2A4]/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute bottom-2 right-2 w-3 h-3 sm:w-4 sm:h-4 border-r-2 border-b-2 border-[#D4C2A4]/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Luxury glow effect */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#D4C2A4]/10 via-transparent to-[#D4C2A4]/10 rounded-lg sm:rounded-xl"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ScrollingImageGallery;

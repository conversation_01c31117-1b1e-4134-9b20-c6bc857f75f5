import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { LiquidButton } from '@/components/ui/liquid-glass-button';
import { MessageCircle } from 'lucide-react';
import ChatBot from './ChatBot';

const ChatBotWidget = () => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      {!isOpen && (
        <LiquidButton
          onClick={toggleChat}
          size="icon"
          variant="default"
          className="fixed z-[9998] rounded-full transition-all duration-300 hover:scale-110 touch-target
            w-14 h-14
            bottom-4 right-4 lg:bottom-6 lg:right-6
            flex items-center justify-center bg-transparent hover:bg-transparent text-black dark:text-white shadow-xl"
        >
          <MessageCircle className="h-6 w-6 drop-shadow-lg" />
        </LiquidButton>
      )}

      <ChatBot isOpen={isOpen} onToggle={toggleChat} />
    </>
  );
};

export default ChatBotWidget;

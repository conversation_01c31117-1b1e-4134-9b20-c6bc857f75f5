import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Loader2, AlertTriangle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { FirebaseService } from '@/services/firebase';
import { Tour } from '@/types/firebase';
import { gsap } from 'gsap';
import { useGSAP } from '@gsap/react';
import PageLoader from '@/components/ui/PageLoader';

// Luxury VVIP Scrollbar Styles
const luxuryScrollbarStyles = `
  .luxury-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  .luxury-scrollbar::-webkit-scrollbar-track {
    background: rgba(22, 25, 29, 0.3);
    border-radius: 3px;
  }
  .luxury-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
      rgba(212, 194, 164, 0.6) 0%,
      rgba(212, 194, 164, 0.4) 100%);
    border-radius: 3px;
    border: 1px solid rgba(212, 194, 164, 0.2);
  }
  .luxury-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
      rgba(212, 194, 164, 0.8) 0%,
      rgba(212, 194, 164, 0.6) 100%);
  }
`;

// Premium VVIP Luxury Styles
const luxuryStyles = `
  /* VIP Glow Effects */
  @keyframes luxuryGlow {
    0%, 100% {
      text-shadow:
        0 0 10px rgba(212, 194, 164, 0.4),
        0 0 20px rgba(212, 194, 164, 0.2),
        0 0 30px rgba(212, 194, 164, 0.1);
    }
    50% {
      text-shadow:
        0 0 15px rgba(212, 194, 164, 0.6),
        0 0 30px rgba(212, 194, 164, 0.4),
        0 0 45px rgba(212, 194, 164, 0.2);
    }
  }

  .luxury-glow-text {
    animation: luxuryGlow 4s ease-in-out infinite;
  }

  /* Premium Glass Container */
  .luxury-glass-container {
    background: linear-gradient(135deg,
      rgba(212, 194, 164, 0.12) 0%,
      rgba(212, 194, 164, 0.08) 50%,
      rgba(212, 194, 164, 0.15) 100%);
    backdrop-filter: blur(25px) saturate(180%);
    -webkit-backdrop-filter: blur(25px) saturate(180%);
    border: 1px solid rgba(212, 194, 164, 0.3);
    box-shadow:
      0 25px 50px rgba(22, 25, 29, 0.4),
      0 0 0 1px rgba(212, 194, 164, 0.1),
      inset 0 1px 0 rgba(212, 194, 164, 0.2),
      inset 0 -1px 0 rgba(212, 194, 164, 0.1);
  }

  /* Luxury Button */
  .luxury-button {
    position: relative;
    background: linear-gradient(135deg, #D4C2A4 0%, #C4B294 50%, #D4C2A4 100%);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow:
      0 8px 25px rgba(212, 194, 164, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(212, 194, 164, 0.4);
  }

  .luxury-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
      0 15px 35px rgba(212, 194, 164, 0.4),
      0 0 0 1px rgba(212, 194, 164, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* Premium Typography */
  .luxury-typography {
    font-feature-settings: "liga" 1, "kern" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: 0.02em;
  }

  /* VIP Badge Animation */
  @keyframes luxuryPulse {
    0%, 100% {
      box-shadow: 0 0 15px rgba(212, 194, 164, 0.4);
    }
    50% {
      box-shadow: 0 0 25px rgba(212, 194, 164, 0.6);
    }
  }

  .luxury-badge {
    animation: luxuryPulse 3s ease-in-out infinite;
  }

  /* Elegant Border Glow */
  @keyframes luxuryBorderGlow {
    0%, 100% {
      border-color: rgba(212, 194, 164, 0.4);
      box-shadow: 0 0 10px rgba(212, 194, 164, 0.2);
    }
    50% {
      border-color: rgba(212, 194, 164, 0.8);
      box-shadow: 0 0 25px rgba(212, 194, 164, 0.4);
    }
  }

  .luxury-border-glow {
    animation: luxuryBorderGlow 3s ease-in-out infinite;
  }

  /* Mobile Responsive Enhancements */
  @media (max-width: 1024px) {
    .luxury-glass-container {
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
    }
  }

  @media (max-width: 768px) {
    .luxury-glass-container {
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
    }

    .luxury-glow-text {
      animation-duration: 5s;
    }

    .luxury-button {
      padding: 10px 20px;
      font-size: 14px;
    }

    /* Responsive Container Heights */
    .mobile-container-height {
      height: 85vh !important;
      min-height: 600px;
    }

    .mobile-slide-height {
      height: 85vh !important;
      min-height: 600px;
    }
  }

  @media (max-width: 640px) {
    .luxury-glass-container {
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
      padding: 10px 14px;
    }

    .luxury-button {
      padding: 8px 16px;
      font-size: 13px;
    }

    /* Smaller Container Heights for Mobile */
    .mobile-container-height {
      height: 80vh !important;
      min-height: 550px;
    }

    .mobile-slide-height {
      height: 80vh !important;
      min-height: 550px;
    }
  }

  @media (max-width: 480px) {
    .luxury-glass-container {
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      padding: 8px 12px;
    }

    .luxury-button {
      padding: 8px 14px;
      font-size: 12px;
    }

    /* Optimized Heights for Small Screens */
    .mobile-container-height {
      height: 75vh !important;
      min-height: 500px;
    }

    .mobile-slide-height {
      height: 75vh !important;
      min-height: 500px;
    }
  }

  @media (max-width: 375px) {
    /* Extra Small Screens */
    .mobile-container-height {
      height: 70vh !important;
      min-height: 450px;
    }

    .mobile-slide-height {
      height: 70vh !important;
      min-height: 450px;
    }
  }

  /* Responsive Image Sizing */
  @media (max-width: 1024px) {
    .responsive-bg-image {
      top: -8% !important;
      left: -8% !important;
      width: 116% !important;
      height: 116% !important;
    }
  }

  @media (max-width: 768px) {
    .responsive-bg-image {
      top: -6% !important;
      left: -6% !important;
      width: 112% !important;
      height: 112% !important;
    }
  }

  @media (max-width: 640px) {
    .responsive-bg-image {
      top: -4% !important;
      left: -4% !important;
      width: 108% !important;
      height: 108% !important;
    }
  }

  @media (max-width: 480px) {
    .responsive-bg-image {
      top: -2% !important;
      left: -2% !important;
      width: 104% !important;
      height: 104% !important;
    }
  }

  @media (max-width: 375px) {
    .responsive-bg-image {
      top: 0% !important;
      left: 0% !important;
      width: 100% !important;
      height: 100% !important;
    }
  }

  /* Responsive Typography */
  /* Large screens - reduced font sizes for better background visibility */
  @media (min-width: 1025px) {
    .responsive-title {
      font-size: 4rem !important;
      line-height: 1.1 !important;
    }

    .responsive-description {
      font-size: 1.1rem !important;
      line-height: 1.5 !important;
    }

    .responsive-pricing {
      font-size: 1rem !important;
    }

    .responsive-price-value {
      font-size: 1.875rem !important;
    }

    .responsive-activities {
      font-size: 0.95rem !important;
    }
  }

  @media (max-width: 1024px) {
    .responsive-title {
      font-size: 3.5rem !important;
      line-height: 1.1 !important;
    }

    .responsive-description {
      font-size: 1rem !important;
      line-height: 1.5 !important;
    }

    .responsive-pricing {
      font-size: 0.95rem !important;
    }

    .responsive-price-value {
      font-size: 1.75rem !important;
    }

    .responsive-activities {
      font-size: 0.9rem !important;
    }
  }

  @media (max-width: 768px) {
    .responsive-title {
      font-size: 2.75rem !important;
      line-height: 1.1 !important;
    }

    .responsive-description {
      font-size: 0.95rem !important;
      line-height: 1.4 !important;
    }

    .responsive-pricing {
      font-size: 0.9rem !important;
    }

    .responsive-price-value {
      font-size: 1.5rem !important;
    }

    .responsive-activities {
      font-size: 0.85rem !important;
    }
  }

  @media (max-width: 640px) {
    .responsive-title {
      font-size: 2.25rem !important;
      line-height: 1.1 !important;
    }

    .responsive-description {
      font-size: 0.9rem !important;
      line-height: 1.4 !important;
    }

    .responsive-pricing {
      font-size: 0.85rem !important;
    }

    .responsive-price-value {
      font-size: 1.35rem !important;
    }

    .responsive-activities {
      font-size: 0.8rem !important;
    }
  }

  @media (max-width: 480px) {
    .responsive-title {
      font-size: 1.875rem !important;
      line-height: 1.1 !important;
    }

    .responsive-description {
      font-size: 0.85rem !important;
      line-height: 1.3 !important;
    }

    .responsive-pricing {
      font-size: 0.8rem !important;
    }

    .responsive-price-value {
      font-size: 1.25rem !important;
    }

    .responsive-activities {
      font-size: 0.75rem !important;
    }
  }

  @media (max-width: 375px) {
    .responsive-title {
      font-size: 1.625rem !important;
      line-height: 1.1 !important;
    }

    .responsive-description {
      font-size: 0.8rem !important;
      line-height: 1.3 !important;
    }

    .responsive-pricing {
      font-size: 0.75rem !important;
    }

    .responsive-price-value {
      font-size: 1.125rem !important;
    }

    .responsive-activities {
      font-size: 0.7rem !important;
    }
  }

  /* Responsive Spacing and Layout */
  @media (max-width: 1024px) {
    .responsive-content-container {
      bottom: 16px !important;
      left: 16px !important;
      max-width: calc(100% - 32px) !important;
      display: flex !important;
      flex-direction: column !important;
    }

    .responsive-gap {
      gap: 1rem !important;
    }

    .responsive-mb {
      margin-bottom: 1rem !important;
    }

    .responsive-mb-lg {
      margin-bottom: 1.5rem !important;
    }
  }

  @media (max-width: 768px) {
    .responsive-content-container {
      bottom: 12px !important;
      left: 12px !important;
      max-width: calc(100% - 24px) !important;
      display: flex !important;
      flex-direction: column !important;
    }

    .responsive-gap {
      gap: 0.75rem !important;
    }

    .responsive-mb {
      margin-bottom: 0.75rem !important;
    }

    .responsive-mb-lg {
      margin-bottom: 1.25rem !important;
    }

    .responsive-px {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
    }

    .responsive-py {
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
    }
  }

  @media (max-width: 640px) {
    .responsive-content-container {
      bottom: 10px !important;
      left: 10px !important;
      max-width: calc(100% - 20px) !important;
      display: flex !important;
      flex-direction: column !important;
    }

    .responsive-gap {
      gap: 0.5rem !important;
    }

    .responsive-mb {
      margin-bottom: 0.5rem !important;
    }

    .responsive-mb-lg {
      margin-bottom: 1rem !important;
    }

    .responsive-px {
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }

    .responsive-py {
      padding-top: 0.375rem !important;
      padding-bottom: 0.375rem !important;
    }
  }

  @media (max-width: 480px) {
    .responsive-content-container {
      bottom: 8px !important;
      left: 8px !important;
      max-width: calc(100% - 16px) !important;
    }

    .responsive-gap {
      gap: 0.375rem !important;
    }

    .responsive-mb {
      margin-bottom: 0.375rem !important;
    }

    .responsive-mb-lg {
      margin-bottom: 0.75rem !important;
    }

    .responsive-px {
      padding-left: 0.375rem !important;
      padding-right: 0.375rem !important;
    }

    .responsive-py {
      padding-top: 0.25rem !important;
      padding-bottom: 0.25rem !important;
    }
  }

  @media (max-width: 375px) {
    .responsive-content-container {
      bottom: 6px !important;
      left: 6px !important;
      max-width: calc(100% - 12px) !important;
    }

    .responsive-gap {
      gap: 0.25rem !important;
    }

    .responsive-mb {
      margin-bottom: 0.25rem !important;
    }

    .responsive-mb-lg {
      margin-bottom: 0.5rem !important;
    }

    .responsive-px {
      padding-left: 0.25rem !important;
      padding-right: 0.25rem !important;
    }

    .responsive-py {
      padding-top: 0.125rem !important;
      padding-bottom: 0.125rem !important;
    }
  }

  /* Responsive Component Positioning */
  @media (max-width: 1024px) {
    .responsive-badge-position {
      top: 1rem !important;
      right: 1rem !important;
    }

    .responsive-counter-position {
      bottom: 1rem !important;
    }

    .responsive-button-size {
      padding: 0.75rem 2rem !important;
      font-size: 1rem !important;
    }

    .responsive-badge-size {
      padding: 0.5rem 0.75rem !important;
      font-size: 0.75rem !important;
    }

    .responsive-counter-size {
      padding: 0.75rem 1.5rem !important;
      font-size: 0.875rem !important;
      margin-top: 1rem !important;
    }
  }

  @media (max-width: 768px) {
    .responsive-badge-position {
      top: 0.75rem !important;
      right: 0.75rem !important;
    }

    .responsive-counter-position {
      bottom: 0.75rem !important;
    }

    .responsive-button-size {
      padding: 0.625rem 1.5rem !important;
      font-size: 0.875rem !important;
    }

    .responsive-badge-size {
      padding: 0.375rem 0.625rem !important;
      font-size: 0.625rem !important;
    }

    .responsive-counter-size {
      padding: 0.5rem 1rem !important;
      font-size: 0.75rem !important;
      margin-top: 0.875rem !important;
    }
  }

  @media (max-width: 640px) {
    .responsive-badge-position {
      top: 0.5rem !important;
      right: 0.5rem !important;
    }

    .responsive-counter-position {
      bottom: 0.5rem !important;
    }

    .responsive-button-size {
      padding: 0.5rem 1.25rem !important;
      font-size: 0.8rem !important;
    }

    .responsive-badge-size {
      padding: 0.25rem 0.5rem !important;
      font-size: 0.5rem !important;
    }

    .responsive-counter-size {
      padding: 0.375rem 0.75rem !important;
      font-size: 0.625rem !important;
      margin-top: 0.75rem !important;
    }
  }

  @media (max-width: 480px) {
    .responsive-badge-position {
      top: 0.375rem !important;
      right: 0.375rem !important;
    }

    .responsive-counter-position {
      bottom: 0.375rem !important;
    }

    .responsive-button-size {
      padding: 0.5rem 1rem !important;
      font-size: 0.75rem !important;
    }

    .responsive-badge-size {
      padding: 0.1875rem 0.375rem !important;
      font-size: 0.4375rem !important;
    }

    .responsive-counter-size {
      padding: 0.25rem 0.5rem !important;
      font-size: 0.5rem !important;
      margin-top: 0.625rem !important;
    }
  }

  @media (max-width: 375px) {
    .responsive-badge-position {
      top: 0.25rem !important;
      right: 0.25rem !important;
    }

    .responsive-counter-position {
      bottom: 0.25rem !important;
    }

    .responsive-button-size {
      padding: 0.375rem 0.875rem !important;
      font-size: 0.7rem !important;
    }

    .responsive-badge-size {
      padding: 0.125rem 0.25rem !important;
      font-size: 0.375rem !important;
    }

    .responsive-counter-size {
      padding: 0.125rem 0.25rem !important;
      font-size: 0.375rem !important;
      margin-top: 0.5rem !important;
    }
  }

  /* Fixed Counter Positioning - Desktop: fixed center, Mobile: below button */
  .fixed-counter {
    position: fixed !important;
    bottom: 2rem !important;
    left: 50% !important;
    right: auto !important;
    transform: translateX(-50%) !important;
    width: fit-content !important;
    z-index: 100 !important;
    background: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    border: none !important;
    box-shadow: none !important;
    margin-top: 0 !important;
    order: auto !important;
  }

  /* Mobile: Position counter below button in flex layout */
  @media (max-width: 1024px) {
    .fixed-counter {
      position: static !important;
      bottom: auto !important;
      left: auto !important;
      right: auto !important;
      transform: none !important;
      margin-left: auto !important;
      margin-right: auto !important;
      margin-top: 1.5rem !important;
      order: 999 !important;
    }
  }

  @media (max-width: 768px) {
    .fixed-counter {
      margin-top: 1.25rem !important;
    }
  }

  @media (max-width: 640px) {
    .fixed-counter {
      margin-top: 1rem !important;
    }
  }

  @media (max-width: 480px) {
    .fixed-counter {
      margin-top: 0.875rem !important;
    }
  }

  @media (max-width: 375px) {
    .fixed-counter {
      margin-top: 0.75rem !important;
    }
  }

  /* Mobile-Specific Luxury Enhancements */
  @media (max-width: 768px) {
    /* Enhanced Mobile Glow Effects */
    .mobile-luxury-glow {
      text-shadow:
        0 0 8px rgba(212, 194, 164, 0.6),
        0 0 16px rgba(212, 194, 164, 0.4),
        0 0 24px rgba(212, 194, 164, 0.2) !important;
    }

    /* Mobile Glass Effect */
    .mobile-glass-enhanced {
      background: linear-gradient(135deg,
        rgba(212, 194, 164, 0.15) 0%,
        rgba(212, 194, 164, 0.1) 50%,
        rgba(212, 194, 164, 0.18) 100%) !important;
      backdrop-filter: blur(20px) saturate(180%) !important;
      -webkit-backdrop-filter: blur(20px) saturate(180%) !important;
      border: 1px solid rgba(212, 194, 164, 0.35) !important;
      box-shadow:
        0 20px 40px rgba(22, 25, 29, 0.5),
        0 0 0 1px rgba(212, 194, 164, 0.15),
        inset 0 1px 0 rgba(212, 194, 164, 0.25),
        inset 0 -1px 0 rgba(212, 194, 164, 0.15) !important;
    }

    /* Mobile Button Enhancement */
    .mobile-button-luxury {
      background: linear-gradient(135deg, #D4C2A4 0%, #C4B294 50%, #D4C2A4 100%) !important;
      box-shadow:
        0 6px 20px rgba(212, 194, 164, 0.35),
        inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
      border: 1px solid rgba(212, 194, 164, 0.45) !important;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1) !important;
    }

    .mobile-button-luxury:hover {
      transform: translateY(-2px) scale(1.01) !important;
      box-shadow:
        0 12px 30px rgba(212, 194, 164, 0.45),
        0 0 0 1px rgba(212, 194, 164, 0.35),
        inset 0 1px 0 rgba(255, 255, 255, 0.35) !important;
    }

    /* Mobile Badge Enhancement */
    .mobile-badge-luxury {
      background: linear-gradient(135deg, rgba(212, 194, 164, 0.25), rgba(212, 194, 164, 0.15)) !important;
      border: 1px solid rgba(212, 194, 164, 0.5) !important;
      box-shadow: 0 6px 20px rgba(212, 194, 164, 0.35) !important;
      animation: mobileLuxuryPulse 4s ease-in-out infinite !important;
    }

    @keyframes mobileLuxuryPulse {
      0%, 100% {
        box-shadow: 0 6px 20px rgba(212, 194, 164, 0.35);
      }
      50% {
        box-shadow: 0 8px 25px rgba(212, 194, 164, 0.5);
      }
    }
  }

  @media (max-width: 480px) {
    /* Enhanced Small Screen Effects */
    .mobile-luxury-glow {
      text-shadow:
        0 0 6px rgba(212, 194, 164, 0.7),
        0 0 12px rgba(212, 194, 164, 0.5),
        0 0 18px rgba(212, 194, 164, 0.3) !important;
    }

    .mobile-glass-enhanced {
      backdrop-filter: blur(15px) saturate(160%) !important;
      -webkit-backdrop-filter: blur(15px) saturate(160%) !important;
    }

    /* Optimized Touch Targets */
    .mobile-touch-target {
      min-height: 44px !important;
      min-width: 44px !important;
    }
  }

  @media (max-width: 375px) {
    /* Extra Small Screen Optimizations */
    .mobile-luxury-glow {
      text-shadow:
        0 0 4px rgba(212, 194, 164, 0.8),
        0 0 8px rgba(212, 194, 164, 0.6),
        0 0 12px rgba(212, 194, 164, 0.4) !important;
    }

    .mobile-glass-enhanced {
      backdrop-filter: blur(12px) saturate(140%) !important;
      -webkit-backdrop-filter: blur(12px) saturate(140%) !important;
    }
  }
`;




const FeaturedTours = () => {
  const navigateToPage = useNavigate();
  const [tours, setTours] = useState<Tour[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);



  const slideRefs = useRef<(HTMLDivElement | null)[]>([]);
  const imageRefs = useRef<(HTMLDivElement | null)[]>([]);
  const textRefs = useRef<(HTMLElement | null)[][]>([]);
  const counterStripRef = useRef<HTMLDivElement | null>(null);

  const SLIDE_DURATION = 1.6; // Increased for smoother transitions
  const NEXT = 1;
  const PREV = -1;

  // Initialize refs arrays
  useEffect(() => {
    slideRefs.current = [];
    imageRefs.current = [];
    textRefs.current = [];
  }, [tours.length]);

  useEffect(() => {
    const fetchFeaturedTours = async () => {
      try {
        setLoading(true);
        const allToursData = await FirebaseService.getTours();
        console.log('Raw tours data from Firebase:', allToursData);

        // Map the data properly like in useTours hook
        const allTours = allToursData.map((tour: any) => ({
          id: tour.id,
          title: tour.title || 'Untitled Tour',
          description: tour.description || 'No description available',
          price: tour.price || 0,
          duration: tour.duration || 'Duration not specified',
          location: tour.location || '',
          destinations: Array.isArray(tour.destinations) ? tour.destinations : [],
          activities: Array.isArray(tour.activities) ? tour.activities : [],
          accommodations: Array.isArray(tour.accommodations) ? tour.accommodations : [],
          maxGroupSize: tour.maxGroupSize || 12,
          minGroupSize: tour.minGroupSize || 1,
          difficulty: tour.difficulty || 'easy',
          includes: Array.isArray(tour.includes) ? tour.includes : [],
          excludes: Array.isArray(tour.excludes) ? tour.excludes : [],
          images: Array.isArray(tour.images) ? tour.images : [],
          featured: tour.featured || false,
          status: tour.status || 'active',
          rating: tour.rating || 0,
          reviewCount: tour.reviewCount || 0,
          tourType: tour.tourType || 'standard',
          category: tour.category || 'Safari',
          accommodationLevel: tour.accommodationLevel || 'Standard',
          seasonality: tour.seasonality || {
            greenSeason: true,
            drySeason: true,
            bestMonths: []
          },
          itinerary: Array.isArray(tour.itinerary) ? tour.itinerary : [],
          fitnessRequirements: tour.fitnessRequirements || {
            level: 'Moderate',
            description: 'Basic fitness required',
            walkingDistance: '2-3 km daily',
            terrain: 'Mostly flat with some uneven ground',
            ageRestrictions: 'Suitable for ages 8+',
            medicalConditions: []
          },
          equipment: tour.equipment || {
            provided: [],
            recommended: [],
            required: []
          },
          groupOptions: Array.isArray(tour.groupOptions) ? tour.groupOptions : [],
          specialFeatures: Array.isArray(tour.specialFeatures) ? tour.specialFeatures : [],
          difficultyDetails: tour.difficultyDetails || 'Standard difficulty level',
          createdAt: tour.createdAt || new Date(),
          updatedAt: tour.updatedAt || new Date()
        })) as Tour[];

        let featuredTours = allTours
          .filter((tour) => tour.featured === true)
          .slice(0, 5); // Limiting to 5 for the carousel

        // If no featured tours, create sample data for demo
        if (featuredTours.length === 0) {
          console.log('No featured tours found, using sample data');
          featuredTours = [
            {
              id: 'sample-1',
              title: 'SERENGETI',
              description: 'Witness the Great Migration and experience the Big Five in their natural habitat.',
              price: 2850,
              duration: '7 DAYS',
              location: 'Serengeti National Park, Tanzania',
              destinations: ['Serengeti', 'Ngorongoro Crater'],
              activities: ['Wildebeest Migration', 'Big Five', 'Luxury Lodges'],
              accommodations: ['Safari Lodge', 'Luxury Tented Camp'],
              maxGroupSize: 12,
              minGroupSize: 2,
              difficulty: 'moderate' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1551632436-cbf8dd35adfa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80'],
              featured: true,
              status: 'active' as const,
              rating: 4.8,
              reviewCount: 124,
              tourType: 'standard' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Luxury',
              seasonality: {
                greenSeason: true,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'October']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Moderate',
                description: 'Basic fitness required',
                walkingDistance: '2-3 km daily',
                terrain: 'Mostly flat with some uneven ground',
                ageRestrictions: 'Suitable for ages 8+',
                medicalConditions: []
              },
              equipment: {
                provided: [
                  { name: 'Safari Vehicle', description: '4x4 game drive vehicle', category: 'Transportation', optional: false },
                  { name: 'Professional Guide', description: 'Experienced safari guide', category: 'Service', optional: false }
                ],
                recommended: [
                  { name: 'Binoculars', description: 'For wildlife viewing', category: 'Optics', optional: true },
                  { name: 'Camera', description: 'For photography', category: 'Photography', optional: true }
                ],
                required: [
                  { name: 'Comfortable walking shoes', description: 'For short walks', category: 'Clothing', optional: false }
                ]
              },
              groupOptions: [
                { type: 'Private', minParticipants: 2, maxParticipants: 6, pricePerPerson: 2850, description: 'Private safari experience' },
                { type: 'Small Group', minParticipants: 4, maxParticipants: 12, pricePerPerson: 2500, description: 'Small group safari' }
              ],
              specialFeatures: ['Wildebeest Migration', 'Big Five', 'Luxury Lodges'],
              difficultyDetails: 'Moderate physical activity with game drives and short walks',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-2',
              title: 'MAASAI MARA',
              description: 'Experience authentic wildlife and cultural visits in Kenya\'s most famous reserve.',
              price: 1950,
              duration: '5 DAYS',
              location: 'Maasai Mara National Reserve, Kenya',
              destinations: ['Maasai Mara', 'Cultural Villages'],
              activities: ['Cultural Visits', 'Game Drives', 'Balloon Safari'],
              accommodations: ['Safari Lodge', 'Luxury Tented Camp'],
              maxGroupSize: 8,
              minGroupSize: 2,
              difficulty: 'moderate' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide', 'Cultural Visits'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Gear'],
              images: ['https://images.unsplash.com/photo-1547036967-23d11aacaee0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
              featured: true,
              status: 'active' as const,
              rating: 4.9,
              reviewCount: 89,
              tourType: 'standard' as const,
              category: 'Adventure',
              accommodationLevel: 'Basic',
              seasonality: {
                greenSeason: false,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'January', 'February']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'High',
                description: 'Excellent fitness required for high altitude trekking',
                walkingDistance: '15-20 km daily',
                terrain: 'Steep mountain trails and rocky paths',
                ageRestrictions: 'Suitable for ages 16+',
                medicalConditions: ['Heart conditions', 'Respiratory issues']
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [
                { type: 'Private', minParticipants: 2, maxParticipants: 4, pricePerPerson: 1950, description: 'Private safari experience' }
              ],
              specialFeatures: ['Cultural Visits', 'Game Drives', 'Balloon Safari'],
              difficultyDetails: 'Moderate physical activity with cultural experiences',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-3',
              title: 'NGORONGORO',
              description: 'Africa\'s Garden of Eden with incredible crater floor wildlife viewing.',
              price: 1650,
              duration: '4 DAYS',
              location: 'Ngorongoro Crater, Tanzania',
              destinations: ['Ngorongoro Crater', 'Olduvai Gorge'],
              activities: ['Crater Floor', 'Rhino Spotting', 'Luxury Camping'],
              accommodations: ['Crater Lodge', 'Luxury Camping'],
              maxGroupSize: 15,
              minGroupSize: 2,
              difficulty: 'easy' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1516426122078-c23e76319801?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2068&q=80'],
              featured: true,
              status: 'active' as const,
              rating: 4.6,
              reviewCount: 156,
              tourType: 'luxury' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Luxury',
              seasonality: {
                greenSeason: true,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'December', 'January']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Easy',
                description: 'Minimal physical activity required',
                walkingDistance: '1-2 km daily',
                terrain: 'Flat crater floor',
                ageRestrictions: 'Suitable for all ages',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [
                { type: 'Private', minParticipants: 2, maxParticipants: 6, pricePerPerson: 1650, description: 'Private crater experience' }
              ],
              specialFeatures: ['Crater Floor', 'Rhino Spotting', 'Luxury Camping'],
              difficultyDetails: 'Easy crater floor game drives',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-4',
              title: 'AMBOSELI',
              description: 'Kilimanjaro views & giants - experience massive elephant herds.',
              price: 1200,
              duration: '3 DAYS',
              location: 'Amboseli National Park, Kenya',
              destinations: ['Amboseli', 'Kilimanjaro Views'],
              activities: ['Elephant Herds', 'Kilimanjaro Views', 'Maasai Culture'],
              accommodations: ['Safari Lodge', 'Tented Camp'],
              maxGroupSize: 12,
              minGroupSize: 2,
              difficulty: 'easy' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1564760055775-d63b17a55c44?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2076&q=80'],
              featured: true,
              status: 'active' as const,
              rating: 4.7,
              reviewCount: 98,
              tourType: 'standard' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Standard',
              seasonality: {
                greenSeason: true,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'January', 'February']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Easy',
                description: 'Minimal physical activity required',
                walkingDistance: '1-2 km daily',
                terrain: 'Flat savanna',
                ageRestrictions: 'Suitable for all ages',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [
                { type: 'Private', minParticipants: 2, maxParticipants: 6, pricePerPerson: 1200, description: 'Private elephant viewing' }
              ],
              specialFeatures: ['Elephant Herds', 'Kilimanjaro Views', 'Maasai Culture'],
              difficultyDetails: 'Easy game drives with cultural experiences',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-5',
              title: 'TARANGIRE',
              description: 'Land of giants & baobabs - incredible bird watching and elephant paradise.',
              price: 2100,
              duration: '6 DAYS',
              location: 'Tarangire National Park, Tanzania',
              destinations: ['Tarangire', 'Baobab Valley'],
              activities: ['Baobab Trees', 'Elephant Paradise', 'Bird Watching'],
              accommodations: ['Safari Lodge', 'Luxury Tented Camp'],
              maxGroupSize: 10,
              minGroupSize: 2,
              difficulty: 'moderate' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1549366021-9f761d040a94?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
              featured: true,
              status: 'active' as const,
              rating: 4.8,
              reviewCount: 142,
              tourType: 'standard' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Luxury',
              seasonality: {
                greenSeason: true,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'October']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Moderate',
                description: 'Basic fitness required',
                walkingDistance: '2-3 km daily',
                terrain: 'Mostly flat with some uneven ground',
                ageRestrictions: 'Suitable for ages 8+',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [
                { type: 'Private', minParticipants: 2, maxParticipants: 6, pricePerPerson: 2100, description: 'Private baobab and elephant experience' }
              ],
              specialFeatures: ['Baobab Trees', 'Elephant Paradise', 'Bird Watching'],
              difficultyDetails: 'Moderate game drives with walking safaris',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            }
          ];
        } else {
          console.log('Loaded featured tours:', featuredTours);
          console.log('First tour images:', featuredTours[0]?.images);

          // Debug image URLs for each tour
          featuredTours.forEach((tour, index) => {
            console.log(`Tour ${index + 1} (${tour.title}):`, {
              id: tour.id,
              images: tour.images,
              imageCount: tour.images?.length || 0,
              firstImage: tour.images?.[0]
            });
          });
        }

        console.log('Setting tours:', featuredTours);
        console.log('Total tours to display:', featuredTours.length);

        // Log each tour's image data for debugging
        featuredTours.forEach((tour, index) => {
          console.log(`Final Tour ${index + 1} (${tour.title}):`, {
            id: tour.id,
            images: tour.images,
            imageCount: tour.images?.length || 0,
            firstImage: tour.images?.[0],
            imageType: typeof tour.images?.[0]
          });
        });

        setTours(featuredTours);
      } catch (err) {
        console.error('Error fetching featured tours:', err);
        setError('Failed to load destinations');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedTours();
  }, []);

  // Initialize GSAP animations
  useGSAP(() => {
    if (tours.length === 0) return;

    // Set initial states for all slides
    slideRefs.current.forEach((slide, index) => {
      if (slide) {
        gsap.set(slide, {
          visibility: index === 0 ? 'visible' : 'hidden',
          y: index === 0 ? 0 : '100%'
        });
      }
    });

    // Set initial states for all images
    imageRefs.current.forEach((image) => {
      if (image) {
        gsap.set(image, {
          scale: 1,
          scaleY: 1,
          y: 0,
          rotation: 0,
          transformOrigin: 'center center'
        });
      }
    });

    // Set initial states for all text elements
    textRefs.current.forEach((textElements, slideIndex) => {
      if (textElements && textElements.length > 0) {
        textElements.forEach((element) => {
          if (element) {
            gsap.set(element, {
              y: slideIndex === 0 ? 0 : '100%',
              opacity: slideIndex === 0 ? 1 : 0
            });
          }
        });
      }
    });

    // Animate in the first slide text with smoother stagger
    const firstTextLines = textRefs.current[0];
    if (firstTextLines && firstTextLines.length > 0) {
      gsap.fromTo(firstTextLines.filter(el => el !== null),
        { y: '100%', opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 1.2, // Increased duration for smoother animation
          stagger: 0.2, // Increased stagger for more elegant timing
          delay: 0.4,
          ease: 'power3.out' // Smoother easing
        }
      );
    }

    // Initialize counter strip
    if (counterStripRef.current) {
      gsap.set(counterStripRef.current, { y: 0 });
    }
  }, [tours]);

  const animateCounter = (targetIndex: number, timeline: gsap.core.Timeline) => {
    if (!counterStripRef.current) return;

    const targetY = -targetIndex * 24; // 24px is height of each number

    timeline.to(counterStripRef.current, {
      y: targetY,
      duration: SLIDE_DURATION * 0.9, // Slightly longer for smoother counter animation
      ease: 'power3.inOut' // Smoother easing
    }, 0);
  };

  const performNavigation = (prevIndex: number, nextIndex: number, direction: number) => {
    setIsAnimating(true);

    // Get current and next elements
    const currentSlide = slideRefs.current[prevIndex];
    const currentImage = imageRefs.current[prevIndex];
    const currentTextLines = textRefs.current[prevIndex]?.filter(el => el !== null) || [];

    const nextSlide = slideRefs.current[nextIndex];
    const nextImage = imageRefs.current[nextIndex];
    const nextTextLines = textRefs.current[nextIndex]?.filter(el => el !== null) || [];

    if (!currentSlide || !nextSlide || !currentImage || !nextImage) {
      setIsAnimating(false);
      return;
    }

    // Setup next slide initial position
    gsap.set(nextSlide, {
      visibility: 'visible',
      y: direction * 100 + '%'
    });

    // Setup next image initial state with proper transform origin
    gsap.set(nextImage, {
      y: -direction * 30 + '%',
      scale: 1.2,
      rotation: -direction * 5,
      transformOrigin: 'center center'
    });

    // Setup next text lines initial state
    gsap.set(nextTextLines, {
      y: '100%',
      opacity: 0
    });

    // Create main animation timeline
    const tl = gsap.timeline({
      onComplete: () => {
        // Clean up after animation
        gsap.set(currentSlide, { visibility: 'hidden' });
        gsap.set(currentImage, {
          scale: 1,
          y: 0,
          rotation: 0,
          transformOrigin: 'center center'
        });
        setIsAnimating(false);
      }
    });

    // Counter animation (starts immediately)
    animateCounter(nextIndex, tl);

    // Phase 1: Animate out current content (0 - 0.5s) - Smoother exit
    tl.to(currentTextLines, {
      y: '-100%',
      opacity: 0,
      duration: 0.5,
      stagger: 0.08,
      ease: 'power3.in' // Smoother easing
    }, 0);

    // Phase 2: Slide transitions (0.3 - 1.3s) - Longer, smoother transitions
    tl.to(currentSlide, {
      y: -direction * 100 + '%',
      duration: 1.0,
      ease: 'power3.inOut' // Smoother easing
    }, 0.3);

    tl.to(currentImage, {
      y: direction * 25 + '%', // Reduced movement for subtlety
      scale: 1.15, // Slightly less scale for smoother effect
      rotation: direction * 3, // Reduced rotation for elegance
      duration: 1.0,
      ease: 'power3.inOut' // Smoother easing
    }, 0.3);

    tl.to(nextSlide, {
      y: '0%',
      duration: 1.0,
      ease: 'power3.inOut' // Smoother easing
    }, 0.3);

    tl.to(nextImage, {
      y: '0%',
      scale: 1,
      rotation: 0,
      duration: 1.0,
      ease: 'power3.inOut' // Smoother easing
    }, 0.3);

    // Phase 3: Animate in new text (0.8 - 1.6s) - Smoother entrance
    tl.to(nextTextLines, {
      y: 0,
      opacity: 1,
      duration: 0.8,
      stagger: 0.15, // Increased stagger for more elegant timing
      ease: 'power3.out' // Smoother easing
    }, 0.8);
  };

  const navigate = useCallback((direction: number) => {
    if (isAnimating) return;

    const prevIndex = currentIndex;
    let nextIndex: number;

    if (direction === NEXT) {
      nextIndex = currentIndex < tours.length - 1 ? currentIndex + 1 : 0;
    } else {
      nextIndex = currentIndex > 0 ? currentIndex - 1 : tours.length - 1;
    }

    setCurrentIndex(nextIndex);
    performNavigation(prevIndex, nextIndex, direction);
  }, [isAnimating, currentIndex, tours.length]);



  const handleClick = useCallback((e: React.MouseEvent) => {
    const windowWidth = window.innerWidth;
    navigate(e.clientX < windowWidth / 2 ? PREV : NEXT);
  }, [navigate]);

  const handleWheel = useCallback((e: React.WheelEvent) => {
    navigate(e.deltaY > 0 ? NEXT : PREV);
  }, [navigate]);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
      navigate(NEXT);
    } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
      navigate(PREV);
    }
  }, [navigate]);

  const handleTouchStart = useRef(0);
  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    const touchEndY = e.changedTouches[0].screenY;
    if (handleTouchStart.current > touchEndY + 5) {
      navigate(NEXT);
    } else if (handleTouchStart.current < touchEndY - 5) {
      navigate(PREV);
    }
  }, [navigate]);

  const handleBookNow = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    const currentTour = tours[currentIndex];
    if (currentTour) {
      navigateToPage(`/tours/${currentTour.id}`);
    }
  }, [currentIndex, tours, navigateToPage]);

  useEffect(() => {
    const handleKeyDownEvent = (e: KeyboardEvent) => handleKeyDown(e);

    document.addEventListener('keydown', handleKeyDownEvent);

    const handleTouchStartEvent = (e: TouchEvent) => {
      handleTouchStart.current = e.changedTouches[0].screenY;
    };

    document.addEventListener('touchstart', handleTouchStartEvent);

    return () => {
      document.removeEventListener('keydown', handleKeyDownEvent);
      document.removeEventListener('touchstart', handleTouchStartEvent);
    };
  }, [handleKeyDown]);

  const formatNumber = (num: number) => num < 10 ? `0${num}` : `${num}`;



  if (loading) {
    return (
      <PageLoader
        title="Loading Safari Tours..."
        subtitle="Discovering amazing wildlife adventures for you..."
      />
    );
  }

  if (error || tours.length === 0) {
    return (
      <div
        className="relative w-full h-screen overflow-hidden flex items-center justify-center luxury-scrollbar"
        style={{
          background: 'linear-gradient(135deg, #16191D 0%, #1A1E23 50%, #16191D 100%)',
          color: '#F2EEE6'
        }}
      >
        <div className="text-center luxury-glass-container p-12 rounded-2xl">
          <AlertTriangle
            className="h-16 w-16 mx-auto mb-6"
            style={{ color: '#D4C2A4' }}
          />
          <h2
            className="text-3xl font-bold mb-4 luxury-glow-text luxury-typography"
            style={{
              fontFamily: 'Cormorant Garamond, serif',
              color: '#F2EEE6'
            }}
          >
            Service Temporarily Unavailable
          </h2>
          <p
            className="text-lg opacity-90 luxury-typography"
            style={{
              fontFamily: 'Open Sans, sans-serif',
              color: '#F2EEE6'
            }}
          >
            {error || 'Our premium safari experiences are currently being updated. Please try again shortly.'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Luxury VVIP Styles */}
      <style>{luxuryScrollbarStyles}</style>
      <style>{luxuryStyles}</style>

      <div
        className="relative w-full h-screen mobile-container-height overflow-hidden luxury-scrollbar"
        style={{
          background: 'linear-gradient(135deg, #16191D 0%, #1A1E23 50%, #16191D 100%)',
          color: '#F2EEE6'
        }}
        onClick={handleClick}
        onWheel={handleWheel}
        onTouchEnd={handleTouchEnd}
      >
        {/* Premium Floating Particles */}
        <div className="absolute inset-0 pointer-events-none z-5">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="luxury-particle absolute rounded-full"
              style={{
                width: `${Math.random() * 4 + 2}px`,
                height: `${Math.random() * 4 + 2}px`,
                background: 'radial-gradient(circle, #D4C2A4, transparent)',
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 6}s`,
                opacity: 0.3
              }}
            />
          ))}
        </div>
        {/* Slideshow Container */}
        <div className="relative w-full h-full overflow-hidden">
          {tours.map((tour, index) => (
            <div
              key={tour.id}
              ref={(el) => (slideRefs.current[index] = el)}
              className="absolute inset-0 w-full h-full mobile-slide-height"
              style={{ visibility: index === 0 ? 'visible' : 'hidden' }}
            >
              {/* Luxury Background Image with Premium Overlay */}
              <div
                ref={(el) => (imageRefs.current[index] = el)}
                className="absolute -top-[10%] -left-[10%] w-[120%] h-[120%] responsive-bg-image bg-cover bg-center will-change-transform"
                style={{
                  backgroundImage: `url(${(() => {
                    const imageUrl = tour.images && tour.images.length > 0 ? tour.images[0] : 'https://images.unsplash.com/photo-1516426122078-c23e76319801?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80';
                    console.log(`Tour ${tour.title} - Image URL:`, imageUrl);
                    return imageUrl;
                  })()})`,
                }}
              >
                {/* Hidden img element for error handling */}
                <img
                  src={tour.images && tour.images.length > 0 ? tour.images[0] : 'https://images.unsplash.com/photo-1516426122078-c23e76319801?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80'}
                  alt=""
                  className="hidden"
                  onError={(e) => {
                    console.error(`Failed to load image for tour ${tour.title}:`, tour.images?.[0]);
                    console.error('Image error details:', e);
                    // Update the background image of the parent div
                    const parentDiv = e.currentTarget.parentElement as HTMLDivElement;
                    if (parentDiv) {
                      parentDiv.style.backgroundImage = 'url(https://images.unsplash.com/photo-1516426122078-c23e76319801?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80)';
                    }
                  }}
                  onLoad={() => {
                    console.log(`Successfully loaded image for tour ${tour.title}`);
                  }}
                />
                {/* Premium Luxury Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-b from-[#16191D]/60 via-[#16191D]/30 to-[#16191D]/80"></div>
                {/* VIP Accent Overlay */}
                <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-[#D4C2A4]/5 to-transparent"></div>
              </div>

              {/* VIP Premium Badge */}
              <div className="absolute top-8 right-8 responsive-badge-position z-20 md:top-8 md:right-8 top-4 right-4">
                <div
                  className="luxury-glass-container mobile-glass-enhanced mobile-badge-luxury px-3 py-2 md:px-4 md:py-2 responsive-badge-size rounded-full luxury-border-glow"
                  style={{
                    background: 'linear-gradient(135deg, rgba(212, 194, 164, 0.2), rgba(212, 194, 164, 0.1))',
                    border: '1px solid rgba(212, 194, 164, 0.4)',
                    boxShadow: '0 8px 25px rgba(212, 194, 164, 0.3)'
                  }}
                >
                  <span
                    className="text-xs md:text-sm responsive-badge-size font-bold luxury-glow-text luxury-typography"
                    style={{
                      color: '#D4C2A4',
                      fontFamily: 'Open Sans, sans-serif',
                      letterSpacing: '0.1em',
                      textTransform: 'uppercase'
                    }}
                  >
                    ✦ VIP Experience ✦
                  </span>
                </div>
              </div>

              {/* Luxury Package Info Container - Flex container for mobile counter positioning */}
              <div className="absolute bottom-20 left-20 max-w-4xl responsive-content-container z-10 md:bottom-20 md:left-20 bottom-12 left-8 flex flex-col">
                {/* VIP Title with Luxury Glow */}
                <div
                  ref={(el) => {
                    if (!textRefs.current[index]) textRefs.current[index] = [];
                    textRefs.current[index][0] = el;
                  }}
                  className="block text-4xl md:text-5xl lg:text-6xl responsive-title font-bold uppercase tracking-tight leading-none luxury-glow-text mobile-luxury-glow luxury-typography"
                  style={{
                    overflow: 'hidden',
                    fontFamily: 'Cormorant Garamond, serif',
                    color: '#F2EEE6',
                    textShadow: '0 0 20px rgba(212, 194, 164, 0.4)'
                  }}
                >
                  <div style={{ transform: 'translateY(0%)' }}>
                    {tour.title}
                  </div>
                </div>

                {/* Premium Package Description */}
                <div
                  ref={(el) => {
                    if (!textRefs.current[index]) textRefs.current[index] = [];
                    textRefs.current[index][1] = el;
                  }}
                  className="text-sm md:text-lg lg:text-xl responsive-description mb-4 responsive-mb luxury-typography"
                  style={{
                    overflow: 'hidden',
                    fontFamily: 'Open Sans, sans-serif',
                    color: '#F2EEE6',
                    opacity: 0.9
                  }}
                >
                  <div style={{ transform: 'translateY(0%)' }}>
                    {tour.description}
                  </div>
                </div>

                {/* VIP Pricing & Duration Container */}
                <div
                  ref={(el) => {
                    if (!textRefs.current[index]) textRefs.current[index] = [];
                    textRefs.current[index][2] = el;
                  }}
                  className="flex items-center gap-6 responsive-gap text-sm md:text-base responsive-pricing mb-6 responsive-mb-lg luxury-typography"
                  style={{
                    overflow: 'hidden',
                    fontFamily: 'Open Sans, sans-serif'
                  }}
                >
                  <div style={{ transform: 'translateY(0%)' }} className="flex items-center gap-4">
                    <span
                      className="luxury-badge px-4 py-2 rounded-full border"
                      style={{
                        background: 'linear-gradient(135deg, #D4C2A4 0%, #C4B294 100%)',
                        color: '#16191D',
                        fontWeight: '600',
                        borderColor: 'rgba(212, 194, 164, 0.4)',
                        boxShadow: '0 4px 15px rgba(212, 194, 164, 0.3)'
                      }}
                    >
                      {tour.duration}
                    </span>
                    <span
                      className="text-2xl responsive-price-value font-bold luxury-glow-text mobile-luxury-glow"
                      style={{
                        color: '#D4C2A4',
                        textShadow: '0 0 15px rgba(212, 194, 164, 0.5)'
                      }}
                    >
                      ${tour.price}
                    </span>
                  </div>
                </div>

                {/* Premium VIP Highlights */}
                <div
                  ref={(el) => {
                    if (!textRefs.current[index]) textRefs.current[index] = [];
                    textRefs.current[index][3] = el;
                  }}
                  className="flex flex-wrap gap-3 responsive-gap mb-8 responsive-mb-lg luxury-typography"
                  style={{
                    overflow: 'hidden',
                    fontFamily: 'Open Sans, sans-serif'
                  }}
                >
                  <div style={{ transform: 'translateY(0%)' }}>
                    {tour.activities.slice(0, 3).map((activity, i) => (
                      <span
                        key={i}
                        className="text-xs sm:text-sm md:text-base responsive-activities   px-4 py-2 responsive-px responsive-py rounded-full mr-2 mb-2"
                        style={{
                          color: '#F2EEE6',
                          border: '1px solid rgba(212, 194, 164, 0.4)',
                          fontWeight: '500'
                        }}
                      >
                        {activity}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Premium VIP Book Now Button */}
                <div
                  ref={(el) => {
                    if (!textRefs.current[index]) textRefs.current[index] = [];
                    textRefs.current[index][4] = el;
                  }}
                  style={{ overflow: 'hidden' }}
                >
                  <button
                    onClick={handleBookNow}
                    className="luxury-button mobile-button-luxury mobile-touch-target font-bold py-4 px-10 text-lg responsive-button-size luxury-typography"
                    style={{
                      transform: 'translateY(0%)',
                      fontFamily: 'Open Sans, sans-serif',
                      color: '#16191D',
                      fontWeight: '700',
                      letterSpacing: '0.05em',
                      textTransform: 'uppercase',
                      borderRadius: '8px'
                    }}
                  >
                    Reserve Your VIP Experience
                  </button>
                </div>

                {/* VIP Slide Counter - Below button on all screen sizes, fixed at one point */}
                <div
                  className="fixed-counter flex items-center justify-center text-lg font-bold tracking-wider responsive-counter-size luxury-typography"
                  style={{
                    fontFamily: 'Open Sans, sans-serif',
                    color: '#F2EEE6'
                  }}
                >
                  <div className="relative min-w-8 h-6 overflow-hidden text-right">
                    <div
                      ref={counterStripRef}
                      className="absolute top-0 left-0 w-full text-right will-change-transform"
                    >
                      {tours.map((_, index) => (
                        <div key={index} className="h-6 block luxury-glow-text mobile-luxury-glow">
                          {formatNumber(index + 1)}
                        </div>
                      ))}
                    </div>
                  </div>
                  <div
                    className="w-12 h-px mx-4"
                    style={{
                      background: 'linear-gradient(90deg, transparent, #D4C2A4, transparent)',
                      boxShadow: '0 0 10px rgba(212, 194, 164, 0.4)'
                    }}
                  ></div>
                  <div className="min-w-8 text-left luxury-glow-text mobile-luxury-glow">
                    {formatNumber(tours.length)}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default FeaturedTours;
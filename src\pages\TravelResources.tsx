
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Shield,
  Thermometer,
  Camera,
  Backpack,
  Heart,
  Globe,
  FileText,
  AlertTriangle,
  CheckCircle,
  Users,
  Mountain,
  Binoculars,
  Star,
  Award,
  BookOpen
} from 'lucide-react';

const TravelResources = () => {
  const [selectedPackingList, setSelectedPackingList] = useState('safari');

  const visaRequirements = [
    {
      country: 'United States',
      requirement: 'Visa Required',
      processing: '5-10 business days',
      validity: '90 days',
      cost: '$50',
      documents: ['Passport', 'Application form', 'Photo', 'Flight itinerary']
    },
    {
      country: 'United Kingdom',
      requirement: 'Visa Required',
      processing: '5-10 business days',
      validity: '90 days',
      cost: '$50',
      documents: ['Passport', 'Application form', 'Photo', 'Hotel booking']
    },
    {
      country: 'European Union',
      requirement: 'Visa Required',
      processing: '5-10 business days',
      validity: '90 days',
      cost: '$50',
      documents: ['Passport', 'Application form', 'Photo', 'Travel insurance']
    },
    {
      country: 'Canada',
      requirement: 'Visa Required',
      processing: '5-10 business days',
      validity: '90 days',
      cost: '$50',
      documents: ['Passport', 'Application form', 'Photo', 'Return ticket']
    }
  ];

  const vaccinations = [
    {
      vaccine: 'Yellow Fever',
      required: true,
      timing: '10 days before travel',
      validity: 'Lifetime',
      description: 'Required for entry from certain countries'
    },
    {
      vaccine: 'Hepatitis A',
      required: true,
      timing: '2 weeks before travel',
      validity: '20+ years',
      description: 'Protection against contaminated food/water'
    },
    {
      vaccine: 'Typhoid',
      required: true,
      timing: '1-2 weeks before travel',
      validity: '3 years',
      description: 'Recommended for all travelers'
    },
    {
      vaccine: 'Malaria Prevention',
      required: true,
      timing: '1-2 weeks before travel',
      validity: 'During stay',
      description: 'Anti-malarial medication required'
    },
    {
      vaccine: 'Hepatitis B',
      required: false,
      timing: '1 month before travel',
      validity: 'Lifetime',
      description: 'For extended stays or medical procedures'
    },
    {
      vaccine: 'Meningitis',
      required: false,
      timing: '2 weeks before travel',
      validity: '3-5 years',
      description: 'Recommended for dry season travel'
    }
  ];

  const packingLists = {
    safari: {
      title: 'Safari Essentials',
      categories: [
        {
          name: 'Clothing',
          items: [
            'Neutral colored clothing (khaki, beige, olive)',
            'Long-sleeved shirts (sun protection)',
            'Lightweight pants',
            'Wide-brimmed hat',
            'Light jacket for early morning drives',
            'Comfortable walking shoes',
            'Sandals for camp',
            'Warm socks'
          ]
        },
        {
          name: 'Photography Equipment',
          items: [
            'Camera with telephoto lens (200mm+)',
            'Extra batteries and memory cards',
            'Lens cleaning kit',
            'Binoculars (8x42 recommended)',
            'Tripod or monopod',
            'Dust-proof camera bag'
          ]
        },
        {
          name: 'Personal Items',
          items: [
            'Sunscreen (SPF 30+)',
            'Insect repellent',
            'Personal medications',
            'Hand sanitizer',
            'Wet wipes',
            'Flashlight or headlamp',
            'Power bank',
            'Travel adapter'
          ]
        }
      ]
    },
    kilimanjaro: {
      title: 'Kilimanjaro Trekking',
      categories: [
        {
          name: 'Clothing Layers',
          items: [
            'Base layers (moisture-wicking)',
            'Insulating layers (fleece/down)',
            'Waterproof jacket and pants',
            'Trekking pants',
            'Warm hat and sun hat',
            'Waterproof gloves',
            'Hiking socks (wool/synthetic)',
            'Gaiters'
          ]
        },
        {
          name: 'Footwear',
          items: [
            'Waterproof hiking boots',
            'Camp shoes (lightweight)',
            'Crampons (if climbing in ice season)',
            'Trekking poles'
          ]
        },
        {
          name: 'Equipment',
          items: [
            'Sleeping bag (rated to -10°C)',
            'Sleeping pad',
            'Daypack (30-40L)',
            'Headlamp with extra batteries',
            'Water bottles/hydration system',
            'Water purification tablets'
          ]
        }
      ]
    },
    cultural: {
      title: 'Cultural Tours',
      categories: [
        {
          name: 'Appropriate Clothing',
          items: [
            'Modest clothing (covering shoulders/knees)',
            'Comfortable walking shoes',
            'Light scarf or shawl',
            'Sun protection',
            'Casual evening wear'
          ]
        },
        {
          name: 'Cultural Interaction',
          items: [
            'Small gifts for children (pens, notebooks)',
            'Translation app or phrasebook',
            'Respectful attitude',
            'Open mind and curiosity'
          ]
        }
      ]
    }
  };

  const weatherSeasons = [
    {
      season: 'Dry Season',
      months: 'June - October',
      temperature: '20-28°C (68-82°F)',
      rainfall: 'Very low',
      wildlife: 'Excellent wildlife viewing',
      advantages: [
        'Clear skies for photography',
        'Animals gather at water sources',
        'Less vegetation for better visibility',
        'Ideal road conditions'
      ],
      considerations: [
        'Peak season - higher prices',
        'More tourists',
        'Dusty conditions'
      ]
    },
    {
      season: 'Short Rains',
      months: 'November - December',
      temperature: '22-30°C (72-86°F)',
      rainfall: 'Moderate',
      wildlife: 'Good wildlife viewing',
      advantages: [
        'Green landscapes',
        'Fewer tourists',
        'Calving season begins',
        'Lower prices'
      ],
      considerations: [
        'Occasional afternoon showers',
        'Some roads may be muddy'
      ]
    },
    {
      season: 'Long Rains',
      months: 'March - May',
      temperature: '20-26°C (68-79°F)',
      rainfall: 'High',
      wildlife: 'Challenging wildlife viewing',
      advantages: [
        'Lowest prices',
        'Lush green scenery',
        'Great for photography',
        'Fewer crowds'
      ],
      considerations: [
        'Heavy rainfall',
        'Some camps may close',
        'Difficult road conditions',
        'Dense vegetation'
      ]
    }
  ];

  const culturalEtiquette = [
    {
      title: 'Greetings',
      description: 'Use both hands when greeting elders. "Jambo" (hello) is widely understood.',
      icon: Users
    },
    {
      title: 'Photography',
      description: 'Always ask permission before photographing people, especially in villages.',
      icon: Camera
    },
    {
      title: 'Dress Code',
      description: 'Dress modestly, especially when visiting villages or religious sites.',
      icon: Shield
    },
    {
      title: 'Gift Giving',
      description: 'Small educational gifts for children are appreciated, but avoid encouraging begging.',
      icon: Heart
    },
    {
      title: 'Respect',
      description: 'Show respect for local customs and traditions. Ask questions with genuine interest.',
      icon: CheckCircle
    }
  ];

  return (
    <div className="min-h-screen bg-[#16191D] luxury-scrollbar">
      <Header />
      <main className="">
        {/* Luxury Hero Section - Mobile Responsive */}
        <div className="relative overflow-hidden bg-[#16191D] pt-20 sm:pt-24 h-[70vh] sm:h-[80vh] md:min-h-screen">
          {/* Elegant Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                               radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
              backgroundSize: '40px 40px'
            }} />
          </div>

          {/* Hero Content */}
          <div className="relative z-10 container mx-auto px-4 py-12 sm:py-16 md:py-20 lg:py-24 text-center">
            <div className="max-w-4xl mx-auto">
              {/* Luxury Badge */}
              <div className="inline-flex items-center gap-1.5 sm:gap-2 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-3 sm:px-4 md:px-6 py-1.5 sm:py-2 mb-6 sm:mb-8">
                <BookOpen className="w-3 h-3 sm:w-4 sm:h-4 text-[#D4C2A4] animate-pulse" />
                <span className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wider uppercase">Essential Travel Resources</span>
              </div>

              <h1 className="font-cormorant text-3xl sm:text-4xl md:text-5xl lg:text-7xl xl:text-8xl text-[#F2EEE6] mb-4 sm:mb-6 leading-tight">
                <span className="block text-[#F2EEE6]">Travel</span>
                <span className="block text-[#D4C2A4] italic">Resources</span>
              </h1>

              <p className="font-open-sans text-sm sm:text-base md:text-lg lg:text-xl text-[#F2EEE6]/80 max-w-xl sm:max-w-2xl mx-auto leading-relaxed mb-6 sm:mb-8 px-2">
                Everything you need to prepare for your extraordinary Tanzania safari adventure.
                <span className="hidden sm:inline"> From visa requirements to packing essentials, cultural insights to health preparations.</span>
              </p>

              {/* Elegant Divider */}
              <div className="flex items-center justify-center gap-2 sm:gap-4 mb-8 sm:mb-12">
                <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent w-16 sm:w-24"></div>
                <div className="w-4 h-4 sm:w-6 sm:h-6 bg-[#D4C2A4] rounded-full"></div>
                <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent w-16 sm:w-24"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Luxury Main Content - Mobile Responsive */}
        <div className="relative bg-[#16191D] py-12 sm:py-16 md:py-20 lg:py-32">
          {/* Elegant Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                               radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
              backgroundSize: '50px 50px'
            }}></div>
          </div>

          <div className="container mx-auto px-4 sm:px-6 relative z-10">
            <Tabs defaultValue="visa" className="w-full">
              {/* Luxury Tab Navigation - Mobile Responsive */}
              <div className="flex justify-center mb-8 sm:mb-12 lg:mb-16">
                <div className="w-full max-w-4xl">
                  {/* Mobile: Vertical Stack */}
                  <div className="sm:hidden">
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/10 rounded-lg p-2">
                      <TabsList className="bg-transparent border-none grid grid-cols-1 gap-2 h-auto w-full">
                        <TabsTrigger
                          value="visa"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-sm py-3 px-4 rounded-lg transition-all duration-300 hover:bg-[#D4C2A4]/10 w-full"
                        >
                          Visa & Entry
                        </TabsTrigger>
                        <TabsTrigger
                          value="health"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-sm py-3 px-4 rounded-lg transition-all duration-300 hover:bg-[#D4C2A4]/10 w-full"
                        >
                          Health & Safety
                        </TabsTrigger>
                        <TabsTrigger
                          value="packing"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-sm py-3 px-4 rounded-lg transition-all duration-300 hover:bg-[#D4C2A4]/10 w-full"
                        >
                          Packing Guide
                        </TabsTrigger>
                        <TabsTrigger
                          value="weather"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-sm py-3 px-4 rounded-lg transition-all duration-300 hover:bg-[#D4C2A4]/10 w-full"
                        >
                          Weather Guide
                        </TabsTrigger>
                        <TabsTrigger
                          value="culture"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-sm py-3 px-4 rounded-lg transition-all duration-300 hover:bg-[#D4C2A4]/10 w-full"
                        >
                          Cultural Tips
                        </TabsTrigger>
                      </TabsList>
                    </div>
                  </div>

                  {/* Desktop: Horizontal */}
                  <div className="hidden sm:flex justify-center">
                    <div className="inline-flex bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/10 rounded-full p-2">
                      <TabsList className="bg-transparent border-none grid grid-cols-5 gap-2 h-auto">
                        <TabsTrigger
                          value="visa"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm py-2 sm:py-3 px-3 sm:px-6 rounded-full transition-all duration-300 hover:bg-[#D4C2A4]/10"
                        >
                          <span className="hidden lg:inline">Visa & Entry</span>
                          <span className="lg:hidden">Visa</span>
                        </TabsTrigger>
                        <TabsTrigger
                          value="health"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm py-2 sm:py-3 px-3 sm:px-6 rounded-full transition-all duration-300 hover:bg-[#D4C2A4]/10"
                        >
                          <span className="hidden lg:inline">Health & Safety</span>
                          <span className="lg:hidden">Health</span>
                        </TabsTrigger>
                        <TabsTrigger
                          value="packing"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm py-2 sm:py-3 px-3 sm:px-6 rounded-full transition-all duration-300 hover:bg-[#D4C2A4]/10"
                        >
                          <span className="hidden lg:inline">Packing Guide</span>
                          <span className="lg:hidden">Packing</span>
                        </TabsTrigger>
                        <TabsTrigger
                          value="weather"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm py-2 sm:py-3 px-3 sm:px-6 rounded-full transition-all duration-300 hover:bg-[#D4C2A4]/10"
                        >
                          <span className="hidden lg:inline">Weather Guide</span>
                          <span className="lg:hidden">Weather</span>
                        </TabsTrigger>
                        <TabsTrigger
                          value="culture"
                          className="data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm py-2 sm:py-3 px-3 sm:px-6 rounded-full transition-all duration-300 hover:bg-[#D4C2A4]/10"
                        >
                          <span className="hidden lg:inline">Cultural Tips</span>
                          <span className="lg:hidden">Culture</span>
                        </TabsTrigger>
                      </TabsList>
                    </div>
                  </div>
                </div>
              </div>

            {/* Luxury Visa Requirements - Mobile Responsive */}
            <TabsContent value="visa" className="space-y-8 sm:space-y-12">
              {/* Elegant Section Header - Mobile Responsive */}
              <div className="text-center mb-8 sm:mb-12 lg:mb-16">
                <h2 className="font-['Cormorant_Garamond'] text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-light text-[#F2EEE6] mb-4 sm:mb-6">
                  Visa & Entry Requirements
                </h2>
                <div className="w-16 sm:w-24 h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent mx-auto mb-4 sm:mb-8"></div>
                <p className="font-['Open_Sans'] text-sm sm:text-base lg:text-lg text-[#F2EEE6]/80 max-w-3xl mx-auto leading-relaxed px-4">
                  Seamless entry into Tanzania begins with proper documentation. Our comprehensive guide ensures
                  your journey starts without complications.
                </p>
              </div>

              {/* Luxury Visa Cards - Mobile Responsive */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
                {visaRequirements.map((visa, index) => (
                  <div key={index} className="group">
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/40 hover:shadow-2xl hover:shadow-[#D4C2A4]/10 transform hover:-translate-y-2">
                      {/* Card Header - Mobile Responsive */}
                      <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0">
                          <Globe className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                        </div>
                        <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                          {visa.country}
                        </h3>
                      </div>

                      {/* Card Content - Mobile Responsive */}
                      <div className="space-y-4 sm:space-y-6">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 sm:py-3 border-b border-[#D4C2A4]/10 gap-2 sm:gap-0">
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm">Requirement</span>
                          <span className="px-2 sm:px-3 py-1 bg-[#D4C2A4]/20 text-[#D4C2A4] font-['Open_Sans'] text-xs font-medium rounded-full self-start sm:self-auto">
                            {visa.requirement}
                          </span>
                        </div>
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 sm:py-3 border-b border-[#D4C2A4]/10 gap-2 sm:gap-0">
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm">Processing Time</span>
                          <span className="font-['Open_Sans'] text-[#F2EEE6] text-xs sm:text-sm font-medium">{visa.processing}</span>
                        </div>
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 sm:py-3 border-b border-[#D4C2A4]/10 gap-2 sm:gap-0">
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm">Validity</span>
                          <span className="font-['Open_Sans'] text-[#F2EEE6] text-xs sm:text-sm font-medium">{visa.validity}</span>
                        </div>
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 sm:py-3 border-b border-[#D4C2A4]/10 gap-2 sm:gap-0">
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm">Cost</span>
                          <span className="font-['Open_Sans'] text-[#D4C2A4] text-xs sm:text-sm font-semibold">{visa.cost}</span>
                        </div>

                        {/* Required Documents - Mobile Responsive */}
                        <div className="pt-2 sm:pt-4">
                          <h4 className="font-['Open_Sans'] text-[#F2EEE6] text-xs sm:text-sm font-medium mb-3 sm:mb-4">Required Documents</h4>
                          <div className="space-y-2 sm:space-y-3">
                            {visa.documents.map((doc, i) => (
                              <div key={i} className="flex items-start gap-2 sm:gap-3">
                                <div className="w-4 h-4 sm:w-5 sm:h-5 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                  <CheckCircle className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-[#D4C2A4]" />
                                </div>
                                <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">{doc}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Luxury Important Notes - Mobile Responsive */}
              <div className="bg-gradient-to-r from-[#D4C2A4]/10 to-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                <div className="flex flex-col sm:flex-row items-start gap-3 sm:gap-4">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                  </div>
                  <div className="w-full">
                    <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6] mb-4 sm:mb-6">Important Considerations</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                      <div className="space-y-2 sm:space-y-3">
                        <div className="flex items-start gap-2 sm:gap-3">
                          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full mt-2 flex-shrink-0"></div>
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">Visa on arrival available at major entry points</span>
                        </div>
                        <div className="flex items-start gap-2 sm:gap-3">
                          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full mt-2 flex-shrink-0"></div>
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">Passport validity minimum 6 months required</span>
                        </div>
                      </div>
                      <div className="space-y-2 sm:space-y-3">
                        <div className="flex items-start gap-2 sm:gap-3">
                          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full mt-2 flex-shrink-0"></div>
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">Yellow fever certificate may be mandatory</span>
                        </div>
                        <div className="flex items-start gap-2 sm:gap-3">
                          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full mt-2 flex-shrink-0"></div>
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">Maintain copies of all essential documents</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Luxury Health & Safety - Mobile Responsive */}
            <TabsContent value="health" className="space-y-8 sm:space-y-12">
              {/* Elegant Section Header - Mobile Responsive */}
              <div className="text-center mb-8 sm:mb-12 lg:mb-16">
                <h2 className="font-['Cormorant_Garamond'] text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-light text-[#F2EEE6] mb-4 sm:mb-6">
                  Health & Safety Excellence
                </h2>
                <div className="w-16 sm:w-24 h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent mx-auto mb-4 sm:mb-8"></div>
                <p className="font-['Open_Sans'] text-sm sm:text-base lg:text-lg text-[#F2EEE6]/80 max-w-3xl mx-auto leading-relaxed px-4">
                  Your wellbeing is paramount. Comprehensive health guidance ensures your Tanzania adventure
                  remains both thrilling and secure.
                </p>
              </div>

              {/* Vaccinations Section - Mobile Responsive */}
              <div className="mb-8 sm:mb-12 lg:mb-16">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mb-8 sm:mb-12">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Shield className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-[#D4C2A4]" />
                  </div>
                  <h3 className="font-['Cormorant_Garamond'] text-xl sm:text-2xl md:text-3xl lg:text-4xl font-light text-[#F2EEE6]">
                    Essential Vaccinations
                  </h3>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                  {vaccinations.map((vaccine, index) => (
                    <div key={index} className="group">
                      <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/40 hover:shadow-xl hover:shadow-[#D4C2A4]/10 transform hover:-translate-y-1">
                        {/* Vaccine Header - Mobile Responsive */}
                        <div className="flex flex-col sm:flex-row items-start justify-between mb-4 sm:mb-6 gap-3 sm:gap-0">
                          <h4 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6]">
                            {vaccine.vaccine}
                          </h4>
                          <span className={`px-2 sm:px-3 py-1 rounded-full text-xs font-['Open_Sans'] font-medium self-start ${
                            vaccine.required
                              ? "bg-red-500/20 text-red-400 border border-red-500/30"
                              : "bg-[#D4C2A4]/20 text-[#D4C2A4] border border-[#D4C2A4]/30"
                          }`}>
                            {vaccine.required ? "Required" : "Recommended"}
                          </span>
                        </div>

                        {/* Vaccine Details - Mobile Responsive */}
                        <div className="space-y-3 sm:space-y-4">
                          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 border-b border-[#D4C2A4]/10 gap-1 sm:gap-0">
                            <span className="font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm">Timing</span>
                            <span className="font-['Open_Sans'] text-[#F2EEE6] text-xs sm:text-sm">{vaccine.timing}</span>
                          </div>
                          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 border-b border-[#D4C2A4]/10 gap-1 sm:gap-0">
                            <span className="font-['Open_Sans'] text-[#F2EEE6]/70 text-xs sm:text-sm">Validity</span>
                            <span className="font-['Open_Sans'] text-[#D4C2A4] text-xs sm:text-sm font-medium">{vaccine.validity}</span>
                          </div>
                          <p className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed pt-1 sm:pt-2">
                            {vaccine.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Health Tips Section - Mobile Responsive */}
              <div className="bg-gradient-to-r from-[#D4C2A4]/10 to-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Heart className="h-5 w-5 sm:h-6 sm:w-6 text-[#D4C2A4]" />
                  </div>
                  <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6]">
                    Wellness Guidelines
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
                  <div className="space-y-3 sm:space-y-4">
                    <h4 className="font-['Open_Sans'] text-[#D4C2A4] text-xs sm:text-sm font-medium uppercase tracking-wider mb-3 sm:mb-4">
                      Essential Precautions
                    </h4>
                    <div className="space-y-2 sm:space-y-3">
                      {[
                        "Consume only bottled or properly boiled water",
                        "Apply insect repellent consistently throughout the day",
                        "Utilize mosquito nets during all sleeping hours",
                        "Maintain rigorous hand hygiene practices"
                      ].map((tip, index) => (
                        <div key={index} className="flex items-start gap-2 sm:gap-3">
                          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full mt-2 flex-shrink-0"></div>
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-3 sm:space-y-4">
                    <h4 className="font-['Open_Sans'] text-[#D4C2A4] text-xs sm:text-sm font-medium uppercase tracking-wider mb-3 sm:mb-4">
                      Additional Recommendations
                    </h4>
                    <div className="space-y-2 sm:space-y-3">
                      {[
                        "Avoid consuming raw or inadequately cooked foods",
                        "Carry a comprehensive first aid kit",
                        "Secure comprehensive travel insurance coverage",
                        "Schedule consultation with travel medicine specialist"
                      ].map((tip, index) => (
                        <div key={index} className="flex items-start gap-2 sm:gap-3">
                          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full mt-2 flex-shrink-0"></div>
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Luxury Packing Guide - Mobile Responsive */}
            <TabsContent value="packing" className="space-y-8 sm:space-y-12">
              {/* Elegant Section Header - Mobile Responsive */}
              <div className="text-center mb-8 sm:mb-12 lg:mb-16">
                <h2 className="font-['Cormorant_Garamond'] text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-light text-[#F2EEE6] mb-4 sm:mb-6">
                  Curated Packing Guides
                </h2>
                <div className="w-16 sm:w-24 h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent mx-auto mb-4 sm:mb-8"></div>
                <p className="font-['Open_Sans'] text-sm sm:text-base lg:text-lg text-[#F2EEE6]/80 max-w-3xl mx-auto leading-relaxed px-4">
                  Meticulously crafted packing essentials tailored to your specific adventure,
                  ensuring you're perfectly prepared for every moment.
                </p>
              </div>

              {/* Luxury Adventure Selector - Mobile Responsive */}
              <div className="flex justify-center mb-8 sm:mb-12 lg:mb-16">
                {/* Mobile: Vertical Stack */}
                <div className="w-full max-w-md sm:hidden">
                  <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-2 space-y-2">
                    {Object.entries(packingLists).map(([key, list]) => (
                      <button
                        key={key}
                        onClick={() => setSelectedPackingList(key)}
                        className={`group flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300 w-full ${
                          selectedPackingList === key
                            ? "bg-[#D4C2A4] text-[#16191D]"
                            : "text-[#D4C2A4] hover:bg-[#D4C2A4]/10"
                        }`}
                      >
                        {key === 'safari' && <Binoculars className="h-4 w-4" />}
                        {key === 'kilimanjaro' && <Mountain className="h-4 w-4" />}
                        {key === 'cultural' && <Users className="h-4 w-4" />}
                        <span className="font-['Open_Sans'] text-sm font-medium">{list.title}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Desktop: Horizontal */}
                <div className="hidden sm:block">
                  <div className="inline-flex bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-full p-2">
                    {Object.entries(packingLists).map(([key, list]) => (
                      <button
                        key={key}
                        onClick={() => setSelectedPackingList(key)}
                        className={`group flex items-center gap-2 sm:gap-3 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 rounded-full transition-all duration-300 ${
                          selectedPackingList === key
                            ? "bg-[#D4C2A4] text-[#16191D]"
                            : "text-[#D4C2A4] hover:bg-[#D4C2A4]/10"
                        }`}
                      >
                        {key === 'safari' && <Binoculars className="h-3 w-3 sm:h-4 sm:w-4" />}
                        {key === 'kilimanjaro' && <Mountain className="h-3 w-3 sm:h-4 sm:w-4" />}
                        {key === 'cultural' && <Users className="h-3 w-3 sm:h-4 sm:w-4" />}
                        <span className="font-['Open_Sans'] text-xs sm:text-sm font-medium">{list.title}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Selected Guide Content - Mobile Responsive */}
              <div>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mb-8 sm:mb-12">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Backpack className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-[#D4C2A4]" />
                  </div>
                  <h3 className="font-['Cormorant_Garamond'] text-xl sm:text-2xl md:text-3xl lg:text-4xl font-light text-[#F2EEE6]">
                    {packingLists[selectedPackingList as keyof typeof packingLists].title}
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                  {packingLists[selectedPackingList as keyof typeof packingLists].categories.map((category, index) => (
                    <div key={index} className="group">
                      <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/40 hover:shadow-xl hover:shadow-[#D4C2A4]/10 transform hover:-translate-y-1">
                        {/* Category Header - Mobile Responsive */}
                        <h4 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6] mb-4 sm:mb-6 pb-2 sm:pb-3 border-b border-[#D4C2A4]/20">
                          {category.name}
                        </h4>

                        {/* Category Items - Mobile Responsive */}
                        <div className="space-y-2 sm:space-y-3">
                          {category.items.map((item, i) => (
                            <div key={i} className="flex items-start gap-2 sm:gap-3 group/item">
                              <div className="w-4 h-4 sm:w-5 sm:h-5 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 group-hover/item:bg-[#D4C2A4]/30 transition-colors duration-200">
                                <CheckCircle className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-[#D4C2A4]" />
                              </div>
                              <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed group-hover/item:text-[#F2EEE6] transition-colors duration-200">
                                {item}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Luxury Weather Guide - Mobile Responsive */}
            <TabsContent value="weather" className="space-y-8 sm:space-y-12">
              {/* Elegant Section Header - Mobile Responsive */}
              <div className="text-center mb-8 sm:mb-12 lg:mb-16">
                <h2 className="font-['Cormorant_Garamond'] text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-light text-[#F2EEE6] mb-4 sm:mb-6">
                  Seasonal Excellence Guide
                </h2>
                <div className="w-16 sm:w-24 h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent mx-auto mb-4 sm:mb-8"></div>
                <p className="font-['Open_Sans'] text-sm sm:text-base lg:text-lg text-[#F2EEE6]/80 max-w-3xl mx-auto leading-relaxed px-4">
                  Master Tanzania's seasonal rhythms to orchestrate the perfect timing for your extraordinary adventure,
                  where nature's calendar becomes your guide to unparalleled experiences.
                </p>
              </div>

              {/* Luxury Season Cards - Mobile Responsive */}
              <div className="space-y-6 sm:space-y-8">
                {weatherSeasons.map((season, index) => (
                  <div key={index} className="group">
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg overflow-hidden transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/40 hover:shadow-2xl hover:shadow-[#D4C2A4]/10 transform hover:-translate-y-2">
                      {/* Season Header - Mobile Responsive */}
                      <div className="p-4 sm:p-6 lg:p-8 border-b border-[#D4C2A4]/20">
                        <div className="flex flex-col sm:flex-row lg:items-center gap-3 sm:gap-4">
                          <div className="flex items-center gap-3 sm:gap-4">
                            <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0">
                              <Thermometer className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-[#D4C2A4]" />
                            </div>
                            <h3 className="font-['Cormorant_Garamond'] text-xl sm:text-2xl lg:text-3xl font-medium text-[#F2EEE6]">
                              {season.season}
                            </h3>
                          </div>
                          <div className="sm:ml-auto">
                            <span className="inline-block px-3 sm:px-4 py-1 sm:py-2 bg-[#D4C2A4]/20 text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm font-medium rounded-full">
                              {season.months}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Season Content - Mobile Responsive */}
                      <div className="p-4 sm:p-6 lg:p-8">
                        {/* Weather Stats - Mobile Responsive */}
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
                          <div className="text-center p-3 sm:p-4 bg-[#D4C2A4]/5 rounded-lg border border-[#D4C2A4]/10">
                            <span className="font-['Open_Sans'] text-[#D4C2A4] text-xs font-medium uppercase tracking-wider block mb-1 sm:mb-2">
                              Temperature
                            </span>
                            <p className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6]">
                              {season.temperature}
                            </p>
                          </div>
                          <div className="text-center p-3 sm:p-4 bg-[#D4C2A4]/5 rounded-lg border border-[#D4C2A4]/10">
                            <span className="font-['Open_Sans'] text-[#D4C2A4] text-xs font-medium uppercase tracking-wider block mb-1 sm:mb-2">
                              Rainfall
                            </span>
                            <p className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6]">
                              {season.rainfall}
                            </p>
                          </div>
                          <div className="text-center p-3 sm:p-4 bg-[#D4C2A4]/5 rounded-lg border border-[#D4C2A4]/10 sm:col-span-1 col-span-1">
                            <span className="font-['Open_Sans'] text-[#D4C2A4] text-xs font-medium uppercase tracking-wider block mb-1 sm:mb-2">
                              Wildlife Viewing
                            </span>
                            <p className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6]">
                              {season.wildlife}
                            </p>
                          </div>
                        </div>

                        {/* Advantages & Considerations - Mobile Responsive */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
                          <div>
                            <h4 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#D4C2A4] mb-4 sm:mb-6">
                              Seasonal Advantages
                            </h4>
                            <div className="space-y-2 sm:space-y-3">
                              {season.advantages.map((advantage, i) => (
                                <div key={i} className="flex items-start gap-2 sm:gap-3">
                                  <div className="w-4 h-4 sm:w-5 sm:h-5 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <CheckCircle className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-green-400" />
                                  </div>
                                  <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">
                                    {advantage}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>

                          <div>
                            <h4 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#D4C2A4] mb-4 sm:mb-6">
                              Important Considerations
                            </h4>
                            <div className="space-y-2 sm:space-y-3">
                              {season.considerations.map((consideration, i) => (
                                <div key={i} className="flex items-start gap-2 sm:gap-3">
                                  <div className="w-4 h-4 sm:w-5 sm:h-5 bg-orange-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <AlertTriangle className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-orange-400" />
                                  </div>
                                  <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">
                                    {consideration}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Luxury Cultural Tips - Mobile Responsive */}
            <TabsContent value="culture" className="space-y-8 sm:space-y-12">
              {/* Elegant Section Header - Mobile Responsive */}
              <div className="text-center mb-8 sm:mb-12 lg:mb-16">
                <h2 className="font-['Cormorant_Garamond'] text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-light text-[#F2EEE6] mb-4 sm:mb-6">
                  Cultural Sophistication
                </h2>
                <div className="w-16 sm:w-24 h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent mx-auto mb-4 sm:mb-8"></div>
                <p className="font-['Open_Sans'] text-sm sm:text-base lg:text-lg text-[#F2EEE6]/80 max-w-3xl mx-auto leading-relaxed px-4">
                  Embrace the rich tapestry of Tanzanian culture with grace and respect,
                  creating meaningful connections that transcend the ordinary tourist experience.
                </p>
              </div>

              {/* Cultural Etiquette Cards - Mobile Responsive */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-12 lg:mb-16">
                {culturalEtiquette.map((tip, index) => (
                  <div key={index} className="group">
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8 text-center transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/40 hover:shadow-xl hover:shadow-[#D4C2A4]/10 transform hover:-translate-y-2">
                      {/* Icon - Mobile Responsive */}
                      <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-[#D4C2A4]/30 transition-colors duration-300">
                        <tip.icon className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-[#D4C2A4]" />
                      </div>

                      {/* Title - Mobile Responsive */}
                      <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6] mb-3 sm:mb-4">
                        {tip.title}
                      </h3>

                      {/* Description - Mobile Responsive */}
                      <p className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">
                        {tip.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Additional Cultural Insights - Mobile Responsive */}
              <div className="bg-gradient-to-r from-[#D4C2A4]/10 to-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-lg p-4 sm:p-6 lg:p-8">
                <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl lg:text-2xl font-medium text-[#F2EEE6] mb-6 sm:mb-8 text-center">
                  Essential Cultural Insights
                </h3>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
                  <div className="space-y-3 sm:space-y-4">
                    <h4 className="font-['Open_Sans'] text-[#D4C2A4] text-xs sm:text-sm font-medium uppercase tracking-wider mb-3 sm:mb-4">
                      Social Customs
                    </h4>
                    <div className="space-y-2 sm:space-y-3">
                      {[
                        "Master essential Swahili greetings and expressions",
                        "Honor tradition by removing footwear in homes",
                        "Extend greetings exclusively with your right hand",
                        "Embrace the relaxed concept of 'African time'"
                      ].map((tip, index) => (
                        <div key={index} className="flex items-start gap-2 sm:gap-3">
                          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full mt-2 flex-shrink-0"></div>
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-3 sm:space-y-4">
                    <h4 className="font-['Open_Sans'] text-[#D4C2A4] text-xs sm:text-sm font-medium uppercase tracking-wider mb-3 sm:mb-4">
                      Respectful Practices
                    </h4>
                    <div className="space-y-2 sm:space-y-3">
                      {[
                        "Engage in friendly market negotiations with patience",
                        "Use open palm gestures instead of pointing fingers",
                        "Show reverence for diverse religious traditions",
                        "Support communities through responsible tourism"
                      ].map((tip, index) => (
                        <div key={index} className="flex items-start gap-2 sm:gap-3">
                          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full mt-2 flex-shrink-0"></div>
                          <span className="font-['Open_Sans'] text-[#F2EEE6]/80 text-xs sm:text-sm leading-relaxed">{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
          </div>
        </div>

        {/* Luxury CTA Section - Mobile Responsive */}
        <div className="relative bg-[#16191D] py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden">
          {/* Elegant Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-full h-full opacity-10">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 20% 80%, #D4C2A4 1px, transparent 1px),
                                 radial-gradient(circle at 80% 20%, #D4C2A4 1px, transparent 1px),
                                 radial-gradient(circle at 40% 40%, #D4C2A4 1px, transparent 1px)`,
                backgroundSize: '100px 100px'
              }}></div>
            </div>
          </div>

          <div className="container mx-auto px-4 sm:px-6 text-center relative z-10">
            {/* Elegant Title - Mobile Responsive */}
            <h2 className="font-['Cormorant_Garamond'] text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-light text-[#F2EEE6] mb-6 sm:mb-8">
              Personalized Travel
              <span className="block text-[#D4C2A4] italic">Concierge Service</span>
            </h2>

            {/* Sophisticated Description - Mobile Responsive */}
            <p className="font-['Open_Sans'] text-sm sm:text-base md:text-lg lg:text-xl text-[#F2EEE6]/80 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed px-2">
              Our distinguished travel specialists craft bespoke Tanzania experiences,
              orchestrating every detail with meticulous precision and unparalleled expertise.
            </p>

            {/* Luxury Action Buttons - Mobile Responsive */}
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center max-w-lg mx-auto">
              <Link
                to="/contact"
                className="group px-6 sm:px-8 lg:px-10 py-3 sm:py-4 bg-[#D4C2A4] text-[#16191D] font-['Open_Sans'] font-semibold text-xs sm:text-sm tracking-wide uppercase rounded-sm transition-all duration-500 hover:bg-[#F2EEE6] hover:shadow-2xl hover:shadow-[#D4C2A4]/20 transform hover:-translate-y-1 text-center"
              >
                <span className="flex items-center justify-center gap-2 sm:gap-3">
                  Contact Expert
                  <Users className="w-3 h-3 sm:w-4 sm:h-4 group-hover:scale-110 transition-transform duration-300" />
                </span>
              </Link>

              <a
                href="/travel-guide.pdf"
                download="Tanzania-Safari-Travel-Guide.pdf"
                className="group px-6 sm:px-8 lg:px-10 py-3 sm:py-4 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-xs sm:text-sm tracking-wide uppercase rounded-sm backdrop-blur-sm transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transform hover:-translate-y-1 text-center"
              >
                <span className="flex items-center justify-center gap-2 sm:gap-3">
                  Download Guide
                  <FileText className="w-3 h-3 sm:w-4 sm:h-4 group-hover:rotate-12 transition-transform duration-300" />
                </span>
              </a>
            </div>

            {/* Elegant Divider - Mobile Responsive */}
            <div className="mt-12 sm:mt-16 flex items-center justify-center">
              <div className="w-20 sm:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
              <div className="mx-3 sm:mx-4 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#D4C2A4] rounded-full"></div>
              <div className="w-20 sm:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
            </div>
          </div>
        </div>
      </main>
      <Footer isDarkBackground={true} />
    </div>
  );
};

export default TravelResources;

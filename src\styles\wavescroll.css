/* WaveScroll Component Styles - Luxury Premium Theme */

/* Luxury Container Styles */
.luxury-wave-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.luxury-wave-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, rgba(212, 194, 164, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 70%, rgba(212, 194, 164, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

/* Reset and base styles */
.ws-pages {
  overflow: hidden;
  position: relative;
  height: 100vh;
  min-height: 400px; /* Minimum height for very small screens */
  background: linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #000000 50%, #0a0a0a 75%, #000000 100%);
}

.ws-bgs {
  position: relative;
  height: 100%;
}

.ws-bg {
  display: flex;
  height: 100%;
  background-size: cover;
  background-position: center center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Luxury Background Overlay */
.luxury-bg-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(22, 25, 29, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 25%,
    rgba(212, 194, 164, 0.05) 50%,
    rgba(0, 0, 0, 0.1) 75%,
    rgba(22, 25, 29, 0.3) 100%
  );
  pointer-events: none;
  z-index: 2;
  transition: opacity 0.6s ease;
}

.luxury-bg-overlay:hover::after {
  opacity: 0.7;
}

/* Stack backgrounds vertically */
.ws-bg:nth-child(1) { top: 0; }
.ws-bg:nth-child(2) { top: 100vh; }
.ws-bg:nth-child(3) { top: 200vh; }
.ws-bg:nth-child(4) { top: 300vh; }
.ws-bg:nth-child(5) { top: 400vh; }

/* When ready, remove background from main containers */
.ws-pages.s--ready .ws-bg {
  background: none !important;
}

/* Luxury Background Parts Styling */
.ws-bg__part {
  overflow: hidden;
  position: relative;
  height: 100%;
  cursor: grab;
  user-select: none;
  float: left;
  touch-action: pan-y; /* Enable vertical panning on touch devices */
  -webkit-touch-callout: none; /* Disable iOS callout */
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight */
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-right: 1px solid rgba(212, 194, 164, 0.1);
}

.ws-bg__part:active {
  cursor: grabbing;
  transform: scale(1.02);
}

.ws-bg__part:hover {
  box-shadow: inset 0 0 20px rgba(212, 194, 164, 0.15);
}

/* Luxury part borders */
.ws-bg__part::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(212, 194, 164, 0.3) 20%,
    rgba(212, 194, 164, 0.6) 50%,
    rgba(212, 194, 164, 0.3) 80%,
    transparent 100%
  );
  z-index: 3;
}

/* Touch-friendly adjustments */
@media (max-width: 768px) {
  .ws-bg__part {
    cursor: pointer; /* Better for touch devices */
    min-width: 5.55%; /* Ensure parts are touch-friendly on tablets */
  }
}

@media (max-width: 480px) {
  .ws-bg__part {
    min-width: 8.33%; /* Larger touch targets on phones */
    touch-action: manipulation; /* Optimize touch response */
  }
}

.ws-bg__part-inner {
  position: absolute;
  top: 0;
  width: 100vw;
  height: 100%;
  background-size: cover;
  background-position: center center;
  will-change: transform;
  filter: brightness(0.9) contrast(1.1) saturate(1.2);
  transition: filter 0.6s ease;
}

.ws-bg__part:hover .ws-bg__part-inner {
  filter: brightness(1.1) contrast(1.2) saturate(1.3);
  transform: scale(1.05);
}

/* Luxury Text Container */
.luxury-text-container {
  overflow: hidden;
  position: absolute;
  left: 15%;
  top: 50%;
  width: 70%;
  height: 50px;
  margin-top: -25px;
  pointer-events: none;
  z-index: 15; /* Ensure text is always on top */
}

.luxury-text-container::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: radial-gradient(
    ellipse at center,
    rgba(212, 194, 164, 0.1) 0%,
    rgba(212, 194, 164, 0.05) 30%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(10px);
  z-index: -1;
}

/* Legacy class for compatibility */
.ws-text {
  overflow: hidden;
  position: absolute;
  left: 15%;
  top: 50%;
  width: 70%;
  height: 50px;
  margin-top: -25px;
  pointer-events: none;
  z-index: 15; /* Ensure text is always on top */
}

/* Luxury Heading Styles */
.luxury-heading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 30px;
  line-height: 50px;
  font-family: 'Cormorant Garamond', serif;
  font-weight: 600;
  will-change: transform;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  text-align: center;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  letter-spacing: 0.05em;
}

.luxury-text-span {
  position: relative;
  display: inline-block;
  padding: 8px 16px;
  background: rgba(22, 25, 29, 0.3);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(212, 194, 164, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(212, 194, 164, 0.1),
    0 0 20px rgba(212, 194, 164, 0.1);
  transition: all 0.4s ease;
}

.luxury-text-span:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(212, 194, 164, 0.2),
    0 0 30px rgba(212, 194, 164, 0.2);
  border-color: rgba(212, 194, 164, 0.4);
}

/* Legacy heading class for compatibility */
.ws-text__heading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 30px;
  line-height: 50px;
  font-family: 'Cormorant Garamond', serif;
  font-weight: 600;
  will-change: transform;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  text-align: center;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  letter-spacing: 0.05em;
}

/* Stack text headings vertically - responsive spacing */
.ws-text__heading:nth-child(1) { top: 0; }
.ws-text__heading:nth-child(2) { top: 50px; }
.ws-text__heading:nth-child(3) { top: 100px; }
.ws-text__heading:nth-child(4) { top: 150px; }
.ws-text__heading:nth-child(5) { top: 200px; }

/* Responsive text heading spacing */
@media (max-width: 768px) {
  .ws-text__heading:nth-child(1) { top: 0; }
  .ws-text__heading:nth-child(2) { top: 42px; }
  .ws-text__heading:nth-child(3) { top: 84px; }
  .ws-text__heading:nth-child(4) { top: 126px; }
  .ws-text__heading:nth-child(5) { top: 168px; }
}

@media (max-width: 480px) {
  .ws-text__heading:nth-child(1) { top: 0; }
  .ws-text__heading:nth-child(2) { top: 35px; }
  .ws-text__heading:nth-child(3) { top: 70px; }
  .ws-text__heading:nth-child(4) { top: 105px; }
  .ws-text__heading:nth-child(5) { top: 140px; }
}

@media (max-width: 320px) {
  .ws-text__heading:nth-child(1) { top: 0; }
  .ws-text__heading:nth-child(2) { top: 30px; }
  .ws-text__heading:nth-child(3) { top: 60px; }
  .ws-text__heading:nth-child(4) { top: 90px; }
  .ws-text__heading:nth-child(5) { top: 120px; }
}

/* Performance optimizations */
.ws-bg__part,
.ws-bg__part-inner,
.ws-text__heading {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth transitions */
.ws-bg__part {
  transition: transform 0.5s ease-out;
}

.ws-text__heading {
  transition: transform 0.5s ease-out;
}

/* Enhanced Mobile optimizations with Luxury Theme */
@media (max-width: 1024px) {
  .ws-pages {
    height: 90vh;
    min-height: 500px;
  }

  .ws-text, .luxury-text-container {
    left: 8%;
    width: 84%;
    height: 45px;
    margin-top: -22.5px;
  }

  .ws-text__heading, .luxury-heading {
    font-size: 28px;
    line-height: 45px;
    text-align: center;
  }

  .luxury-text-span {
    padding: 6px 12px;
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .ws-pages {
    height: 85vh;
    min-height: 450px;
  }

  .ws-text, .luxury-text-container {
    left: 5%;
    width: 90%;
    height: 42px;
    margin-top: -21px;
  }

  .ws-text__heading, .luxury-heading {
    font-size: 24px;
    line-height: 42px;
    flex-direction: column;
    align-items: center;
    text-align: center;
    justify-content: center;
  }

  .luxury-text-span {
    padding: 5px 10px;
    font-size: 24px;
    border-radius: 6px;
  }

  .ws-text__heading span:last-child {
    font-size: 16px;
    opacity: 0.8;
    margin-top: 4px;
  }

  /* Reduce background parts for better performance on tablets */
  .ws-bg__part {
    transition: transform 0.4s ease-out;
  }

  /* Adjust luxury effects for tablets */
  .luxury-wave-container::before {
    opacity: 0.7;
  }
}

@media (max-width: 640px) {
  .ws-pages {
    height: 80vh;
    min-height: 400px;
  }

  .ws-text {
    left: 3%;
    width: 94%;
    height: 38px;
    margin-top: -19px;
  }

  .ws-text__heading {
    font-size: 22px;
    line-height: 38px;
  }
}

@media (max-width: 480px) {
  .ws-pages {
    height: 75vh;
    min-height: 350px;
  }

  .ws-text, .luxury-text-container {
    left: 2%;
    width: 96%;
    height: 35px;
    margin-top: -17.5px;
  }

  .ws-text__heading, .luxury-heading {
    font-size: 18px;
    line-height: 35px;
    font-weight: 700;
  }

  .luxury-text-span {
    padding: 4px 8px;
    font-size: 18px;
    border-radius: 4px;
    backdrop-filter: blur(5px);
  }

  .ws-text__heading span:last-child {
    font-size: 14px;
  }

  /* Optimize for small screens */
  .ws-bg__part {
    transition: transform 0.3s ease-out;
  }

  .ws-bg__part-inner {
    background-size: cover;
    background-position: center center;
    filter: brightness(0.8) contrast(1.1) saturate(1.1);
  }

  /* Simplify luxury effects for mobile */
  .luxury-wave-container::before {
    opacity: 0.5;
  }

  .ws-bg__part::before {
    opacity: 0.7;
  }
}

@media (max-width: 375px) {
  .ws-pages {
    height: 70vh;
    min-height: 320px;
  }

  .ws-text {
    left: 1%;
    width: 98%;
    height: 32px;
    margin-top: -16px;
  }

  .ws-text__heading {
    font-size: 16px;
    line-height: 32px;
    font-weight: 700;
  }

  .ws-text__heading span:last-child {
    font-size: 12px;
  }
}

@media (max-width: 320px) {
  .ws-pages {
    height: 65vh;
    min-height: 300px;
  }

  .ws-text, .luxury-text-container {
    left: 1%;
    width: 98%;
    height: 30px;
    margin-top: -15px;
  }

  .ws-text__heading, .luxury-heading {
    font-size: 14px;
    line-height: 30px;
    font-weight: 700;
  }

  .luxury-text-span {
    padding: 3px 6px;
    font-size: 14px;
    border-radius: 3px;
    backdrop-filter: blur(3px);
  }

  .ws-text__heading span:last-child {
    font-size: 11px;
  }

  /* Minimal luxury effects for very small screens */
  .luxury-wave-container::before {
    opacity: 0.3;
  }

  .ws-bg__part::before {
    opacity: 0.5;
  }

  .luxury-bg-overlay::after {
    opacity: 0.6;
  }
}

/* Prevent text selection during drag */
.ws-pages.dragging {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Loading state */
.ws-pages:not(.s--ready) .ws-bg {
  opacity: 0.7;
}

.ws-pages.s--ready .ws-bg {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .ws-text__heading {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
    font-weight: 700;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .ws-bg__part,
  .ws-text__heading {
    transition: none !important;
  }
}

/* Focus styles for accessibility */
.ws-bg__part:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Ensure proper stacking */
.ws-bgs {
  z-index: 1;
}

.ws-text {
  z-index: 2;
}

/* Orientation and viewport optimizations */
@media (orientation: landscape) and (max-height: 500px) {
  .ws-pages {
    height: 95vh;
    min-height: 280px;
  }

  .ws-text {
    top: 45%;
  }

  .ws-text__heading {
    font-size: 16px;
    line-height: 1.2;
  }
}

/* High DPI screen optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .ws-bg__part-inner {
    background-size: cover;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Performance optimizations for low-end devices */
@media (max-width: 480px) {
  .ws-bg__part-inner {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Reduce motion complexity on small screens */
  .ws-bg__part {
    transition: transform 0.25s ease-out;
  }

  .ws-text__heading {
    transition: transform 0.25s ease-out;
  }
}

/* Very small screens (older phones) */
@media (max-width: 360px) and (max-height: 640px) {
  .ws-pages {
    height: 60vh;
    min-height: 280px;
  }

  .ws-text__heading {
    font-size: 13px;
    font-weight: 800;
    letter-spacing: 0.5px;
  }
}

/* Ensure proper scaling on iOS devices */
@supports (-webkit-touch-callout: none) {
  .ws-pages {
    -webkit-overflow-scrolling: touch;
  }

  .ws-bg__part {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

/* Luxury Animations and Effects */
@keyframes luxuryGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(212, 194, 164, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(212, 194, 164, 0.2);
  }
}

@keyframes luxuryPulse {
  0%, 100% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.2;
  }
}

@keyframes luxuryShimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.luxury-wave-container {
  animation: luxuryGlow 4s ease-in-out infinite;
}

.luxury-wave-container::before {
  animation: luxuryPulse 6s ease-in-out infinite;
}

/* Luxury text shimmer effect */
.luxury-text-span::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(212, 194, 164, 0.2),
    transparent
  );
  transition: left 0.8s ease;
}

.luxury-text-span:hover::before {
  left: 100%;
}

/* Premium glass morphism effects */
.luxury-glass-effect {
  background: rgba(22, 25, 29, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(212, 194, 164, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(212, 194, 164, 0.1);
}

/* Luxury scrollbar styling */
.luxury-wave-container::-webkit-scrollbar {
  width: 8px;
}

.luxury-wave-container::-webkit-scrollbar-track {
  background: rgba(22, 25, 29, 0.3);
  border-radius: 4px;
}

.luxury-wave-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #D4C2A4, #B8A082);
  border-radius: 4px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.luxury-wave-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #E5D3B7, #D4C2A4);
}

/* Luxury Title Text Effects */
.luxury-title-text {
  position: relative;
  display: inline-block;
}

.luxury-title-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(212, 194, 164, 0.3) 0%, rgba(242, 238, 230, 0.1) 50%, rgba(212, 194, 164, 0.3) 100%);
  border-radius: 8px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  padding: 4px 8px;
  margin: -4px -8px;
}

.luxury-title-text:hover::before {
  opacity: 1;
}

/* Enhanced text visibility */
.luxury-title-text {
  text-shadow:
    0 0 20px rgba(212, 194, 164, 0.6),
    0 0 40px rgba(212, 194, 164, 0.4),
    2px 2px 4px rgba(0, 0, 0, 0.5),
    0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

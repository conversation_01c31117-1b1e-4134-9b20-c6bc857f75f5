import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, Link, X, Image as ImageIcon, Loader2 } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  value?: string | string[];
  onChange: (value: string | string[]) => void;
  multiple?: boolean;
  folder?: string;
  label?: string;
  placeholder?: string;
  className?: string;
  required?: boolean;
  maxFiles?: number;
  disabled?: boolean;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  multiple = false,
  folder = 'images',
  label = 'Image',
  placeholder = 'Enter image URL or upload file',
  className,
  required = false,
  maxFiles = 5,
  disabled = false
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [urlInput, setUrlInput] = useState('');
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Convert value to array for consistent handling
  const currentImages = Array.isArray(value) ? value : (value ? [value] : []);

  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);
    
    // Validate files
    for (const file of fileArray) {
      const validation = FirebaseService.validateImageFile(file);
      if (!validation.isValid) {
        toast({
          title: "Invalid File",
          description: validation.error,
          variant: "destructive"
        });
        return;
      }
    }

    // Check file count limits
    if (multiple) {
      if (currentImages.length + fileArray.length > maxFiles) {
        toast({
          title: "Too Many Files",
          description: `Maximum ${maxFiles} images allowed`,
          variant: "destructive"
        });
        return;
      }
    } else if (fileArray.length > 1) {
      toast({
        title: "Single File Only",
        description: "Please select only one image",
        variant: "destructive"
      });
      return;
    }

    try {
      setUploading(true);
      setUploadProgress(0);

      let uploadedUrls: string[];

      if (fileArray.length === 1) {
        const url = await FirebaseService.uploadImage(
          fileArray[0],
          folder,
          setUploadProgress
        );
        uploadedUrls = [url];
      } else {
        uploadedUrls = await FirebaseService.uploadMultipleImages(
          fileArray,
          folder,
          setUploadProgress
        );
      }

      // Update the value
      if (multiple) {
        onChange([...currentImages, ...uploadedUrls]);
      } else {
        onChange(uploadedUrls[0]);
      }

      toast({
        title: "Success",
        description: `Image${fileArray.length > 1 ? 's' : ''} uploaded successfully`
      });

    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload Failed",
        description: "Failed to upload image. Please try again.",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  }, [currentImages, folder, maxFiles, multiple, onChange, toast]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    
    if (disabled || uploading) return;
    
    handleFileSelect(e.dataTransfer.files);
  }, [disabled, uploading, handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && !uploading) {
      setDragActive(true);
    }
  }, [disabled, uploading]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  const handleUrlAdd = () => {
    if (!urlInput.trim()) return;

    try {
      new URL(urlInput); // Validate URL
      
      if (multiple) {
        onChange([...currentImages, urlInput.trim()]);
      } else {
        onChange(urlInput.trim());
      }
      
      setUrlInput('');
      toast({
        title: "Success",
        description: "Image URL added successfully"
      });
    } catch {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid image URL",
        variant: "destructive"
      });
    }
  };

  const handleRemoveImage = (index: number) => {
    if (multiple) {
      const newImages = currentImages.filter((_, i) => i !== index);
      onChange(newImages);
    } else {
      onChange('');
    }
  };

  const openFileDialog = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {label && (
        <Label className="text-sm font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}

      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="upload" disabled={disabled}>
            <Upload className="w-4 h-4 mr-2" />
            Upload File
          </TabsTrigger>
          <TabsTrigger value="url" disabled={disabled}>
            <Link className="w-4 h-4 mr-2" />
            Image URL
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-4">
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
              dragActive ? "border-primary bg-primary/5" : "border-gray-300",
              disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-primary/50"
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={!disabled && !uploading ? openFileDialog : undefined}
          >
            {uploading ? (
              <div className="space-y-2">
                <Loader2 className="w-8 h-8 mx-auto animate-spin text-primary" />
                <p className="text-sm text-gray-600">Uploading...</p>
                <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
                <p className="text-xs text-gray-500">{Math.round(uploadProgress)}%</p>
              </div>
            ) : (
              <div className="space-y-2">
                <ImageIcon className="w-8 h-8 mx-auto text-gray-400" />
                <p className="text-sm text-gray-600">
                  Drag and drop {multiple ? 'images' : 'an image'} here, or click to select
                </p>
                <p className="text-xs text-gray-500">
                  Supports JPEG, PNG, WebP (max 10MB each)
                  {multiple && ` • Max ${maxFiles} files`}
                </p>
              </div>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/jpeg,image/jpg,image/png,image/webp"
            multiple={multiple}
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
            disabled={disabled || uploading}
          />
        </TabsContent>

        <TabsContent value="url" className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
              placeholder={placeholder}
              disabled={disabled}
              onKeyPress={(e) => e.key === 'Enter' && handleUrlAdd()}
            />
            <Button 
              onClick={handleUrlAdd} 
              disabled={!urlInput.trim() || disabled}
              variant="outline"
            >
              Add
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      {/* Image Preview */}
      {currentImages.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">Preview</Label>
          <div className={cn(
            "grid gap-4",
            multiple ? "grid-cols-2 md:grid-cols-3 lg:grid-cols-4" : "grid-cols-1 max-w-xs"
          )}>
            {currentImages.map((imageUrl, index) => (
              <Card key={index} className="relative group">
                <CardContent className="p-2">
                  <div className="relative aspect-square">
                    <img
                      src={imageUrl}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-full object-cover rounded"
                      onError={() => {
                        toast({
                          title: "Invalid Image",
                          description: "Failed to load image preview",
                          variant: "destructive"
                        });
                      }}
                    />
                    {!disabled && (
                      <Button
                        size="sm"
                        variant="destructive"
                        className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleRemoveImage(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;

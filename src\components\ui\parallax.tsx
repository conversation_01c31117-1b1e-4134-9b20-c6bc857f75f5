
import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface ParallaxProps extends React.HTMLAttributes<HTMLDivElement> {
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  children?: React.ReactNode;
  className?: string;
}

export function Parallax({
  speed = 0.2,
  direction = 'up',
  children,
  className,
  ...props
}: ParallaxProps) {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const elementTop = element.getBoundingClientRect().top + window.scrollY;
      const viewportHeight = window.innerHeight;
      
      // Only apply parallax when element is in view or near view
      if (elementTop < scrollPosition + viewportHeight && elementTop + element.offsetHeight > scrollPosition) {
        let translateValue = 0;
        
        // Calculate parallax offset based on the element's position in the viewport
        const elementPositionInViewport = elementTop - scrollPosition;
        const relativePosition = elementPositionInViewport / viewportHeight;
        
        // Apply movement based on direction and speed
        translateValue = (relativePosition - 0.5) * speed * 100;
        
        if (direction === 'down') translateValue = -translateValue;
        
        // Apply the transform
        if (direction === 'up' || direction === 'down') {
          element.style.transform = `translateY(${translateValue}px)`;
        } else if (direction === 'left' || direction === 'right') {
          translateValue = direction === 'right' ? -translateValue : translateValue;
          element.style.transform = `translateX(${translateValue}px)`;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call to position elements correctly
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed, direction]);

  return (
    <div
      ref={elementRef}
      className={cn("will-change-transform transition-transform", className)}
      {...props}
    >
      {children}
    </div>
  );
}

export default Parallax;

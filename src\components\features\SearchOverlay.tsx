import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FiPhone, FiX, FiSearch } from 'react-icons/fi';
import { FirebaseService } from '@/services/firebase';
import '@/styles/search-overlay.css';

interface SearchResult {
  id: string;
  category: string;
  title: string;
  description: string;
  imageUrl: string;
  link?: string;
}

interface SearchOverlayProps {
  onClose: () => void;
}

const SearchOverlay: React.FC<SearchOverlayProps> = ({ onClose }) => {
  const [searchValue, setSearchValue] = useState('Serengeti national Park');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);

  // Handle escape key to close overlay
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // Prevent body scroll when overlay is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  // Function to search across tours, destinations, and blog posts
  const performSearch = async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setResults([]);
      return;
    }

    setLoading(true);
    try {
      const [tours, destinations, blogPosts] = await Promise.all([
        FirebaseService.searchTours(searchTerm),
        FirebaseService.searchDestinations(searchTerm),
        FirebaseService.getBlogPosts()
      ]);

      const searchResults: SearchResult[] = [];

      // Add tour results
      tours.forEach((tour: any) => {
        if (tour.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            tour.description?.toLowerCase().includes(searchTerm.toLowerCase())) {
          searchResults.push({
            id: tour.id,
            category: 'TOUR',
            title: tour.title || 'Untitled Tour',
            description: tour.description || 'No description available',
            imageUrl: tour.images?.[0] || 'https://images.unsplash.com/photo-1516426122078-c23e76319801?q=80&w=300',
            link: `/tours/${tour.id}`
          });
        }
      });

      // Add destination results
      destinations.forEach((destination: any) => {
        searchResults.push({
          id: destination.id,
          category: 'DESTINATION',
          title: destination.name || 'Untitled Destination',
          description: destination.description || 'No description available',
          imageUrl: destination.images?.[0] || 'https://images.unsplash.com/photo-1516426122078-c23e76319801?q=80&w=300',
          link: `/destinations/${destination.id}`
        });
      });

      // Add blog post results
      blogPosts.forEach((post: any) => {
        if (post.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            post.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            post.excerpt?.toLowerCase().includes(searchTerm.toLowerCase())) {
          searchResults.push({
            id: post.id,
            category: 'BLOG',
            title: post.title || 'Untitled Post',
            description: post.excerpt || post.content?.substring(0, 200) + '...' || 'No description available',
            imageUrl: post.featuredImage || 'https://images.unsplash.com/photo-1516426122078-c23e76319801?q=80&w=300',
            link: `/blog/${post.slug || post.id}`
          });
        }
      });

      setResults(searchResults.slice(0, 10)); // Limit to 10 results
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Perform search when component mounts and when search value changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(searchValue);
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchValue]);

  const handleClearSearch = () => {
    setSearchValue('');
  };

  // Handle backdrop click to close overlay
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    // Full-screen luxury overlay container with custom scrollbar
    <div
      className="fixed inset-0 bg-[#16191D] text-[#F2EEE6] z-50 overflow-y-auto luxury-search-overlay"
      onClick={handleBackdropClick}
    >
      {/* Main content container with responsive padding */}
      <div
        className="px-4 sm:px-6 md:px-8 lg:px-12 py-4 sm:py-6 md:py-8 min-h-full"
        onClick={(e) => e.stopPropagation()}
      >

        {/* Luxury Header Section */}
        <header className="flex flex-col sm:flex-row justify-between items-center mb-6 sm:mb-8 md:mb-10 lg:mb-12 space-y-4 sm:space-y-0">
          {/* Left Side - Contact Info with glass morphism */}
          <div className="flex items-center gap-2 sm:gap-3 bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl px-3 sm:px-4 py-2 sm:py-3 shadow-lg hover:shadow-2xl transition-all duration-500 hover:bg-[#D4C2A4]/10 group order-2 sm:order-1">
            <div className="bg-[#D4C2A4]/20 p-1.5 sm:p-2 rounded-lg flex-shrink-0 group-hover:bg-[#D4C2A4]/30 transition-all duration-300">
              <FiPhone className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5 text-[#D4C2A4]" />
            </div>
            <div className="font-open-sans">
              <div className="text-xs sm:text-sm text-[#F2EEE6] font-medium">Call an Expert</div>
              <div className="text-xs text-[#F2EEE6]/70">Get More Details</div>
            </div>
          </div>

          {/* Center - Main Title with luxury styling */}
          <div className="order-1 sm:order-2 text-center">
            <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-cormorant uppercase tracking-[0.2em] sm:tracking-[0.3em] font-light text-[#F2EEE6] luxury-glow">
              WARRIORS SEARCH
            </h1>
            <div className="w-16 sm:w-20 md:w-24 h-0.5 bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent mx-auto mt-2 sm:mt-3"></div>
          </div>

          {/* Right Side - Close Button with luxury styling */}
          <button
            onClick={onClose}
            className="cursor-pointer bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl p-2 sm:p-3 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/40 transition-all duration-300 shadow-lg hover:shadow-2xl hover:scale-105 group order-3"
            aria-label="Close search overlay"
          >
            <FiX className="h-5 w-5 sm:h-6 sm:w-6 md:h-7 md:w-7 lg:h-8 lg:w-8 text-[#F2EEE6] group-hover:text-[#D4C2A4] transition-colors duration-300" />
          </button>
        </header>

        {/* Luxury Search Bar Section */}
        <div className="w-full sm:w-5/6 md:w-3/4 lg:w-2/3 xl:w-1/2 mx-auto mb-6 sm:mb-8 md:mb-10 lg:mb-12">
          <div className="relative group">
            {/* Glass morphism search container */}
            <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-2xl sm:rounded-3xl shadow-2xl hover:shadow-[0_25px_50px_-12px_rgba(212,194,164,0.25)] transition-all duration-500 hover:border-[#D4C2A4]/40 overflow-hidden">
              <div className="flex items-center p-3 sm:p-4 md:p-5">
                {/* Search Icon with luxury styling */}
                <div className="bg-[#D4C2A4]/20 p-2 sm:p-2.5 md:p-3 rounded-xl sm:rounded-2xl flex-shrink-0 mr-3 sm:mr-4 group-hover:bg-[#D4C2A4]/30 transition-all duration-300">
                  <FiSearch className="text-[#D4C2A4] text-sm sm:text-base md:text-lg" />
                </div>

                {/* Input Field with luxury styling */}
                <input
                  type="text"
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  className="flex-grow bg-transparent border-none focus:ring-0 focus:outline-none text-[#F2EEE6] placeholder-[#F2EEE6]/50 text-sm sm:text-base md:text-lg font-cormorant font-light"
                  placeholder="Discover your next adventure..."
                />

                {/* Clear Button with luxury styling */}
                {searchValue && (
                  <button
                    onClick={handleClearSearch}
                    className="bg-[#D4C2A4]/10 hover:bg-[#D4C2A4]/20 text-[#D4C2A4] hover:text-[#F2EEE6] px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg sm:rounded-xl text-xs sm:text-sm font-open-sans font-medium transition-all duration-300 ml-2 sm:ml-3 flex-shrink-0 hover:scale-105"
                  >
                    Clear
                  </button>
                )}
              </div>

              {/* Luxury glow effect on focus */}
              <div className="absolute inset-0 rounded-2xl sm:rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                <div className="absolute inset-0 rounded-2xl sm:rounded-3xl bg-gradient-to-r from-[#D4C2A4]/10 via-transparent to-[#D4C2A4]/10"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Luxury Search Results Section */}
        <main className="w-full sm:w-5/6 md:w-3/4 lg:w-2/3 xl:w-1/2 mx-auto pb-6 sm:pb-8 md:pb-10 lg:pb-12">
          {loading ? (
            <div className="flex flex-col justify-center items-center py-8 sm:py-12 md:py-16">
              <div className="luxury-loading w-16 sm:w-20 md:w-24 h-1 rounded-full mb-4 sm:mb-6"></div>
              <div className="text-[#F2EEE6]/70 font-open-sans text-sm sm:text-base font-light">Discovering your perfect adventure...</div>
            </div>
          ) : results.length === 0 && searchValue.trim() ? (
            <div className="flex flex-col justify-center items-center py-8 sm:py-12 md:py-16">
              <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-2xl sm:rounded-3xl p-6 sm:p-8 md:p-10 text-center shadow-2xl">
                <div className="text-[#F2EEE6]/70 font-open-sans text-sm sm:text-base mb-2">No adventures found for</div>
                <div className="text-[#D4C2A4] font-cormorant text-lg sm:text-xl md:text-2xl font-medium">"{searchValue}"</div>
                <div className="text-[#F2EEE6]/50 font-open-sans text-xs sm:text-sm mt-2">Try a different search term or explore our featured destinations</div>
              </div>
            </div>
          ) : (
            <div className="space-y-4 sm:space-y-6 md:space-y-8">
              {results.map((result: SearchResult, index: number) => (
                <Link
                  key={result.id}
                  to={result.link || '#'}
                  onClick={onClose}
                  className="block group search-result-item"
                >
                  <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-2xl sm:rounded-3xl p-3 sm:p-4 md:p-6 shadow-lg hover:shadow-2xl hover:shadow-[#D4C2A4]/10 transition-all duration-500 hover:border-[#D4C2A4]/40 hover:bg-[#D4C2A4]/10 hover:-translate-y-1 sm:hover:-translate-y-2">
                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6">
                      {/* Result Image with luxury styling */}
                      <div className="relative overflow-hidden rounded-xl sm:rounded-2xl flex-shrink-0 w-full sm:w-32 md:w-40 lg:w-48 h-32 sm:h-24 md:h-28 lg:h-32">
                        <img
                          src={result.imageUrl}
                          alt={result.title}
                          className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      </div>

                      {/* Result Text Content */}
                      <div className="flex-1 min-w-0">
                        {/* Category Label with luxury badge */}
                        <div className="inline-flex items-center bg-[#D4C2A4]/20 text-[#D4C2A4] px-2 sm:px-3 py-1 rounded-lg font-open-sans text-xs sm:text-sm uppercase tracking-wider mb-2 sm:mb-3 font-medium">
                          {result.category}
                        </div>

                        {/* Title with luxury typography */}
                        <h2 className="text-lg sm:text-xl md:text-2xl text-[#F2EEE6] font-cormorant font-medium leading-tight group-hover:text-[#D4C2A4] transition-colors duration-300 mb-2 sm:mb-3">
                          {result.title}
                        </h2>

                        {/* Description with responsive text */}
                        <p className="text-[#F2EEE6]/70 font-open-sans leading-relaxed text-xs sm:text-sm md:text-base line-clamp-2 sm:line-clamp-3 group-hover:text-[#F2EEE6]/90 transition-colors duration-300">
                          {result.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default SearchOverlay;

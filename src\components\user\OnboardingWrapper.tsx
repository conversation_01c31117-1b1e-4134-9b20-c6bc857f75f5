import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import UserOnboarding from './UserOnboarding';

const OnboardingWrapper: React.FC = () => {
  const { showOnboarding, setShowOnboarding } = useAuth();

  return (
    <UserOnboarding
      isOpen={showOnboarding}
      onClose={() => setShowOnboarding(false)}
      onComplete={() => {
        setShowOnboarding(false);
        // Optionally show a success message or redirect
        console.log('Onboarding completed successfully!');
      }}
    />
  );
};

export default OnboardingWrapper;

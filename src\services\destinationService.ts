import { FirebaseService } from './firebase';
import { Destination } from '@/types/firebase';

export interface DestinationSummary {
  id: string;
  name: string;
  region: string;
  country: string;
  featured: boolean;
}

export class DestinationService {
  // Cache for destinations to avoid repeated Firebase calls
  private static destinationsCache: Destination[] | null = null;
  private static cacheTimestamp: number = 0;
  private static CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Get all destinations with caching
  static async getDestinations(): Promise<Destination[]> {
    const now = Date.now();
    
    // Return cached data if it's still fresh
    if (this.destinationsCache && (now - this.cacheTimestamp) < this.CACHE_DURATION) {
      return this.destinationsCache;
    }

    try {
      const destinations = await FirebaseService.getDestinations() as Destination[];
      this.destinationsCache = destinations;
      this.cacheTimestamp = now;
      return destinations;
    } catch (error) {
      console.error('Error fetching destinations:', error);
      // Return cached data if available, even if stale
      if (this.destinationsCache) {
        return this.destinationsCache;
      }
      throw error;
    }
  }

  // Get destination names for autocomplete
  static async getDestinationNames(): Promise<string[]> {
    try {
      const destinations = await this.getDestinations();
      return destinations
        .filter(dest => dest.name && dest.name.trim() !== '')
        .map(dest => dest.name)
        .sort();
    } catch (error) {
      console.error('Error getting destination names:', error);
      // Return fallback destinations
      return [
        'Serengeti National Park',
        'Ngorongoro Crater',
        'Tarangire National Park',
        'Lake Manyara National Park',
        'Mount Kilimanjaro',
        'Ruaha National Park',
        'Selous Game Reserve',
        'Maasai Villages'
      ];
    }
  }

  // Get featured destinations
  static async getFeaturedDestinations(): Promise<Destination[]> {
    try {
      const destinations = await this.getDestinations();
      return destinations.filter(dest => dest.featured);
    } catch (error) {
      console.error('Error getting featured destinations:', error);
      return [];
    }
  }

  // Search destinations by name or description
  static async searchDestinations(searchTerm: string): Promise<Destination[]> {
    try {
      if (!searchTerm || searchTerm.trim() === '') {
        return await this.getDestinations();
      }

      const destinations = await this.getDestinations();
      const term = searchTerm.toLowerCase().trim();

      return destinations.filter(dest => 
        dest.name?.toLowerCase().includes(term) ||
        dest.description?.toLowerCase().includes(term) ||
        dest.region?.toLowerCase().includes(term) ||
        dest.activities?.some(activity => activity.toLowerCase().includes(term))
      );
    } catch (error) {
      console.error('Error searching destinations:', error);
      return [];
    }
  }

  // Get destination by name (for exact matching)
  static async getDestinationByName(name: string): Promise<Destination | null> {
    try {
      const destinations = await this.getDestinations();
      return destinations.find(dest => 
        dest.name?.toLowerCase() === name.toLowerCase()
      ) || null;
    } catch (error) {
      console.error('Error getting destination by name:', error);
      return null;
    }
  }

  // Get popular destinations for quick search tags
  static async getPopularDestinations(): Promise<DestinationSummary[]> {
    try {
      const destinations = await this.getDestinations();
      
      // Define popular destinations based on common safari destinations
      const popularNames = [
        'Serengeti National Park',
        'Ngorongoro Crater', 
        'Tarangire National Park',
        'Lake Manyara National Park',
        'Mount Kilimanjaro',
        'Maasai Villages'
      ];

      const popular = destinations
        .filter(dest => popularNames.some(name => 
          dest.name?.toLowerCase().includes(name.toLowerCase())
        ))
        .map(dest => ({
          id: dest.id,
          name: dest.name,
          region: dest.region,
          country: dest.country,
          featured: dest.featured
        }));

      // If we don't have enough from Firebase, add fallbacks
      if (popular.length < 4) {
        const fallbacks = popularNames
          .filter(name => !popular.some(p => p.name.includes(name)))
          .slice(0, 6 - popular.length)
          .map((name, index) => ({
            id: `fallback-${index}`,
            name,
            region: 'Northern Tanzania',
            country: 'Tanzania',
            featured: true
          }));
        
        return [...popular, ...fallbacks];
      }

      return popular.slice(0, 6);
    } catch (error) {
      console.error('Error getting popular destinations:', error);
      // Return fallback popular destinations
      return [
        { id: 'serengeti', name: 'Serengeti National Park', region: 'Northern Tanzania', country: 'Tanzania', featured: true },
        { id: 'ngorongoro', name: 'Ngorongoro Crater', region: 'Northern Tanzania', country: 'Tanzania', featured: true },
        { id: 'tarangire', name: 'Tarangire National Park', region: 'Northern Tanzania', country: 'Tanzania', featured: true },
        { id: 'manyara', name: 'Lake Manyara National Park', region: 'Northern Tanzania', country: 'Tanzania', featured: true },
        { id: 'kilimanjaro', name: 'Mount Kilimanjaro', region: 'Northern Tanzania', country: 'Tanzania', featured: true },
        { id: 'maasai', name: 'Maasai Villages', region: 'Northern Tanzania', country: 'Tanzania', featured: true }
      ];
    }
  }

  // Clear cache (useful for admin operations)
  static clearCache(): void {
    this.destinationsCache = null;
    this.cacheTimestamp = 0;
  }

  // Get destination statistics
  static async getDestinationStats(): Promise<{
    total: number;
    featured: number;
    byRegion: Record<string, number>;
  }> {
    try {
      const destinations = await this.getDestinations();
      
      const stats = {
        total: destinations.length,
        featured: destinations.filter(d => d.featured).length,
        byRegion: {} as Record<string, number>
      };

      destinations.forEach(dest => {
        const region = dest.region || 'Unknown';
        stats.byRegion[region] = (stats.byRegion[region] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Error getting destination stats:', error);
      return { total: 0, featured: 0, byRegion: {} };
    }
  }

  // Validate destination name against Firebase data
  static async validateDestination(name: string): Promise<boolean> {
    try {
      const destinations = await this.getDestinationNames();
      return destinations.some(dest => 
        dest.toLowerCase() === name.toLowerCase()
      );
    } catch (error) {
      console.error('Error validating destination:', error);
      return false;
    }
  }

  // Get destination suggestions based on partial input
  static async getDestinationSuggestions(partial: string, limit: number = 5): Promise<string[]> {
    try {
      if (!partial || partial.trim().length < 2) {
        return [];
      }

      const destinations = await this.getDestinationNames();
      const term = partial.toLowerCase().trim();

      const suggestions = destinations
        .filter(name => name.toLowerCase().includes(term))
        .sort((a, b) => {
          // Prioritize exact matches at the beginning
          const aStarts = a.toLowerCase().startsWith(term);
          const bStarts = b.toLowerCase().startsWith(term);
          
          if (aStarts && !bStarts) return -1;
          if (!aStarts && bStarts) return 1;
          
          return a.localeCompare(b);
        })
        .slice(0, limit);

      return suggestions;
    } catch (error) {
      console.error('Error getting destination suggestions:', error);
      return [];
    }
  }
}

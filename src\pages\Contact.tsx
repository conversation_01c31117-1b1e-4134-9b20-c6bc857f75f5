import React, { useState } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import LeafletMap from '@/components/maps/LeafletMap';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MapPin, Phone, Mail, Clock, Send, Star, Award, Shield } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { useToast } from '@/hooks/use-toast';
import { Timestamp } from 'firebase/firestore';

const Contact = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    category: 'general',
    message: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.email || !formData.message) {
      toast({
        title: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    try {
      setSubmitting(true);

      const messageData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        subject: formData.subject,
        category: formData.category as 'general' | 'booking' | 'complaint' | 'suggestion',
        message: formData.message,
        status: 'unread' as const,
        priority: 'medium' as const,
        createdAt: Timestamp.now()
      };

      await FirebaseService.createContactMessage(messageData);

      toast({
        title: "Message sent successfully!",
        description: "We'll get back to you within 24 hours."
      });

      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        category: 'general',
        message: ''
      });

    } catch (error: any) {
      let errorMessage = "Please try again or contact us directly.";

      if (error?.code === 'permission-denied') {
        errorMessage = "You don't have permission to send messages. Please try again later.";
      } else if (error?.code === 'unavailable') {
        errorMessage = "Service is temporarily unavailable. Please try again later.";
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Failed to send message",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#16191D]">
      <Header />
      <main className="relative">
        {/* Luxury Hero Section */}
        <div className="relative overflow-hidden bg-[#16191D] pt-16 sm:pt-20">
          {/* Elegant Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                               radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
              backgroundSize: '40px 40px'
            }} />
          </div>

          {/* Hero Content */}
          <div className="relative z-10 container mx-auto px-4 py-12 sm:py-16 md:py-20 lg:py-24 text-center">
            <div className="max-w-4xl mx-auto">
              {/* Luxury Badge */}
              <div className="inline-flex items-center gap-1.5 sm:gap-2 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-3 sm:px-4 md:px-6 py-1.5 sm:py-2 mb-6 sm:mb-8">
                <Star className="w-3 h-3 sm:w-4 sm:h-4 text-[#D4C2A4]" />
                <span className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wider uppercase">Premium Safari Experience</span>
              </div>

              <h1 className="font-cormorant text-3xl sm:text-4xl md:text-5xl lg:text-7xl xl:text-8xl text-[#F2EEE6] mb-4 sm:mb-6 leading-tight">
                Connect with
                <span className="block text-[#D4C2A4] italic">Excellence</span>
              </h1>

              <p className="font-open-sans text-sm sm:text-base md:text-lg lg:text-xl text-[#F2EEE6]/80 max-w-xl sm:max-w-2xl mx-auto leading-relaxed mb-6 sm:mb-8 px-2">
                Begin your extraordinary African journey with our dedicated safari specialists.
                Every conversation is the first step toward your perfect adventure.
              </p>

              {/* Elegant Divider */}
              <div className="flex items-center justify-center gap-2 sm:gap-4 mb-8 sm:mb-12">
                <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent w-16 sm:w-24"></div>
                <Award className="w-4 h-4 sm:w-6 sm:h-6 text-[#D4C2A4]" />
                <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent w-16 sm:w-24"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Section */}
        <div className="relative bg-[#16191D] py-10 sm:py-12 md:py-16 lg:py-20">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-10 md:gap-12 lg:gap-16 max-w-7xl mx-auto">

              {/* Luxury Contact Form */}
              <div className="order-2 lg:order-1">
                <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 lg:p-10 shadow-2xl">
                  {/* Form Header */}
                  <div className="text-center mb-6 sm:mb-8 md:mb-10">
                    <h2 className="font-cormorant text-2xl sm:text-3xl lg:text-4xl text-[#F2EEE6] mb-3 sm:mb-4">
                      Begin Your Journey
                    </h2>
                    <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm leading-relaxed px-2">
                      Share your vision with us, and we'll craft an unforgettable safari experience tailored to your dreams.
                    </p>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5 md:space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-5 md:gap-6">
                      <div className="space-y-1.5 sm:space-y-2">
                        <Label htmlFor="name" className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase">
                          Full Name *
                        </Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          required
                          className="bg-[#F2EEE6]/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#F2EEE6]/50 focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20 rounded-lg sm:rounded-xl h-10 sm:h-11 md:h-12 font-open-sans text-sm"
                          placeholder="Your full name"
                        />
                      </div>
                      <div className="space-y-1.5 sm:space-y-2">
                        <Label htmlFor="email" className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase">
                          Email Address *
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          required
                          className="bg-[#F2EEE6]/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#F2EEE6]/50 focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20 rounded-lg sm:rounded-xl h-10 sm:h-11 md:h-12 font-open-sans text-sm"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-5 md:gap-6">
                      <div className="space-y-1.5 sm:space-y-2">
                        <Label htmlFor="phone" className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase">
                          Phone Number
                        </Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          className="bg-[#F2EEE6]/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#F2EEE6]/50 focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20 rounded-lg sm:rounded-xl h-10 sm:h-11 md:h-12 font-open-sans text-sm"
                          placeholder="+****************"
                        />
                      </div>
                      <div className="space-y-1.5 sm:space-y-2">
                        <Label htmlFor="category" className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase">
                          Inquiry Type
                        </Label>
                        <Select
                          value={formData.category}
                          onValueChange={(value) => handleInputChange('category', value)}
                        >
                          <SelectTrigger className="bg-[#F2EEE6]/5 border-[#D4C2A4]/30 text-[#F2EEE6] focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20 rounded-lg sm:rounded-xl h-10 sm:h-11 md:h-12 font-open-sans text-sm">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className="bg-[#16191D] border-[#D4C2A4]/30">
                            <SelectItem value="general" className="text-[#F2EEE6] focus:bg-[#D4C2A4]/20">General Inquiry</SelectItem>
                            <SelectItem value="booking" className="text-[#F2EEE6] focus:bg-[#D4C2A4]/20">Safari Booking</SelectItem>
                            <SelectItem value="complaint" className="text-[#F2EEE6] focus:bg-[#D4C2A4]/20">Feedback</SelectItem>
                            <SelectItem value="suggestion" className="text-[#F2EEE6] focus:bg-[#D4C2A4]/20">Suggestion</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-1.5 sm:space-y-2">
                      <Label htmlFor="subject" className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase">
                        Subject
                      </Label>
                      <Input
                        id="subject"
                        value={formData.subject}
                        onChange={(e) => handleInputChange('subject', e.target.value)}
                        placeholder="Brief description of your inquiry"
                        className="bg-[#F2EEE6]/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#F2EEE6]/50 focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20 rounded-lg sm:rounded-xl h-10 sm:h-11 md:h-12 font-open-sans text-sm"
                      />
                    </div>

                    <div className="space-y-1.5 sm:space-y-2">
                      <Label htmlFor="message" className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase">
                        Your Message *
                      </Label>
                      <Textarea
                        id="message"
                        value={formData.message}
                        onChange={(e) => handleInputChange('message', e.target.value)}
                        placeholder="Share your safari dreams, preferred destinations, travel dates, group size, and any special requirements..."
                        rows={4}
                        required
                        className="bg-[#F2EEE6]/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#F2EEE6]/50 focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20 rounded-lg sm:rounded-xl font-open-sans resize-none text-sm min-h-[100px] sm:min-h-[120px] md:min-h-[140px]"
                      />
                    </div>

                    <Button
                      type="submit"
                      disabled={loading || submitting}
                      className="w-full bg-gradient-to-r from-[#D4C2A4] to-[#D4C2A4]/80 hover:from-[#D4C2A4]/90 hover:to-[#D4C2A4]/70 text-[#16191D] font-open-sans font-semibold tracking-wide uppercase text-xs sm:text-sm h-11 sm:h-12 md:h-14 rounded-lg sm:rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-[#D4C2A4]/25 disabled:opacity-50"
                    >
                      {loading || submitting ? (
                        <span className="flex items-center gap-1.5 sm:gap-2">
                          <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-[#16191D]/30 border-t-[#16191D] rounded-full animate-spin"></div>
                          Sending Message...
                        </span>
                      ) : (
                        <span className="flex items-center gap-1.5 sm:gap-2">
                          <Send className="w-3 h-3 sm:w-4 sm:h-4" />
                          Send Message
                        </span>
                      )}
                    </Button>
                  </form>
                </div>
              </div>

              {/* Luxury Contact Information */}
              <div className="order-1 lg:order-2 space-y-6 sm:space-y-8">
                {/* Main Contact Card */}
                <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 lg:p-10 shadow-2xl">
                  <div className="text-center mb-6 sm:mb-8">
                    <h2 className="font-cormorant text-2xl sm:text-3xl lg:text-4xl text-[#F2EEE6] mb-3 sm:mb-4">
                      Reach Our Experts
                    </h2>
                    <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm px-2">
                      Our dedicated team is ready to assist you with personalized service and expert guidance.
                    </p>
                  </div>

                  <div className="space-y-4 sm:space-y-6 md:space-y-8">
                    <div className="group">
                      <div className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 rounded-xl sm:rounded-2xl transition-all duration-300 hover:bg-[#D4C2A4]/5">
                        <div className="bg-[#D4C2A4]/20 p-2 sm:p-3 rounded-lg sm:rounded-xl flex-shrink-0">
                          <MapPin className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4C2A4]" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-cormorant text-lg sm:text-xl text-[#F2EEE6] mb-1 sm:mb-2">Safari Headquarters</h3>
                          <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm leading-relaxed">
                            123 Safari Street<br />
                            Arusha, Tanzania<br />
                            P.O. Box 12345
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 rounded-xl sm:rounded-2xl transition-all duration-300 hover:bg-[#D4C2A4]/5">
                        <div className="bg-[#D4C2A4]/20 p-2 sm:p-3 rounded-lg sm:rounded-xl flex-shrink-0">
                          <Phone className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4C2A4]" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-cormorant text-lg sm:text-xl text-[#F2EEE6] mb-1 sm:mb-2">Direct Lines</h3>
                          <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm leading-relaxed">
                            Main Office: +25566121379<br />
                            WhatsApp: +25566121379<br />
                            Emergency: +25566121379
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 rounded-xl sm:rounded-2xl transition-all duration-300 hover:bg-[#D4C2A4]/5">
                        <div className="bg-[#D4C2A4]/20 p-2 sm:p-3 rounded-lg sm:rounded-xl flex-shrink-0">
                          <Mail className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4C2A4]" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-cormorant text-lg sm:text-xl text-[#F2EEE6] mb-1 sm:mb-2">Email Channels</h3>
                          <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm leading-relaxed">
                            General: <EMAIL><br />
                            Bookings: <EMAIL><br />
                            Support: <EMAIL>
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 rounded-xl sm:rounded-2xl transition-all duration-300 hover:bg-[#D4C2A4]/5">
                        <div className="bg-[#D4C2A4]/20 p-2 sm:p-3 rounded-lg sm:rounded-xl flex-shrink-0">
                          <Clock className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4C2A4]" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-cormorant text-lg sm:text-xl text-[#F2EEE6] mb-1 sm:mb-2">Operating Hours</h3>
                          <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm leading-relaxed">
                            Monday - Friday: 8:00 AM - 6:00 PM<br />
                            Saturday: 9:00 AM - 4:00 PM<br />
                            Sunday: Closed<br />
                            <span className="text-xs text-[#D4C2A4]">(East Africa Time - EAT)</span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Premium Support Card */}
                <div className="bg-gradient-to-br from-[#D4C2A4]/10 to-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/30 rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 shadow-2xl">
                  <div className="text-center mb-4 sm:mb-6">
                    <Shield className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-[#D4C2A4] mx-auto mb-3 sm:mb-4" />
                    <h3 className="font-cormorant text-xl sm:text-2xl text-[#F2EEE6] mb-2 sm:mb-3">
                      Priority Support
                    </h3>
                    <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm leading-relaxed px-2">
                      For immediate assistance, especially urgent matters or emergencies during your safari adventure.
                    </p>
                  </div>

                  <div className="space-y-3 sm:space-y-4">
                    <Button
                      variant="outline"
                      className="w-full justify-start bg-[#D4C2A4]/10 border-[#D4C2A4]/30 text-[#F2EEE6] hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4]/50 rounded-lg sm:rounded-xl h-10 sm:h-11 md:h-12 font-open-sans transition-all duration-300 text-xs sm:text-sm"
                    >
                      <Phone className="w-3 h-3 sm:w-4 sm:h-4 mr-2 sm:mr-3 text-[#D4C2A4]" />
                      Call Directly: +25566121379
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full justify-start bg-[#D4C2A4]/10 border-[#D4C2A4]/30 text-[#F2EEE6] hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4]/50 rounded-lg sm:rounded-xl h-10 sm:h-11 md:h-12 font-open-sans transition-all duration-300 text-xs sm:text-sm"
                    >
                      <Mail className="w-3 h-3 sm:w-4 sm:h-4 mr-2 sm:mr-3 text-[#D4C2A4]" />
                      WhatsApp: +25566121379
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Luxury Map Section */}
        <div className="relative bg-[#16191D] py-10 sm:py-12 md:py-16 lg:py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-8 sm:mb-10 md:mb-12">
                <h2 className="font-cormorant text-2xl sm:text-3xl md:text-4xl lg:text-5xl text-[#F2EEE6] mb-3 sm:mb-4">
                  Visit Our Safari Hub
                </h2>
                <p className="font-open-sans text-[#F2EEE6]/70 text-sm sm:text-base md:text-lg max-w-xl sm:max-w-2xl mx-auto px-4">
                  Located in the heart of Arusha, our office is your gateway to extraordinary African adventures.
                </p>
              </div>

              <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 shadow-2xl">
                <div className="rounded-xl sm:rounded-2xl overflow-hidden mb-4 sm:mb-6">
                  <LeafletMap
                    destinations={[
                      {
                        id: 'office',
                        name: 'Warrior of Africa Safari Tours Office',
                        lat: -3.3869,
                        lng: 36.6830,
                        description: 'Our main office in Arusha, Tanzania. Visit us for personalized safari planning and expert advice.',
                        image: 'https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=800&h=600'
                      }
                    ]}
                    height="300px"
                    className="sm:h-[350px] md:h-[400px]"
                    showRoutes={false}
                    onDestinationClick={() => {
                      // Office location clicked
                    }}
                  />
                </div>

                <div className="bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl p-4 sm:p-6">
                  <div className="flex items-start space-x-3 sm:space-x-4">
                    <div className="bg-[#D4C2A4]/20 p-2 sm:p-3 rounded-lg sm:rounded-xl flex-shrink-0">
                      <MapPin className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4C2A4]" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-cormorant text-lg sm:text-xl text-[#F2EEE6] mb-1 sm:mb-2">Office Location</h3>
                      <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm leading-relaxed mb-1 sm:mb-2">
                        123 Safari Street, Arusha, Tanzania<br />
                        Near the Clock Tower, Central Business District
                      </p>
                      <p className="font-open-sans text-[#D4C2A4] text-xs">
                        Coordinates: -3.3869°, 36.6830°
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer isDarkBackground={true} />
    </div>
  );
};

export default Contact;

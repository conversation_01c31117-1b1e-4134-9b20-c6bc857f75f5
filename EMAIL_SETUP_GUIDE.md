# Email Forwarding Setup Guide

## Overview
This guide explains how to set up email forwarding for booking confirmations and custom tour requests to your email (<EMAIL>).

## Current Implementation
The system uses a mock EmailJS implementation that logs email data to the console. To enable actual email sending, follow the setup steps below.

## Setup Steps

### 1. Install EmailJS Package
```bash
npm install @emailjs/browser
```

### 2. Create EmailJS Account
1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Create a free account
3. Create a new service (Gmail, Outlook, etc.)
4. Note down your:
   - Service ID
   - Public Key

### 3. Create Email Templates

#### Template 1: Booking Confirmation (template_booking_forward)
Create a template with these variables:
- `{{to_email}}` - Recipient email
- `{{subject}}` - Email subject
- `{{booking_id}}` - Booking ID
- `{{tour_title}}` - Tour name
- `{{customer_name}}` - Customer name
- `{{customer_email}}` - Customer email
- `{{customer_phone}}` - Customer phone
- `{{start_date}}` - Tour start date
- `{{group_size}}` - Number of adults
- `{{children_count}}` - Number of children
- `{{accommodation}}` - Accommodation type
- `{{total_price}}` - Total price
- `{{special_requests}}` - Special requests
- `{{travelers_details}}` - Traveler information
- `{{add_ons}}` - Selected add-ons
- `{{booking_date}}` - Booking date
- `{{booking_time}}` - Booking time

#### Template 2: Custom Tour Request (template_custom_tour_forward)
Create a template with these variables:
- `{{to_email}}` - Recipient email
- `{{subject}}` - Email subject
- `{{request_id}}` - Request ID
- `{{customer_name}}` - Customer name
- `{{customer_email}}` - Customer email
- `{{customer_phone}}` - Customer phone
- `{{duration}}` - Tour duration
- `{{participants}}` - Number of participants
- `{{budget_range}}` - Budget range
- `{{start_date}}` - Preferred start date
- `{{destinations}}` - Selected destinations
- `{{interests}}` - Selected interests
- `{{accommodation}}` - Accommodation preference
- `{{activities}}` - Selected activities
- `{{special_requests}}` - Special requests
- `{{fitness_level}}` - Fitness level
- `{{photography_interest}}` - Photography interest
- `{{request_date}}` - Request date
- `{{request_time}}` - Request time

### 4. Update Email Service Configuration

In `src/services/emailService.ts`, update these constants:
```typescript
const EMAILJS_SERVICE_ID = 'your_actual_service_id';
const EMAILJS_TEMPLATE_ID_BOOKING = 'template_booking_forward';
const EMAILJS_TEMPLATE_ID_CUSTOM_TOUR = 'template_custom_tour_forward';
const EMAILJS_PUBLIC_KEY = 'your_actual_public_key';
```

### 5. Enable Real EmailJS
Replace the mock implementation with real EmailJS:

```typescript
// Remove the mock implementation and uncomment:
import emailjs from '@emailjs/browser';

// Initialize EmailJS
emailjs.init(EMAILJS_PUBLIC_KEY);

// Replace mockEmailJS.send with emailjs.send in all methods
```

## Email Templates Examples

### Booking Confirmation Template
```html
<h2>New Booking Confirmation</h2>
<p><strong>Booking ID:</strong> {{booking_id}}</p>
<p><strong>Tour:</strong> {{tour_title}}</p>
<p><strong>Customer:</strong> {{customer_name}} ({{customer_email}})</p>
<p><strong>Phone:</strong> {{customer_phone}}</p>
<p><strong>Start Date:</strong> {{start_date}}</p>
<p><strong>Group Size:</strong> {{group_size}} adults, {{children_count}} children</p>
<p><strong>Accommodation:</strong> {{accommodation}}</p>
<p><strong>Total Price:</strong> {{total_price}}</p>
<p><strong>Add-ons:</strong> {{add_ons}}</p>
<p><strong>Special Requests:</strong> {{special_requests}}</p>
<p><strong>Travelers:</strong></p>
<pre>{{travelers_details}}</pre>
<p><strong>Booked on:</strong> {{booking_date}} at {{booking_time}}</p>
```

### Custom Tour Request Template
```html
<h2>New Custom Tour Request</h2>
<p><strong>Request ID:</strong> {{request_id}}</p>
<p><strong>Customer:</strong> {{customer_name}} ({{customer_email}})</p>
<p><strong>Phone:</strong> {{customer_phone}}</p>
<p><strong>Duration:</strong> {{duration}}</p>
<p><strong>Participants:</strong> {{participants}}</p>
<p><strong>Budget:</strong> {{budget_range}}</p>
<p><strong>Start Date:</strong> {{start_date}}</p>
<p><strong>Destinations:</strong> {{destinations}}</p>
<p><strong>Interests:</strong> {{interests}}</p>
<p><strong>Accommodation:</strong> {{accommodation}}</p>
<p><strong>Activities:</strong> {{activities}}</p>
<p><strong>Fitness Level:</strong> {{fitness_level}}</p>
<p><strong>Photography Interest:</strong> {{photography_interest}}</p>
<p><strong>Special Requests:</strong> {{special_requests}}</p>
<p><strong>Requested on:</strong> {{request_date}} at {{request_time}}</p>
```

## Testing
1. Make a test booking or custom tour request
2. Check browser console for email data
3. Once EmailJS is configured, emails will be <NAME_EMAIL>

## Features Implemented
✅ LastSection buttons now navigate to tour-builder and contact pages
✅ SafariInfoSection CTA button now navigates to contact page
✅ Email service created with proper data formatting
✅ Booking system integrated with email forwarding
✅ Custom tour system integrated with email forwarding
✅ Structured email templates with all booking/tour details
✅ Error handling to prevent breaking the booking/tour process

## Next Steps
1. Set up EmailJS account and templates
2. Update configuration with real credentials
3. Test email functionality
4. Monitor email delivery and adjust templates as needed

import { useState, useEffect, useCallback, useMemo } from 'react';
import { FirebaseService } from '@/services/firebase';
import { SearchResult, SearchState, EnhancedSearchFilters, Tour, Destination } from '@/types/firebase';

// Debounce hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Transform tour data to search result
const transformTourToSearchResult = (tour: Tour): SearchResult => ({
  id: tour.id,
  title: tour.title,
  type: 'tour',
  description: tour.description,
  image: tour.images?.[0] || '',
  location: tour.location,
  rating: tour.rating,
  price: tour.price,
  duration: tour.duration,
  difficulty: tour.difficulty,
  maxGroupSize: tour.maxGroupSize,
});

// Transform destination data to search result
const transformDestinationToSearchResult = (destination: Destination): SearchResult => ({
  id: destination.id,
  title: destination.name,
  type: 'destination',
  description: destination.description,
  image: destination.images?.[0] || '',
  location: `${destination.name}, ${destination.country}`,
  country: destination.country,
  region: destination.region,
  bestTimeToVisit: destination.bestTimeToVisit,
  activities: destination.activities,
});

export const useEnhancedSearch = () => {
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    results: [],
    loading: false,
    error: null,
    showResults: false,
    filters: {
      type: 'all',
    },
  });

  // Debounce search query to avoid excessive API calls
  const debouncedQuery = useDebounce(searchState.query, 300);

  // Search function
  const performSearch = useCallback(async (query: string, filters: EnhancedSearchFilters) => {
    if (!query.trim() || query.length < 2) {
      setSearchState(prev => ({
        ...prev,
        results: [],
        loading: false,
        showResults: false,
        error: null,
      }));
      return;
    }

    setSearchState(prev => ({
      ...prev,
      loading: true,
      error: null,
    }));

    try {
      const promises: Promise<any>[] = [];
      
      // Search tours if type is 'all' or 'tour'
      if (filters.type === 'all' || filters.type === 'tour') {
        promises.push(FirebaseService.searchTours(query));
      }
      
      // Search destinations if type is 'all' or 'destination'
      if (filters.type === 'all' || filters.type === 'destination') {
        promises.push(FirebaseService.searchDestinations(query));
      }

      const results = await Promise.all(promises);
      let combinedResults: SearchResult[] = [];

      // Process tour results
      if (filters.type === 'all' || filters.type === 'tour') {
        const tourResults = results[0] || [];
        const transformedTours = tourResults.map(transformTourToSearchResult);
        combinedResults = [...combinedResults, ...transformedTours];
      }

      // Process destination results
      if (filters.type === 'all' || filters.type === 'destination') {
        const destinationResults = filters.type === 'all' ? results[1] || [] : results[0] || [];
        const transformedDestinations = destinationResults.map(transformDestinationToSearchResult);
        combinedResults = [...combinedResults, ...transformedDestinations];
      }

      // Apply additional filters
      let filteredResults = combinedResults;

      if (filters.priceRange && filters.type !== 'destination') {
        filteredResults = filteredResults.filter(result => {
          if (result.type === 'tour' && result.price) {
            return result.price >= filters.priceRange![0] && result.price <= filters.priceRange![1];
          }
          return true;
        });
      }

      if (filters.difficulty) {
        filteredResults = filteredResults.filter(result => {
          if (result.type === 'tour') {
            return result.difficulty === filters.difficulty;
          }
          return true;
        });
      }

      if (filters.destination) {
        filteredResults = filteredResults.filter(result =>
          result.location.toLowerCase().includes(filters.destination!.toLowerCase())
        );
      }

      // Sort results: tours first, then destinations, then by relevance
      filteredResults.sort((a, b) => {
        // Type priority: tours first
        if (a.type !== b.type) {
          return a.type === 'tour' ? -1 : 1;
        }
        
        // Within same type, sort by rating if available
        if (a.rating && b.rating) {
          return b.rating - a.rating;
        }
        
        // Fallback to alphabetical
        return a.title.localeCompare(b.title);
      });

      setSearchState(prev => ({
        ...prev,
        results: filteredResults,
        loading: false,
        showResults: filteredResults.length > 0,
        error: null,
      }));

    } catch (error) {
      console.error('Search error:', error);
      setSearchState(prev => ({
        ...prev,
        results: [],
        loading: false,
        showResults: false,
        error: 'Failed to search. Please try again.',
      }));
    }
  }, []);

  // Effect to trigger search when debounced query or filters change
  useEffect(() => {
    performSearch(debouncedQuery, searchState.filters);
  }, [debouncedQuery, searchState.filters, performSearch]);

  // Update search query
  const setQuery = useCallback((query: string) => {
    setSearchState(prev => ({
      ...prev,
      query,
    }));
  }, []);

  // Update filters
  const setFilters = useCallback((filters: Partial<EnhancedSearchFilters>) => {
    setSearchState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...filters },
    }));
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      query: '',
      results: [],
      showResults: false,
      error: null,
    }));
  }, []);

  // Hide results
  const hideResults = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      showResults: false,
    }));
  }, []);

  // Show results
  const showResults = useCallback(() => {
    if (searchState.results.length > 0 || searchState.error) {
      setSearchState(prev => ({
        ...prev,
        showResults: true,
      }));
    }
  }, [searchState.results.length, searchState.error]);

  // Memoized return object
  return useMemo(() => ({
    query: searchState.query,
    results: searchState.results,
    loading: searchState.loading,
    error: searchState.error,
    showResults: searchState.showResults,
    filters: searchState.filters,
    setQuery,
    setFilters,
    clearSearch,
    hideResults,
    showResults,
  }), [
    searchState.query,
    searchState.results,
    searchState.loading,
    searchState.error,
    searchState.showResults,
    searchState.filters,
    setQuery,
    setFilters,
    clearSearch,
    hideResults,
    showResults,
  ]);
};

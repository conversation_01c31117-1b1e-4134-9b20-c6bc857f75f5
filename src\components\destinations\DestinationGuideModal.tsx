
import React, { useState, useEffect } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MapPin, 
  Camera, 
  Bird, 
  Leaf, 
  Users, 
  Sun, 
  Cloud, 
  Eye,
  Heart,
  BookOpen,
  Calendar,
  Thermometer,
  Droplets,
  Star,
  Shield
} from 'lucide-react';
import { Destination, WildlifeInfo } from '@/types/firebase';

interface DestinationGuideModalProps {
  isOpen: boolean;
  onClose: () => void;
  destination: Destination | null;
}

const DestinationGuideModal: React.FC<DestinationGuideModalProps> = ({
  isOpen,
  onClose,
  destination
}) => {
  if (!destination) return null;

  // Sample enhanced destination data
  const enhancedDestination = {
    ...destination,
    detailedGuide: {
      overview: "The Serengeti is one of the most famous wildlife reserves in the world, covering 14,750 square kilometers of pristine savannah. Home to the Great Migration and an abundance of wildlife year-round.",
      geography: "The Serengeti consists of vast plains, woodland, and riverine forests. The landscape is dotted with rocky outcrops called 'kopjes' that serve as important wildlife viewing points.",
      history: "Established as a national park in 1951, the Serengeti has been protecting wildlife for over 70 years. The name 'Serengeti' comes from the Maasai word 'siringet', meaning 'endless plains'.",
      bestTimeToVisit: {
        drySeason: "June to October - Best for game viewing and the Great Migration",
        greenSeason: "November to May - Calving season and lush landscapes",
        photography: "Early morning and late afternoon provide the best lighting",
        birding: "November to April when migrant species are present"
      },
      gettingThere: "Fly into Seronera Airstrip or drive from Arusha (5-6 hours). Most visitors come as part of a safari circuit including Ngorongoro and Tarangire.",
      accommodation: "From luxury lodges to mobile camps, with options inside and outside the park boundaries.",
      packingTips: [
        "Neutral-colored clothing (khaki, brown, olive)",
        "Wide-brimmed hat and sunglasses",
        "High-quality binoculars",
        "Camera with telephoto lens",
        "Insect repellent and sunscreen",
        "Warm layers for early morning drives"
      ],
      healthSafety: "Yellow fever vaccination required. Malaria prophylaxis recommended. Stay in vehicles during game drives.",
      travelTips: [
        "Book accommodations well in advance",
        "Bring cash for park fees and tips",
        "Respect wildlife and maintain safe distances",
        "Support local Maasai communities",
        "Pack light for small aircraft transfers"
      ]
    },
    seasonalInfo: {
      drySeason: {
        months: ["June", "July", "August", "September", "October"],
        description: "Peak safari season with excellent game viewing",
        wildlife: "Animals concentrate around water sources, making them easier to spot",
        photography: "Clear skies and golden light, but dusty conditions",
        advantages: ["Excellent game viewing", "Clear weather", "Great Migration", "Dry roads"],
        disadvantages: ["Higher prices", "More crowds", "Dusty conditions", "Limited green scenery"]
      },
      greenSeason: {
        months: ["November", "December", "January", "February", "March", "April", "May"],
        description: "Calving season with lush landscapes and dramatic skies",
        wildlife: "Calving season for wildebeest, predator action increases",
        photography: "Dramatic skies, lush landscapes, but possible rain",
        advantages: ["Lower prices", "Fewer crowds", "Newborn animals", "Lush scenery", "Migrant birds"],
        disadvantages: ["Possible rain", "Some roads impassable", "Animals more dispersed"]
      }
    },
    wildlife: [
      {
        species: "African Lion",
        scientificName: "Panthera leo",
        category: "big-five",
        abundance: "common",
        bestSpottingTime: "Early morning and late afternoon",
        behavior: "Social cats living in prides, often seen resting during the day",
        conservationStatus: "Vulnerable",
        photographyTips: "Use telephoto lens, respect minimum distance, best light at golden hour"
      },
      {
        species: "African Elephant",
        scientificName: "Loxodonta africana",
        category: "big-five",
        abundance: "common",
        bestSpottingTime: "Throughout the day near water sources",
        behavior: "Highly social, form family groups led by matriarch",
        conservationStatus: "Endangered",
        photographyTips: "Capture family interactions, show scale with landscape"
      },
      {
        species: "Leopard",
        scientificName: "Panthera pardus",
        category: "big-five",
        abundance: "moderate",
        bestSpottingTime: "Early morning and evening, often in trees",
        behavior: "Solitary and elusive, excellent climbers",
        conservationStatus: "Near Threatened",
        photographyTips: "Look for them resting in trees, use high ISO for low light"
      }
    ] as WildlifeInfo[],
    conservationInfo: {
      initiatives: [
        "Anti-poaching patrols",
        "Community conservation programs",
        "Wildlife corridors protection",
        "Research and monitoring programs"
      ],
      challenges: [
        "Human-wildlife conflict",
        "Climate change impacts",
        "Habitat fragmentation",
        "Poaching pressure"
      ],
      howTouristsHelp: [
        "Park fees fund conservation",
        "Employment for local communities",
        "Supporting conservation organizations",
        "Following responsible tourism practices"
      ],
      conservationFee: 60
    },
    culturalInfo: {
      tribes: ["Maasai", "Sukuma", "Kurya"],
      languages: ["Swahili", "English", "Maasai"],
      traditions: ["Cattle herding", "Traditional beadwork", "Warrior ceremonies"],
      etiquette: [
        "Ask permission before photographing people",
        "Dress modestly when visiting villages",
        "Learn basic Swahili greetings",
        "Respect local customs and traditions"
      ],
      culturalSites: ["Maasai villages", "Rock art sites", "Traditional markets"]
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <MapPin className="h-6 w-6 text-orange-600" />
            <DialogTitle className="text-2xl">{destination.name} - Complete Guide</DialogTitle>
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="wildlife">Wildlife</TabsTrigger>
                <TabsTrigger value="seasons">Seasons</TabsTrigger>
                <TabsTrigger value="conservation">Conservation</TabsTrigger>
                <TabsTrigger value="culture">Culture</TabsTrigger>
                <TabsTrigger value="planning">Planning</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <img
                    src={destination.images[0]}
                    alt={destination.name}
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold text-lg mb-2">Quick Facts</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Location:</span>
                          <span>{destination.region}, {destination.country}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Best Time:</span>
                          <span>{destination.bestTimeToVisit.join(', ')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Wildlife Species:</span>
                          <span>{enhancedDestination.wildlife.length}+ species</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Conservation Fee:</span>
                          <span>${enhancedDestination.conservationInfo.conservationFee}/day</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-lg mb-3">Overview</h3>
                  <p className="text-gray-700 leading-relaxed">
                    {enhancedDestination.detailedGuide.overview}
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-lg mb-3">Geography & Landscape</h3>
                  <p className="text-gray-700 leading-relaxed">
                    {enhancedDestination.detailedGuide.geography}
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-lg mb-3">History</h3>
                  <p className="text-gray-700 leading-relaxed">
                    {enhancedDestination.detailedGuide.history}
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="wildlife" className="space-y-6">
                <div className="grid gap-4">
                  {enhancedDestination.wildlife.map((animal, index) => (
                    <Card key={index}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{animal.species}</CardTitle>
                          <div className="flex items-center gap-2">
                            <Badge 
                              variant={
                                animal.category === 'big-five' ? 'default' : 'secondary'
                              }
                              className={
                                animal.category === 'big-five' ? 'bg-orange-600' : ''
                              }
                            >
                              {animal.category === 'big-five' ? 'Big Five' : animal.category}
                            </Badge>
                            <Badge 
                              variant="outline"
                              className={
                                animal.abundance === 'common' ? 'border-green-500 text-green-700' :
                                animal.abundance === 'moderate' ? 'border-yellow-500 text-yellow-700' :
                                'border-red-500 text-red-700'
                              }
                            >
                              {animal.abundance}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 italic">{animal.scientificName}</p>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <p className="text-sm text-gray-700">{animal.behavior}</p>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium">Best Spotting Time:</span>
                              <p className="text-gray-600">{animal.bestSpottingTime}</p>
                            </div>
                            <div>
                              <span className="font-medium">Conservation Status:</span>
                              <p className="text-gray-600">{animal.conservationStatus}</p>
                            </div>
                          </div>

                          {animal.photographyTips && (
                            <div className="bg-blue-50 p-3 rounded-lg">
                              <div className="flex items-center gap-2 mb-1">
                                <Camera className="h-4 w-4 text-blue-600" />
                                <span className="font-medium text-blue-900">Photography Tips</span>
                              </div>
                              <p className="text-sm text-blue-800">{animal.photographyTips}</p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="seasons" className="space-y-6">
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Sun className="h-5 w-5 text-yellow-500" />
                        Dry Season (June - October)
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-gray-700">{enhancedDestination.seasonalInfo.drySeason.description}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium text-green-700 mb-2">Advantages</h4>
                          <ul className="space-y-1">
                            {enhancedDestination.seasonalInfo.drySeason.advantages.map((advantage, index) => (
                              <li key={index} className="text-sm text-gray-600 flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                                {advantage}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-medium text-orange-700 mb-2">Considerations</h4>
                          <ul className="space-y-1">
                            {enhancedDestination.seasonalInfo.drySeason.disadvantages.map((disadvantage, index) => (
                              <li key={index} className="text-sm text-gray-600 flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-orange-500 rounded-full" />
                                {disadvantage}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="bg-yellow-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">Wildlife & Photography</h4>
                        <p className="text-sm text-gray-700 mb-2">{enhancedDestination.seasonalInfo.drySeason.wildlife}</p>
                        <p className="text-sm text-gray-700">{enhancedDestination.seasonalInfo.drySeason.photography}</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Cloud className="h-5 w-5 text-blue-500" />
                        Green Season (November - May)
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-gray-700">{enhancedDestination.seasonalInfo.greenSeason.description}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium text-green-700 mb-2">Advantages</h4>
                          <ul className="space-y-1">
                            {enhancedDestination.seasonalInfo.greenSeason.advantages.map((advantage, index) => (
                              <li key={index} className="text-sm text-gray-600 flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                                {advantage}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-medium text-orange-700 mb-2">Considerations</h4>
                          <ul className="space-y-1">
                            {enhancedDestination.seasonalInfo.greenSeason.disadvantages.map((disadvantage, index) => (
                              <li key={index} className="text-sm text-gray-600 flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-orange-500 rounded-full" />
                                {disadvantage}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="bg-green-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">Wildlife & Photography</h4>
                        <p className="text-sm text-gray-700 mb-2">{enhancedDestination.seasonalInfo.greenSeason.wildlife}</p>
                        <p className="text-sm text-gray-700">{enhancedDestination.seasonalInfo.greenSeason.photography}</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="conservation" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5 text-green-600" />
                      Conservation Efforts
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-3">Current Initiatives</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {enhancedDestination.conservationInfo.initiatives.map((initiative, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Leaf className="h-4 w-4 text-green-600" />
                            <span className="text-sm">{initiative}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-3">Conservation Challenges</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {enhancedDestination.conservationInfo.challenges.map((challenge, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Eye className="h-4 w-4 text-orange-600" />
                            <span className="text-sm">{challenge}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                      <h4 className="font-medium text-green-800 mb-3">How Your Visit Helps</h4>
                      <div className="space-y-2">
                        {enhancedDestination.conservationInfo.howTouristsHelp.map((help, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Heart className="h-4 w-4 text-green-600" />
                            <span className="text-sm text-green-800">{help}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="culture" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-purple-600" />
                      Cultural Heritage
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-medium mb-3">Local Tribes</h4>
                        <div className="space-y-2">
                          {enhancedDestination.culturalInfo.tribes.map((tribe, index) => (
                            <Badge key={index} variant="outline" className="mr-2">
                              {tribe}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-3">Languages</h4>
                        <div className="space-y-2">
                          {enhancedDestination.culturalInfo.languages.map((language, index) => (
                            <Badge key={index} variant="outline" className="mr-2">
                              {language}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-3">Cultural Sites & Experiences</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {enhancedDestination.culturalInfo.culturalSites.map((site, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-purple-600" />
                            <span className="text-sm">{site}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-purple-50 p-4 rounded-lg">
                      <h4 className="font-medium text-purple-800 mb-3">Cultural Etiquette</h4>
                      <div className="space-y-2">
                        {enhancedDestination.culturalInfo.etiquette.map((tip, index) => (
                          <div key={index} className="flex items-start gap-2">
                            <Star className="h-4 w-4 text-purple-600 mt-0.5" />
                            <span className="text-sm text-purple-800">{tip}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="planning" className="space-y-6">
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-blue-600" />
                        Planning Your Visit
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-3">Getting There</h4>
                        <p className="text-sm text-gray-700">{enhancedDestination.detailedGuide.gettingThere}</p>
                      </div>

                      <div>
                        <h4 className="font-medium mb-3">Accommodation Options</h4>
                        <p className="text-sm text-gray-700">{enhancedDestination.detailedGuide.accommodation}</p>
                      </div>

                      <div>
                        <h4 className="font-medium mb-3">Health & Safety</h4>
                        <p className="text-sm text-gray-700">{enhancedDestination.detailedGuide.healthSafety}</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Essential Packing List</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {enhancedDestination.detailedGuide.packingTips.map((item, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-orange-500 rounded-full" />
                            <span className="text-sm">{item}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Travel Tips</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {enhancedDestination.detailedGuide.travelTips.map((tip, index) => (
                          <div key={index} className="flex items-start gap-2">
                            <BookOpen className="h-4 w-4 text-orange-600 mt-0.5" />
                            <span className="text-sm text-gray-700">{tip}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Weather Widget */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Current Conditions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Thermometer className="h-4 w-4 text-red-500" />
                      <span className="text-sm">Temperature</span>
                    </div>
                    <span className="font-medium">28°C / 82°F</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Droplets className="h-4 w-4 text-blue-500" />
                      <span className="text-sm">Humidity</span>
                    </div>
                    <span className="font-medium">65%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Eye className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Visibility</span>
                    </div>
                    <span className="font-medium">Excellent</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" variant="outline">
                  <MapPin className="h-4 w-4 mr-2" />
                  View on Map
                </Button>
                <Button className="w-full" variant="outline">
                  <Camera className="h-4 w-4 mr-2" />
                  Photo Gallery
                </Button>
                <Button className="w-full" variant="outline">
                  <Heart className="h-4 w-4 mr-2" />
                  Add to Wishlist
                </Button>
                <Button className="w-full bg-orange-600 hover:bg-orange-700">
                  <Calendar className="h-4 w-4 mr-2" />
                  Book Tour
                </Button>
              </CardContent>
            </Card>

            {/* Related Tours */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Related Tours</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {[
                  { name: 'Serengeti Safari', duration: '5 days', price: '$2,499' },
                  { name: 'Migration Special', duration: '7 days', price: '$3,299' },
                  { name: 'Photography Safari', duration: '6 days', price: '$2,899' }
                ].map((tour, index) => (
                  <div key={index} className="flex justify-between items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                    <div>
                      <div className="font-medium text-sm">{tour.name}</div>
                      <div className="text-xs text-gray-600">{tour.duration}</div>
                    </div>
                    <div className="text-sm font-medium text-orange-600">{tour.price}</div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DestinationGuideModal;

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast'; // Assuming you have this hook
import { WeatherService, WeatherResponse } from '@/services/weatherService'; // Assuming your service path
import { Home, Wind, Droplets, HeartPulse, Cloud, Sun, Zap, CloudRain, Eye } from 'lucide-react';

// The WeatherData interface from your original code
interface WeatherData {
  location: string;
  coordinates: { lat: number; lon: number };
  temperature: number;
  condition: string;
  description: string;
  humidity: number;
  windSpeed: number;
  visibility: number; // Using visibility from API
  recommendation: string; // This can be used if available
  icon: string; // API icon URL, can be used as a fallback
  forecast?: Array<{
    date: string;
    day: string;
    temp: number;
    condition: string;
    icon: string;
  }>;
}

// Icon helper to map API conditions to the design's icons
const getWeatherIcon = (condition: string, className: string, isBig: boolean = false) => {
  const lowerCaseCondition = condition.toLowerCase();

  if (lowerCaseCondition.includes('thunder')) {
    return (
      <div className={`relative ${className}`}>
        <Cloud className="w-full h-full text-gray-500" fill="currentColor" stroke="none" />
        <Zap className="w-1/2 h-1/2 absolute top-1/2 left-1 text-yellow-400" fill="currentColor" />
      </div>
    );
  }
  if (lowerCaseCondition.includes('rain') || lowerCaseCondition.includes('drizzle')) {
    return <CloudRain className={`${className} text-gray-500`} fill="currentColor" stroke="none" />;
  }
  if (lowerCaseCondition.includes('sun') || lowerCaseCondition.includes('clear')) {
    return isBig ? (
      <div className={`relative ${className}`}>
        <Cloud className="w-36 h-36 text-blue-300/70" fill="currentColor" stroke="none"/>
        <Sun className="w-20 h-20 absolute -right-3 -top-3 text-yellow-400" fill="currentColor" stroke="none"/>
      </div>
    ) : (
      <Sun className={`${className} text-yellow-400`} fill="currentColor" stroke="none" />
    );
  }
   if (lowerCaseCondition.includes('partly cloudy')) {
    return isBig ? (
        <div className={`relative ${className}`}>
            <Cloud className="w-36 h-36 text-blue-300/70" fill="currentColor" stroke="none"/>
            <Sun className="w-20 h-20 absolute -right-3 -top-3 text-yellow-400" fill="currentColor" stroke="none"/>
        </div>
    ) : (
        <div className={`relative ${className}`}>
            <Cloud className="w-full h-full text-white" fill="currentColor" stroke="none"/>
            <Sun className="w-1/2 h-1/2 absolute -right-1 -top-1 text-yellow-400" fill="currentColor" stroke="none"/>
        </div>
    );
  }
  if (lowerCaseCondition.includes('cloudy') || lowerCaseCondition.includes('overcast')) {
    return <Cloud className={`${className} ${isBig ? 'text-blue-300/70' : 'text-gray-400'}`} fill="currentColor" stroke="none" />;
  }
  
  // Fallback for other conditions like mist, fog, etc.
  return <Cloud className={`${className} ${isBig ? 'text-blue-300/70' : 'text-gray-400'}`} fill="currentColor" stroke="none"/>;
};


const WeatherWidget = () => {
  const { toast } = useToast();
  const [weather, setWeather] = useState<WeatherData[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLocation, setSelectedLocation] = useState<WeatherData | null>(null);

  useEffect(() => {
    fetchWeatherData();
  }, []);

  const fetchWeatherData = async () => {
    setLoading(true);
    try {
      const weatherResponses = await WeatherService.getSafariWeather(); // Using your service
      const weatherData = weatherResponses.map(response => WeatherService.formatWeatherData(response));
      
      setWeather(weatherData);
      setSelectedLocation(weatherData[0] || null);
    } catch (error) {
      console.error("Failed to fetch weather data:", error);
      toast({
        title: "Weather Data Error",
        description: "Unable to fetch live weather data. Please check your connection.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const otherLocations = weather.filter(loc => loc.location !== selectedLocation?.location);

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-blue-300 via-purple-300 to-orange-200 p-2 sm:p-4 lg:p-8 flex items-center justify-center font-sans">
        <Card className="w-full max-w-6xl bg-white/50 backdrop-blur-xl rounded-[1.5rem] sm:rounded-[2.5rem] p-3 sm:p-4 lg:p-6 shadow-2xl border border-white/30 animate-pulse">
            <div className="flex flex-col lg:flex-row gap-3 sm:gap-4 lg:gap-6">
                <div className="w-full lg:w-[35%] h-[400px] sm:h-[500px] lg:h-[550px] bg-gray-400/50 rounded-2xl sm:rounded-3xl"></div>
                <div className="w-full lg:w-[65%] flex flex-col gap-3 sm:gap-4 lg:gap-6">
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 lg:gap-4">
                        {[...Array(4)].map((_, i) => <div key={i} className="h-32 sm:h-40 lg:h-48 bg-gray-400/50 rounded-xl sm:rounded-2xl"></div>)}
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 sm:gap-3 lg:gap-4">
                        {[...Array(3)].map((_, i) => <div key={i} className="h-16 sm:h-20 lg:h-24 bg-gray-400/50 rounded-xl sm:rounded-2xl"></div>)}
                    </div>
                </div>
            </div>
        </Card>
      </div>
    );
  }

  if (!selectedLocation) {
     return (
        <div className="bg-gradient-to-br from-blue-300 via-purple-300 to-orange-200 p-2 sm:p-4 lg:p-8 flex items-center justify-center">
            <Card className="text-center p-4 sm:p-6 lg:p-8 rounded-[1.5rem] sm:rounded-[2.5rem]">No weather data available. Please try again later.</Card>
        </div>
    );
  }
  
  return (
    <div className=" p-2 sm:p-4 lg:p-8 flex items-center justify-center font-sans">
      <Card className="w-full max-w-6xl bg-white/50 backdrop-blur-xl rounded-[1.5rem] sm:rounded-[2.5rem] p-3 sm:p-4 lg:p-6 shadow-2xl border border-white/30">
        <div className="flex flex-col lg:flex-row gap-3 sm:gap-4 lg:gap-6">

          {/* Left Panel: Main Weather Display for selectedLocation */}
          <div className="w-full lg:w-[35%] bg-gradient-to-b from-purple-500 to-blue-700 rounded-2xl sm:rounded-3xl p-4 sm:p-6 flex flex-col text-white relative overflow-hidden shadow-lg min-h-[400px] sm:min-h-[500px] lg:min-h-[550px]">
            <div className="flex justify-between items-baseline mb-4 sm:mb-0">
                <div>
                    <div className="text-xl sm:text-2xl font-bold">{selectedLocation.location}</div>
                    <div className="text-xs sm:text-sm text-gray-200">{new Date().toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}</div>
                </div>
            </div>

            {/* Mobile Layout: Horizontal arrangement */}
            <div className="flex-grow flex lg:hidden items-center justify-between my-6">
               <div className="flex items-start">
                  <span className="text-5xl sm:text-6xl font-bold tracking-tighter">{Math.round(selectedLocation.temperature)}</span>
                  <span className="text-2xl sm:text-3xl font-bold mt-1">°</span>
               </div>
               <div className="flex flex-col items-center">
                  <div className="scale-75 sm:scale-90">
                    {getWeatherIcon(selectedLocation.condition, '', true)}
                  </div>
                  <div className="text-center text-sm sm:text-base font-medium mt-2 capitalize">
                    {selectedLocation.description}
                  </div>
               </div>
            </div>

            {/* Desktop Layout: Vertical arrangement (unchanged) */}
            <div className="my-10 flex-grow hidden lg:flex items-center justify-center relative">
               <div className="absolute left-0 top-1/2 -translate-y-1/2 flex items-start">
                  <span className="text-8xl font-bold tracking-tighter">{Math.round(selectedLocation.temperature)}</span>
                  <span className="text-3xl font-bold mt-2">°</span>
               </div>
               <div className="absolute right-0 top-1/2 -translate-y-1/2 -mr-4">
                  {getWeatherIcon(selectedLocation.condition, '', true)}
               </div>
            </div>

            <div className="text-center text-lg font-medium mb-8 -mt-4 capitalize hidden lg:block">
              {selectedLocation.description}
            </div>

            <div className="flex justify-around text-center text-xs sm:text-sm border-t border-white/20 pt-3 sm:pt-4 mt-auto">
              <div>
                <div className="font-semibold">{selectedLocation.windSpeed}km/h</div>
                <div className="text-xs text-gray-300">wind</div>
              </div>
              <div>
                <div className="font-semibold">{selectedLocation.humidity}%</div>
                <div className="text-xs text-gray-300">humidity</div>
              </div>
              <div>
                <div className="font-semibold">{selectedLocation.visibility}km</div>
                <div className="text-xs text-gray-300">visibility</div>
              </div>
            </div>

            <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-white/20 flex justify-around items-center">
              <button className="p-1.5 sm:p-2 bg-white/20 rounded-lg"><Home className="w-4 h-4 sm:w-5 sm:h-5" /></button>
              <button className="p-1.5 sm:p-2 text-gray-300 hover:text-white"><Wind className="w-4 h-4 sm:w-5 sm:h-5" /></button>
              <button className="p-1.5 sm:p-2 text-gray-300 hover:text-white"><Droplets className="w-4 h-4 sm:w-5 sm:h-5" /></button>
              <button className="p-1.5 sm:p-2 text-gray-300 hover:text-white"><HeartPulse className="w-4 h-4 sm:w-5 sm:h-5" /></button>
            </div>
          </div>

          {/* Right Panel */}
          <div className="w-full lg:w-[65%] flex flex-col gap-3 sm:gap-4 lg:gap-6">
            {/* 4-Day Forecast */}
            <div className="lg:grid lg:grid-cols-4 lg:gap-2 lg:sm:gap-3 lg:gap-4">
              {/* Mobile Layout: Flexbox for better control */}
              <div className="flex flex-wrap justify-center gap-2 sm:gap-3 lg:hidden">
                {selectedLocation.forecast?.slice(0, 4).map((day, index) => {
                  const forecastLength = selectedLocation.forecast?.length || 0;
                  const isLastOddCard = forecastLength === 3 && index === 2;

                  return (
                    <div
                      key={day.date}
                      className={`flex flex-col items-center justify-between text-center text-gray-800 p-2 sm:p-3 bg-white/40 rounded-xl sm:rounded-2xl shadow min-h-[140px] sm:min-h-[160px] ${
                        isLastOddCard ? 'w-[calc(50%-0.25rem)] sm:w-[calc(50%-0.375rem)]' : 'w-[calc(50%-0.25rem)] sm:w-[calc(50%-0.375rem)]'
                      } ${isLastOddCard ? 'mx-auto' : ''}`}
                    >
                      <div className="flex flex-col items-center space-y-0.5">
                        <div className="font-bold text-xs sm:text-sm leading-tight">{day.day}</div>
                        <div className="text-[10px] sm:text-xs text-gray-600 leading-tight">{new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</div>
                      </div>

                      <div className="flex-shrink-0 my-1">
                        <div className="scale-[0.65] sm:scale-75">
                          {day.icon ? <img src={day.icon} alt={day.condition} className="w-12 h-12 sm:w-16 sm:h-16 mx-auto" /> : getWeatherIcon(day.condition, 'w-12 h-12 sm:w-16 sm:h-16')}
                        </div>
                      </div>

                      <div className="flex flex-col items-center space-y-0.5">
                        <div className="font-semibold text-sm sm:text-base leading-tight">{Math.round(day.temp)}°C</div>
                        <div className="text-[10px] sm:text-xs font-medium capitalize leading-tight text-gray-700 px-1 text-center line-clamp-1">{day.condition}</div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Desktop Layout: Grid (unchanged) */}
              <div className="hidden lg:contents">
                {selectedLocation.forecast?.slice(0, 4).map((day) => (
                  <div key={day.date} className="flex flex-col items-center text-center text-gray-800 p-2 sm:p-3 bg-white/40 rounded-xl sm:rounded-2xl shadow">
                    <div className="font-bold text-xs sm:text-sm">{day.day}</div>
                    <div className="text-xs text-gray-600 hidden sm:block">{new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</div>
                    <div className="my-1 scale-75 sm:scale-100">
                      {day.icon ? <img src={day.icon} alt={day.condition} className="w-12 h-12 sm:w-16 sm:h-16" /> : getWeatherIcon(day.condition, 'w-12 h-12 sm:w-16 sm:h-16')}
                    </div>
                    <div className="font-semibold text-sm sm:text-base">{Math.round(day.temp)}°C</div>
                    <div className="text-xs mt-1 font-medium capitalize hidden sm:block">{day.condition}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Other Cities */}
            <div className="grid grid-cols-1 gap-2 sm:gap-3 lg:gap-4 lg:grid-cols-3">
              {otherLocations.map((city) => (
                <button
                  key={city.location}
                  onClick={() => setSelectedLocation(city)}
                  className="flex items-center p-3 sm:p-4 bg-purple-400/80 rounded-xl sm:rounded-2xl text-white shadow-md text-left transition-transform hover:scale-105"
                >
                   <div className="scale-75 sm:scale-100">
                     {getWeatherIcon(city.condition, 'w-10 h-10 sm:w-12 sm:h-12')}
                   </div>
                   <div className="ml-3 sm:ml-4">
                     <div className="font-bold text-sm sm:text-base">{city.location}</div>
                     <div className="text-xl sm:text-2xl font-light">{Math.round(city.temperature)}°C</div>
                   </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default WeatherWidget;
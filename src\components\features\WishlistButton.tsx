
import React from 'react';
import { Button } from '@/components/ui/button';
import { Heart } from 'lucide-react';
import { useWishlist } from '@/contexts/WishlistContext';
import { useLanguage } from '@/contexts/LanguageContext';

interface WishlistButtonProps {
  item: {
    id: string;
    title: string;
    price: number;
    image: string;
    type: 'tour' | 'destination';
  };
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

const WishlistButton: React.FC<WishlistButtonProps> = ({ item, size = 'default' }) => {
  const { addToWishlist, removeFromWishlist, isInWishlist, loading } = useWishlist();
  const { t } = useLanguage();
  const inWishlist = isInWishlist(item.id);

  const handleToggle = () => {
    if (inWishlist) {
      removeFromWishlist(item.id);
    } else {
      addToWishlist(item);
    }
  };

  return (
    <Button
      variant={inWishlist ? "default" : "outline"}
      size={size}
      onClick={handleToggle}
      disabled={loading}
      className={`${inWishlist ? 'bg-red-600 hover:bg-red-700' : ''}`}
    >
      <Heart className={`h-4 w-4 ${size === 'icon' ? '' : 'mr-2'} ${inWishlist ? 'fill-current' : ''}`} />
      {size !== 'icon' && (inWishlist ? 'Remove from Wishlist' : t('wishlist.add'))}
    </Button>
  );
};

export default WishlistButton;

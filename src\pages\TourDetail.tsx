import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import PageLoader from '@/components/ui/PageLoader';
import ReviewsSection from '@/components/reviews/ReviewsSection';
import TourRouteMap from '@/components/tours/TourRouteMap';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Clock, MapPin, Star, Users, Heart, Share, Loader2, Award, Shield, Camera } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Tour } from '@/types/firebase';
import { useToast } from '@/hooks/use-toast';

const TourDetail = () => {
  const { id } = useParams();
  const { toast } = useToast();
  const [tour, setTour] = useState<Tour | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(0);

  useEffect(() => {
    const loadTour = async () => {
      if (!id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const tourData = await FirebaseService.getTour(id);
        if (tourData) {
          setTour(tourData as Tour);
        } else {
          toast({
            title: "Tour not found",
            description: "The requested tour could not be found.",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error loading tour:', error);
        toast({
          title: "Error loading tour",
          description: "There was an error loading the tour details.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadTour();
  }, [id, toast]);

  if (loading) {
    return (
      <PageLoader
        title="Loading Tour Details..."
        subtitle="Preparing your safari adventure information..."
      />
    );
  }

  if (!tour) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-20 flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Tour Not Found</h1>
            <p className="text-gray-600 mb-4">The requested tour could not be found.</p>
            <Link to="/tours">
              <Button>Back to Tours</Button>
            </Link>
          </div>
        </main>
        <Footer isDarkBackground={true} />
      </div>
    );
  }

  const RatingStars = ({ rating, size = 'w-5 h-5' }: { rating: number; size?: string }) => (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${size} transition-colors duration-200 ${
            star <= rating
              ? 'fill-[#D4C2A4] text-[#D4C2A4]'
              : 'text-gray-300'
          }`}
        />
      ))}
    </div>
  );

  return (
    <div className="min-h-screen bg-[#16191D]">
      <Header />
      <main className="relative">
        {/* Luxury Hero Section */}
        <div className="relative h-[60vh] sm:h-[65vh] md:h-[75vh] lg:h-[80vh] overflow-hidden">
          {/* Main Hero Image */}
          <div className="absolute inset-0">
            <img
              src={tour.images && tour.images.length > 0
                ? `${tour.images[selectedImage]}`
                : ''
              }
              alt={tour.title}
              className="w-full h-full object-cover transition-all duration-700 ease-out"
            />
            {/* Elegant Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/95 via-[#16191D]/50 to-transparent" />
            <div className="absolute inset-0 bg-gradient-to-r from-[#16191D]/70 via-transparent to-[#16191D]/40" />
          </div>

          {/* Hero Content */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="container mx-auto px-4 sm:px-6 pt-20 sm:pt-24 pb-12 sm:pb-16 md:pb-20">
              <div className="max-w-4xl">
                {/* Luxury Badges */}
                <div className="flex flex-wrap gap-2 sm:gap-3 mb-4 sm:mb-6">
                  <Badge className="bg-[#D4C2A4]/20 text-[#D4C2A4] border border-[#D4C2A4]/30 backdrop-blur-sm font-open-sans text-xs sm:text-sm px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                    {tour.category || 'Luxury Safari'}
                  </Badge>
                  <Badge className="bg-[#F2EEE6]/10 text-[#F2EEE6] border border-[#F2EEE6]/20 backdrop-blur-sm font-open-sans text-xs sm:text-sm px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                    {tour.accommodationLevel || 'Premium'}
                  </Badge>
                  <Badge className={`backdrop-blur-sm font-open-sans text-xs sm:text-sm px-3 sm:px-4 py-1.5 sm:py-2 rounded-full border ${
                    tour.difficulty === 'easy'
                      ? 'bg-green-500/20 text-green-300 border-green-400/30'
                      : tour.difficulty === 'moderate'
                      ? 'bg-yellow-500/20 text-yellow-300 border-yellow-400/30'
                      : 'bg-red-500/20 text-red-300 border-red-400/30'
                  }`}>
                    {tour.difficulty || 'Moderate'}
                  </Badge>
                </div>

                {/* Hero Title */}
                <h1 className="font-cormorant text-3xl sm:text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-light text-[#F2EEE6] mb-4 sm:mb-6 leading-tight tracking-wide">
                  {tour.title}
                </h1>

                {/* Hero Meta Info */}
                <div className="flex flex-col sm:flex-row sm:flex-wrap gap-3 sm:gap-4 md:gap-6 lg:gap-8 text-[#F2EEE6]/80 mb-6 sm:mb-8">
                  <div className="flex items-center gap-2 sm:gap-3 font-open-sans">
                    <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4] flex-shrink-0" />
                    <span className="text-sm sm:text-base lg:text-lg">{tour.duration}</span>
                  </div>
                  <div className="flex items-center gap-2 sm:gap-3 font-open-sans">
                    <Users className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4] flex-shrink-0" />
                    <span className="text-sm sm:text-base lg:text-lg">{tour.minGroupSize}-{tour.maxGroupSize} guests</span>
                  </div>
                  <div className="flex items-center gap-2 sm:gap-3 font-open-sans">
                    <MapPin className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4] flex-shrink-0" />
                    <span className="text-sm sm:text-base lg:text-lg">{tour.destinations ? tour.destinations.join(', ') : tour.location}</span>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                  <RatingStars rating={tour.rating || 0} size="w-5 h-5 sm:w-6 sm:h-6" />
                  <span className="text-[#F2EEE6] font-open-sans text-sm sm:text-base lg:text-lg">
                    {tour.rating || 0} ({tour.reviewCount || 0} reviews)
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Elegant Image Gallery Thumbnails */}
          {tour.images && tour.images.length > 1 && (
            <div className="absolute bottom-3 sm:bottom-4 md:bottom-6 right-3 sm:right-4 md:right-6">
              <div className="flex gap-1.5 sm:gap-2">
                {tour.images.slice(0, 3).map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 lg:w-20 lg:h-20 rounded-lg overflow-hidden transition-all duration-300 ${
                      selectedImage === index
                        ? 'ring-2 ring-[#D4C2A4] scale-105'
                        : 'ring-1 ring-white/20 hover:ring-[#D4C2A4]/50 hover:scale-105'
                    }`}
                  >
                    <img
                      src={`${image}`}
                      alt={`${tour.title} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black/20" />
                  </button>
                ))}
                {tour.images.length > 3 && (
                  <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 lg:w-20 lg:h-20 rounded-lg bg-[#16191D]/80 backdrop-blur-sm border border-[#D4C2A4]/30 flex items-center justify-center">
                    <span className="text-[#D4C2A4] font-open-sans text-xs sm:text-sm">+{tour.images.length - 3}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Luxury Content Section */}
        <div className="bg-[#16191D] relative">
          {/* Subtle Pattern Overlay */}
          <div className="absolute inset-0 opacity-5">
            <div className="w-full h-full" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4C2A4' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }} />
          </div>

          <div className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 md:py-20 lg:py-24 relative">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 sm:gap-10 lg:gap-12 xl:gap-16">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-8 sm:space-y-10 lg:space-y-12">
                {/* Elegant Description Section */}
                <div className="space-y-6 sm:space-y-8">
                  <div className="space-y-4 sm:space-y-6">
                    <h2 className="font-cormorant text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-light text-[#F2EEE6] leading-tight">
                      Experience the Extraordinary
                    </h2>
                    <div className="w-16 sm:w-20 md:w-24 h-px bg-gradient-to-r from-[#D4C2A4] to-transparent"></div>
                    <p className="font-open-sans text-base sm:text-lg md:text-xl text-[#F2EEE6]/80 leading-relaxed max-w-4xl">
                      {tour.description}
                    </p>
                  </div>

                  {/* Luxury Features Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                    <div className="group">
                      <div className="bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-2xl p-4 sm:p-6 transition-all duration-300 hover:bg-[#D4C2A4]/15 hover:border-[#D4C2A4]/30">
                        <Award className="h-6 w-6 sm:h-8 sm:w-8 text-[#D4C2A4] mb-3 sm:mb-4" />
                        <h3 className="font-cormorant text-lg sm:text-xl text-[#F2EEE6] mb-2">Premium Experience</h3>
                        <p className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]/70">Curated for discerning travelers</p>
                      </div>
                    </div>
                    <div className="group">
                      <div className="bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-2xl p-4 sm:p-6 transition-all duration-300 hover:bg-[#D4C2A4]/15 hover:border-[#D4C2A4]/30">
                        <Shield className="h-6 w-6 sm:h-8 sm:w-8 text-[#D4C2A4] mb-3 sm:mb-4" />
                        <h3 className="font-cormorant text-lg sm:text-xl text-[#F2EEE6] mb-2">Expert Guides</h3>
                        <p className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]/70">Professional wildlife specialists</p>
                      </div>
                    </div>
                    <div className="group sm:col-span-2 lg:col-span-1">
                      <div className="bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-2xl p-4 sm:p-6 transition-all duration-300 hover:bg-[#D4C2A4]/15 hover:border-[#D4C2A4]/30">
                        <Camera className="h-6 w-6 sm:h-8 sm:w-8 text-[#D4C2A4] mb-3 sm:mb-4" />
                        <h3 className="font-cormorant text-lg sm:text-xl text-[#F2EEE6] mb-2">Unforgettable Moments</h3>
                        <p className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]/70">Memories that last a lifetime</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Luxury Tabs */}
                <Tabs defaultValue="itinerary" className="w-full">
                  <TabsList className="flex flex-wrap w-full h-auto p-1.5 sm:p-2 gap-1 sm:gap-2 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-2xl">
                    <TabsTrigger
                      value="itinerary"
                      className="flex-1 min-w-[80px] sm:min-w-[100px] font-open-sans text-xs sm:text-sm px-3 sm:px-4 lg:px-6 py-2 sm:py-3 rounded-xl transition-all duration-300 data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] data-[state=active]:shadow-lg text-[#F2EEE6]/70 hover:text-[#F2EEE6] hover:bg-[#D4C2A4]/20"
                    >
                      Itinerary
                    </TabsTrigger>
                    <TabsTrigger
                      value="inclusions"
                      className="flex-1 min-w-[80px] sm:min-w-[100px] font-open-sans text-xs sm:text-sm px-3 sm:px-4 lg:px-6 py-2 sm:py-3 rounded-xl transition-all duration-300 data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] data-[state=active]:shadow-lg text-[#F2EEE6]/70 hover:text-[#F2EEE6] hover:bg-[#D4C2A4]/20"
                    >
                      Details
                    </TabsTrigger>
                    <TabsTrigger
                      value="gallery"
                      className="flex-1 min-w-[80px] sm:min-w-[100px] font-open-sans text-xs sm:text-sm px-3 sm:px-4 lg:px-6 py-2 sm:py-3 rounded-xl transition-all duration-300 data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] data-[state=active]:shadow-lg text-[#F2EEE6]/70 hover:text-[#F2EEE6] hover:bg-[#D4C2A4]/20"
                    >
                      Gallery
                    </TabsTrigger>
                    <TabsTrigger
                      value="reviews"
                      className="flex-1 min-w-[80px] sm:min-w-[100px] font-open-sans text-xs sm:text-sm px-3 sm:px-4 lg:px-6 py-2 sm:py-3 rounded-xl transition-all duration-300 data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] data-[state=active]:shadow-lg text-[#F2EEE6]/70 hover:text-[#F2EEE6] hover:bg-[#D4C2A4]/20"
                    >
                      Reviews
                    </TabsTrigger>
                    <TabsTrigger
                      value="route"
                      className="flex-1 min-w-[80px] sm:min-w-[100px] font-open-sans text-xs sm:text-sm px-3 sm:px-4 lg:px-6 py-2 sm:py-3 rounded-xl transition-all duration-300 data-[state=active]:bg-[#D4C2A4] data-[state=active]:text-[#16191D] data-[state=active]:shadow-lg text-[#F2EEE6]/70 hover:text-[#F2EEE6] hover:bg-[#D4C2A4]/20"
                    >
                      <span className="hidden sm:inline">Route Map</span>
                      <span className="sm:hidden">Route</span>
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="itinerary" className="space-y-6 sm:space-y-8 mt-6 sm:mt-8">
                    {tour.itinerary && tour.itinerary.length > 0 ? (
                      tour.itinerary.map((day, index) => (
                        <div key={index} className="group">
                          <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30 hover:shadow-2xl hover:shadow-[#D4C2A4]/10">
                            {/* Day Header */}
                            <div className="flex flex-col sm:flex-row sm:items-center mb-4 sm:mb-6 gap-4 sm:gap-0">
                              <div className="relative flex-shrink-0">
                                <div className="bg-gradient-to-br from-[#D4C2A4] to-[#D4C2A4]/80 text-[#16191D] rounded-2xl w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 flex items-center justify-center sm:mr-4 lg:mr-6 shadow-lg">
                                  <span className="font-cormorant text-lg sm:text-xl lg:text-2xl font-semibold">{day.day}</span>
                                </div>
                                <div className="absolute -inset-1 bg-gradient-to-br from-[#D4C2A4]/30 to-transparent rounded-2xl blur-sm -z-10"></div>
                              </div>
                              <div className="flex-1">
                                <h3 className="font-cormorant text-xl sm:text-2xl md:text-3xl lg:text-4xl font-light text-[#F2EEE6] leading-tight">
                                  {day.title}
                                </h3>
                                <div className="w-12 sm:w-14 lg:w-16 h-px bg-gradient-to-r from-[#D4C2A4] to-transparent mt-2"></div>
                              </div>
                            </div>

                            {/* Day Description */}
                            <p className="font-open-sans text-sm sm:text-base lg:text-lg text-[#F2EEE6]/80 leading-relaxed mb-6 sm:mb-8">
                              {day.description}
                            </p>

                            {/* Day Details Grid */}
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                              <div className="space-y-3">
                                <h4 className="font-cormorant text-lg sm:text-xl text-[#D4C2A4] font-medium">Activities</h4>
                                <ul className="space-y-2">
                                  {day.activities?.map((activity, i) => (
                                    <li key={i} className="flex items-start gap-3 font-open-sans text-sm sm:text-base text-[#F2EEE6]/70">
                                      <span className="text-[#D4C2A4] mt-1.5 text-xs">●</span>
                                      <span>{typeof activity === 'string' ? activity : activity.activity}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                              <div className="space-y-3">
                                <h4 className="font-cormorant text-lg sm:text-xl text-[#D4C2A4] font-medium">Accommodation</h4>
                                <p className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/70 leading-relaxed">{day.accommodation}</p>
                              </div>
                              <div className="space-y-3 sm:col-span-2 lg:col-span-1">
                                <h4 className="font-cormorant text-lg sm:text-xl text-[#D4C2A4] font-medium">Meals</h4>
                                <p className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/70">{day.meals?.join(', ')}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-6 sm:p-8 lg:p-12 text-center">
                        <h3 className="font-cormorant text-xl sm:text-2xl text-[#F2EEE6] mb-4">Bespoke Itinerary</h3>
                        <p className="font-open-sans text-[#F2EEE6]/70 text-sm sm:text-base lg:text-lg">
                          A detailed, personalized itinerary will be crafted for you upon booking.
                        </p>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="inclusions" className="space-y-6 sm:space-y-8 mt-6 sm:mt-8">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
                      {/* What's Included */}
                      <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30">
                        <div className="flex items-center mb-4 sm:mb-6">
                          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-green-400 rounded-full mr-3 sm:mr-4"></div>
                          <h3 className="font-cormorant text-xl sm:text-2xl lg:text-3xl font-light text-[#F2EEE6]">What's Included</h3>
                        </div>
                        <div className="w-16 sm:w-20 h-px bg-gradient-to-r from-green-400 to-transparent mb-4 sm:mb-6"></div>
                        <ul className="space-y-3 sm:space-y-4">
                          {tour.includes?.map((item, index) => (
                            <li key={index} className="flex items-start gap-3 sm:gap-4 group">
                              <div className="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 bg-green-400/20 rounded-full flex items-center justify-center mt-0.5 group-hover:bg-green-400/30 transition-colors duration-300">
                                <span className="text-green-400 text-xs sm:text-sm">✓</span>
                              </div>
                              <span className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80 leading-relaxed">{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* What's Not Included */}
                      <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30">
                        <div className="flex items-center mb-4 sm:mb-6">
                          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-red-400 rounded-full mr-3 sm:mr-4"></div>
                          <h3 className="font-cormorant text-xl sm:text-2xl lg:text-3xl font-light text-[#F2EEE6]">What's Not Included</h3>
                        </div>
                        <div className="w-16 sm:w-20 h-px bg-gradient-to-r from-red-400 to-transparent mb-4 sm:mb-6"></div>
                        <ul className="space-y-3 sm:space-y-4">
                          {tour.excludes?.map((item, index) => (
                            <li key={index} className="flex items-start gap-3 sm:gap-4 group">
                              <div className="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 bg-red-400/20 rounded-full flex items-center justify-center mt-0.5 group-hover:bg-red-400/30 transition-colors duration-300">
                                <span className="text-red-400 text-xs sm:text-sm">✗</span>
                              </div>
                              <span className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80 leading-relaxed">{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    {/* Fitness Requirements */}
                    {tour.fitnessRequirements && (
                      <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30">
                        <div className="flex items-center mb-4 sm:mb-6">
                          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-[#D4C2A4] rounded-full mr-3 sm:mr-4"></div>
                          <h3 className="font-cormorant text-xl sm:text-2xl lg:text-3xl font-light text-[#F2EEE6]">Fitness Requirements</h3>
                        </div>
                        <div className="w-16 sm:w-20 h-px bg-gradient-to-r from-[#D4C2A4] to-transparent mb-4 sm:mb-6"></div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                          <div className="space-y-3 sm:space-y-4">
                            <div>
                              <h4 className="font-cormorant text-lg sm:text-xl text-[#D4C2A4] mb-2">Level</h4>
                              <p className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80">{tour.fitnessRequirements.level}</p>
                            </div>
                            <div>
                              <h4 className="font-cormorant text-lg sm:text-xl text-[#D4C2A4] mb-2">Walking Distance</h4>
                              <p className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80">{tour.fitnessRequirements.walkingDistance}</p>
                            </div>
                          </div>
                          <div className="space-y-3 sm:space-y-4">
                            <div>
                              <h4 className="font-cormorant text-lg sm:text-xl text-[#D4C2A4] mb-2">Description</h4>
                              <p className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80 leading-relaxed">{tour.fitnessRequirements.description}</p>
                            </div>
                            <div>
                              <h4 className="font-cormorant text-lg sm:text-xl text-[#D4C2A4] mb-2">Terrain</h4>
                              <p className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80">{tour.fitnessRequirements.terrain}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="gallery" className="mt-6 sm:mt-8">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                      {tour.images?.map((image, index) => (
                        <div key={index} className="group relative aspect-square rounded-3xl overflow-hidden bg-[#D4C2A4]/5 border border-[#D4C2A4]/20">
                          <img
                            src={`${image}`}
                            alt={`Gallery ${index + 1}`}
                            className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110 cursor-pointer"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500" />
                          <div className="absolute bottom-3 sm:bottom-4 left-3 sm:left-4 right-3 sm:right-4 transform translate-y-4 group-hover:translate-y-0 opacity-0 group-hover:opacity-100 transition-all duration-500">
                            <p className="font-open-sans text-white text-xs sm:text-sm">View Image {index + 1}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="reviews" className="mt-6 sm:mt-8">
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30 hover:shadow-2xl hover:shadow-[#D4C2A4]/10">
                      <div className="mb-4 sm:mb-6">
                        <div className="flex items-center mb-3 sm:mb-4">
                          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-[#D4C2A4] rounded-full mr-3 sm:mr-4"></div>
                          <h3 className="font-cormorant text-xl sm:text-2xl lg:text-3xl font-light text-[#F2EEE6]">Guest Reviews</h3>
                        </div>
                        <div className="w-16 sm:w-20 h-px bg-gradient-to-r from-[#D4C2A4] to-transparent mb-4 sm:mb-6"></div>
                        <p className="font-open-sans text-[#F2EEE6]/70 text-sm sm:text-base lg:text-lg leading-relaxed">
                          Discover what our guests say about their extraordinary safari experiences.
                        </p>
                      </div>
                      <div className="[&>*]:text-[#F2EEE6] [&>*]:font-open-sans">
                        <ReviewsSection tourId={tour.id} tourName={tour.title} />
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="route" className="mt-6 sm:mt-8">
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30 hover:shadow-2xl hover:shadow-[#D4C2A4]/10">
                      <div className="mb-4 sm:mb-6">
                        <div className="flex items-center mb-3 sm:mb-4">
                          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-[#D4C2A4] rounded-full mr-3 sm:mr-4"></div>
                          <h3 className="font-cormorant text-xl sm:text-2xl lg:text-3xl font-light text-[#F2EEE6]">Safari Route</h3>
                        </div>
                        <div className="w-16 sm:w-20 h-px bg-gradient-to-r from-[#D4C2A4] to-transparent mb-4 sm:mb-6"></div>
                        <p className="font-open-sans text-[#F2EEE6]/70 text-sm sm:text-base lg:text-lg leading-relaxed mb-4 sm:mb-6">
                          Explore the carefully crafted route of your safari adventure through Tanzania's most spectacular destinations.
                        </p>
                      </div>
                      <div className="bg-[#16191D]/30 rounded-2xl p-3 sm:p-4 border border-[#D4C2A4]/10">
                        <TourRouteMap tour={tour} />
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              {/* Luxury Booking Sidebar */}
              <div className="lg:col-span-1 order-first lg:order-last">
                <div className="lg:sticky lg:top-8">
                  <div className="bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/30 rounded-3xl p-4 sm:p-6 lg:p-8 shadow-2xl shadow-[#D4C2A4]/10">
                    {/* Price Section */}
                    <div className="flex flex-col sm:flex-row lg:flex-col justify-between items-start mb-6 sm:mb-8 gap-4 sm:gap-0 lg:gap-4">
                      <div className="space-y-1 sm:space-y-2">
                        <div className="font-cormorant text-2xl sm:text-3xl lg:text-[35px] font-light text-[#D4C2A4]">
                          ${tour.price.toLocaleString()}
                        </div>
                        <div className="font-open-sans text-[#F2EEE6]/70 text-sm sm:text-base lg:text-lg">per person</div>
                      </div>
                      <div className="flex gap-2 sm:gap-3">
                        <button className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 hover:bg-[#D4C2A4]/30 border border-[#D4C2A4]/30 rounded-xl flex items-center justify-center transition-all duration-300 group">
                          <Heart className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4] group-hover:scale-110 transition-transform duration-300" />
                        </button>
                        <button className="w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/20 hover:bg-[#D4C2A4]/30 border border-[#D4C2A4]/30 rounded-xl flex items-center justify-center transition-all duration-300 group">
                          <Share className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4] group-hover:scale-110 transition-transform duration-300" />
                        </button>
                      </div>
                    </div>

                    {/* Booking Actions */}
                    <div className="space-y-3 sm:space-y-4 mb-6 sm:mb-8">
                      <Link to={`/book/${tour.id}`} className="block">
                        <button className="w-full bg-gradient-to-r from-[#D4C2A4] to-[#D4C2A4]/90 hover:from-[#D4C2A4]/90 hover:to-[#D4C2A4] text-[#16191D] font-open-sans font-semibold text-sm sm:text-base py-3 sm:py-4 px-4 sm:px-6 rounded-2xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg hover:shadow-[#D4C2A4]/20">
                          Reserve Your Safari
                        </button>
                      </Link>

                      <Link to="/tour-builder" className="block">
                        <button className="w-full bg-transparent hover:bg-[#D4C2A4]/10 text-[#D4C2A4] border-2 border-[#D4C2A4]/50 hover:border-[#D4C2A4] font-open-sans text-sm sm:text-base py-3 sm:py-4 px-4 sm:px-6 rounded-2xl transition-all duration-300">
                          Customize Experience
                        </button>
                      </Link>
                    </div>

                    {/* Expert Consultation */}
                    <div className="border-t border-[#D4C2A4]/20 pt-4 sm:pt-6">
                      <h4 className="font-cormorant text-lg sm:text-xl lg:text-2xl text-[#F2EEE6] mb-2 sm:mb-3">Expert Consultation</h4>
                      <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm leading-relaxed mb-3 sm:mb-4">
                        Speak with our safari specialists for personalized recommendations and bespoke itinerary planning.
                      </p>
                      <button className="w-full bg-transparent hover:bg-[#D4C2A4]/10 text-[#D4C2A4] border border-[#D4C2A4]/50 hover:border-[#D4C2A4] text-xs sm:text-sm py-3 sm:py-4 px-4 sm:px-6 rounded-2xl transition-all duration-300 flex items-center justify-center gap-2 sm:gap-3">
                        <Calendar className="h-4 w-4 sm:h-5 sm:w-5" />
                        Schedule Consultation
                      </button>
                    </div>

                    {/* Trust Indicators */}
                    <div className="border-t border-[#D4C2A4]/20 pt-4 sm:pt-6 mt-4 sm:mt-6">
                      <div className="grid grid-cols-2 gap-3 sm:gap-4 text-center">
                        <div>
                          <div className="font-cormorant text-xl sm:text-2xl text-[#D4C2A4] mb-1">24/7</div>
                          <div className="font-open-sans text-xs text-[#F2EEE6]/60">Support</div>
                        </div>
                        <div>
                          <div className="font-cormorant text-xl sm:text-2xl text-[#D4C2A4] mb-1">100%</div>
                          <div className="font-open-sans text-xs text-[#F2EEE6]/60">Secure</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </main>
        <Footer isDarkBackground={true} />
      </div>
    );
  };

export default TourDetail;

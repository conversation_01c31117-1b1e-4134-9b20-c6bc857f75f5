
import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Clock, Users, Star, Calendar, ArrowRight } from 'lucide-react';

interface TourCardProps {
  tour: {
    id: string;
    title: string;
    description: string;
    price: number;
    duration: string;
    image: string;
    category: string;
    accommodationLevel: string;
    destinations: string[];
    rating: number;
    reviews: number;
  };
  viewMode?: 'grid' | 'list';
}

const TourCard: React.FC<TourCardProps> = ({ tour, viewMode = 'grid' }) => {
  // Function to truncate text with ellipsis
  const truncateText = (text: string, maxWords: number = 20) => {
    const words = text.split(' ');
    if (words.length <= maxWords) return text;
    return words.slice(0, maxWords).join(' ') + '...';
  };

  // Safely handle potentially undefined values
  const destinations = Array.isArray(tour.destinations) ? tour.destinations : [];
  const safeTitle = tour.title || 'Untitled Tour';
  const safeDescription = tour.description || 'No description available';
  const safePrice = tour.price || 0;
  const safeDuration = tour.duration || 'Duration not specified';
  const safeCategory = tour.category || 'Safari';
  const safeAccommodationLevel = tour.accommodationLevel || 'Standard';
  const safeRating = tour.rating || 0;
  const safeReviews = tour.reviews || 0;
  
  // Handle image safely
  const safeImage = tour.image ;
  const imageUrl = safeImage.includes('https') 
    ? safeImage 
    : safeImage;
  
  return (
    <Card className="group bg-[#16191D] border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 transition-all duration-500 overflow-hidden rounded-sm">
      <div className="relative overflow-hidden">
        <img
          src={imageUrl}
          alt={safeTitle}
          className={`w-full object-cover group-hover:scale-110 transition-transform duration-700 ${
            viewMode === 'grid' ? 'h-48 sm:h-56 md:h-64 lg:h-72' : 'h-40 sm:h-48'
          }`}
        />

        {/* Elegant Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Luxury Badges */}
        <div className="absolute top-3 sm:top-4 md:top-6 left-3 sm:left-4 md:left-6 flex flex-col gap-2 sm:gap-3">
          <Badge className="bg-[#D4C2A4] text-[#16191D] font-open-sans text-xs tracking-wide px-2 sm:px-3 py-1 rounded-sm">
            {safeCategory}
          </Badge>
          <Badge className="bg-[#16191D]/80 text-[#D4C2A4] border border-[#D4C2A4]/30 font-open-sans text-xs tracking-wide px-2 sm:px-3 py-1 rounded-sm backdrop-blur-sm">
            {safeAccommodationLevel}
          </Badge>
        </div>

        {/* Price Badge */}
        <div className="absolute top-3 sm:top-4 md:top-6 right-3 sm:right-4 md:right-6">
          <div className="bg-[#16191D]/90 backdrop-blur-sm border border-[#D4C2A4]/30 rounded-sm px-2 sm:px-3 md:px-4 py-1 sm:py-2">
            <div className="text-[#D4C2A4] font-cormorant text-sm sm:text-base md:text-lg font-medium">
              ${safePrice.toLocaleString()}
            </div>
            <div className="text-[#F2EEE6]/60 font-open-sans text-xs">
              per person
            </div>
          </div>
        </div>
      </div>

      <CardContent className="p-4 sm:p-6 md:p-8">
        <div className={`${viewMode === 'list' ? 'flex flex-col sm:flex-row items-start justify-between gap-4 sm:gap-6 md:gap-8' : ''}`}>
          <div className={`${viewMode === 'list' ? 'flex-1' : ''}`}>
            {/* Title Section */}
            <div className="mb-4 sm:mb-5 md:mb-6">
              <h3 className={`font-cormorant text-[#F2EEE6] group-hover:text-[#D4C2A4] transition-colors duration-300 leading-tight ${
                viewMode === 'grid' ? 'text-lg sm:text-xl md:text-2xl lg:text-3xl' : 'text-lg sm:text-xl md:text-2xl'
              }`}>
                {safeTitle}
              </h3>
              <div className="w-8 sm:w-10 md:w-12 h-px bg-[#D4C2A4] mt-2 sm:mt-3"></div>
            </div>

            <p className={`text-[#F2EEE6]/70 font-open-sans leading-relaxed mb-4 sm:mb-5 md:mb-6 ${
              viewMode === 'list' ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'
            }`}>
              {truncateText(safeDescription, viewMode === 'list' ? 15 : 25)}
            </p>

            {/* Elegant Tour Details */}
            <div className={`grid gap-3 sm:gap-4 mb-6 sm:mb-7 md:mb-8 ${
              viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2' : 'grid-cols-2 sm:grid-cols-4'
            }`}>
              <div className="flex items-center text-[#F2EEE6]/60 font-open-sans text-xs sm:text-sm">
                <Clock className="h-3 w-3 sm:h-4 sm:w-4 mr-2 sm:mr-3 text-[#D4C2A4] flex-shrink-0" />
                <span className="truncate">{safeDuration}</span>
              </div>
              <div className="flex items-center text-[#F2EEE6]/60 font-open-sans text-xs sm:text-sm">
                <Users className="h-3 w-3 sm:h-4 sm:w-4 mr-2 sm:mr-3 text-[#D4C2A4] flex-shrink-0" />
                <span className="truncate">Small Group</span>
              </div>
              <div className="flex items-center text-[#F2EEE6]/60 font-open-sans text-xs sm:text-sm">
                <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-2 sm:mr-3 text-[#D4C2A4] flex-shrink-0" />
                <span className="truncate">{destinations.length > 0 ? destinations[0] : 'Tanzania'}</span>
              </div>
              <div className="flex items-center text-[#F2EEE6]/60 font-open-sans text-xs sm:text-sm">
                <Star className="h-3 w-3 sm:h-4 sm:w-4 mr-2 sm:mr-3 text-[#D4C2A4] fill-current flex-shrink-0" />
                <span className="truncate">{safeRating} ({safeReviews})</span>
              </div>
            </div>
          </div>

          {/* List View Actions */}
          {viewMode === 'list' && (
            <div className="flex flex-col sm:items-end justify-between w-full sm:w-auto">
              <div className="text-left sm:text-right mb-4 sm:mb-6">
                <div className="text-xl sm:text-2xl font-cormorant text-[#D4C2A4] font-medium mb-1">
                  ${safePrice.toLocaleString()}
                </div>
                <div className="text-[#F2EEE6]/60 font-open-sans text-xs sm:text-sm">per person</div>
              </div>
              <div className="flex flex-row sm:flex-col gap-2 sm:gap-3 w-full sm:min-w-[160px]">
                <Button
                  asChild
                  className="flex-1 sm:flex-none bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 font-open-sans font-medium rounded-sm transition-all duration-300 text-xs sm:text-sm py-2 sm:py-3"
                >
                  <Link to={`/tours/${tour.id}`} className="flex items-center justify-center">
                    <span className="hidden sm:inline">View Details</span>
                    <span className="sm:hidden">Details</span>
                    <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  className="flex-1 sm:flex-none border-[#D4C2A4]/30 text-[#D4C2A4] hover:border-[#D4C2A4] hover:bg-[#D4C2A4]/10 font-open-sans rounded-sm transition-all duration-300 text-xs sm:text-sm py-2 sm:py-3"
                >
                  <Link to={`/book/${tour.id}`} className="flex items-center justify-center">
                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Book Now</span>
                    <span className="sm:hidden">Book</span>
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Grid View Actions */}
        {viewMode === 'grid' && (
          <div className="flex gap-3 sm:gap-4">
            <Button
              asChild
              className="flex-1 bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 font-open-sans font-medium py-2 sm:py-3 rounded-sm transition-all duration-300 text-xs sm:text-sm"
            >
              <Link to={`/tours/${tour.id}`} className="flex items-center justify-center">
                <span className="hidden sm:inline">View Details</span>
                <span className="sm:hidden">Details</span>
                <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              className="border-[#D4C2A4]/30 text-[#D4C2A4] hover:border-[#D4C2A4] hover:bg-[#D4C2A4]/10 font-open-sans py-2 sm:py-3 px-4 sm:px-6 rounded-sm transition-all duration-300 text-xs sm:text-sm"
            >
              <Link to={`/book/${tour.id}`} className="flex items-center justify-center">
                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Book</span>
                <span className="sm:hidden">Book</span>
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TourCard;

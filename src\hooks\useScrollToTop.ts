import { useCallback } from 'react';

/**
 * Custom hook that provides a function to scroll to the top of the page
 * Can be used for manual scroll-to-top functionality in navigation links
 */
export const useScrollToTop = () => {
  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'instant' // Use 'instant' for immediate scroll
    });
  }, []);

  const smoothScrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth' // Use 'smooth' for animated scroll
    });
  }, []);

  return { scrollToTop, smoothScrollToTop };
};

/**
 * Utility function to scroll to top - can be used directly without hook
 */
export const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: 'instant'
  });
};

/**
 * Utility function for smooth scroll to top
 */
export const smoothScrollToTop = () => {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: 'smooth'
  });
};

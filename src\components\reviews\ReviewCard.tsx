
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Star, ThumbsUp, Flag } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Review {
  id: string;
  author: string;
  avatar?: string;
  rating: number;
  title: string;
  content: string;
  date: string;
  tourName: string;
  verified: boolean;
  helpful: number;
  photos?: string[];
}

interface ReviewCardProps {
  review: Review;
  onHelpful: (reviewId: string) => void;
  onReport: (reviewId: string) => void;
}

const ReviewCard: React.FC<ReviewCardProps> = ({ review, onHelpful, onReport }) => {
  return (
    <Card className="mb-4">
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          <Avatar className="h-12 w-12">
            <AvatarImage src={review.avatar} alt={review.author} />
            <AvatarFallback>{review.author.charAt(0).toUpperCase()}</AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <h4 className="font-semibold">{review.author}</h4>
                {review.verified && (
                  <Badge variant="secondary" className="text-xs">
                    Verified Traveler
                  </Badge>
                )}
              </div>
              <span className="text-sm text-gray-500">{review.date}</span>
            </div>

            <div className="flex items-center space-x-2 mb-2">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star 
                    key={i} 
                    className={`h-4 w-4 ${i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                  />
                ))}
              </div>
              <span className="text-sm text-gray-600">for {review.tourName}</span>
            </div>

            <h5 className="font-medium mb-2">{review.title}</h5>
            <p className="text-gray-700 mb-4">{review.content}</p>

            {review.photos && review.photos.length > 0 && (
              <div className="flex space-x-2 mb-4">
                {review.photos.map((photo, index) => (
                  <img
                    key={index}
                    src={`https://images.unsplash.com/${photo}?auto=format&fit=crop&w=100&h=100`}
                    alt={`Review photo ${index + 1}`}
                    className="w-16 h-16 rounded-lg object-cover cursor-pointer hover:opacity-80"
                  />
                ))}
              </div>
            )}

            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onHelpful(review.id)}
                className="flex items-center space-x-1 text-gray-500 hover:text-gray-700"
              >
                <ThumbsUp className="h-4 w-4" />
                <span>Helpful ({review.helpful})</span>
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onReport(review.id)}
                className="flex items-center space-x-1 text-gray-500 hover:text-red-600"
              >
                <Flag className="h-4 w-4" />
                <span>Report</span>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReviewCard;

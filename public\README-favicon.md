# Favicon Setup for Warriors of Africa Safari

## Current Status
- 🔄 **NEEDS UPDATE**: Replace current safari-themed favicons with actual logo
- ✅ HTML favicon links configured correctly
- ✅ Web manifest configured for PWA support
- ❌ Favicon files need to be regenerated from logo.png

## Required Actions
To replace the current safari-themed favicons with the actual Warriors of Africa Safari logo:

### Step 1: Generate Favicon Files from Logo
Use the existing `/logo.png` file to create favicon files in multiple formats:

1. **Using Online Tools** (Recommended):
   - Visit [favicon.io](https://favicon.io/favicon-converter/) or [realfavicongenerator.net](https://realfavicongenerator.net/)
   - Upload the `/public/logo.png` file
   - Generate favicon package with these sizes:
     - `favicon.ico` (16x16, 32x32, 48x48)
     - `favicon-16x16.png`
     - `favicon-32x32.png`
     - `apple-touch-icon.png` (180x180)
     - `android-chrome-192x192.png`
     - `android-chrome-512x512.png`

2. **Using ImageMagick** (Command Line):
   ```bash
   # Convert logo to ICO format
   magick logo.png -resize 32x32 favicon.ico

   # Create different sizes
   magick logo.png -resize 16x16 favicon-16x16.png
   magick logo.png -resize 32x32 favicon-32x32.png
   magick logo.png -resize 180x180 apple-touch-icon.png
   magick logo.png -resize 192x192 android-chrome-192x192.png
   magick logo.png -resize 512x512 android-chrome-512x512.png
   ```

### Step 2: Replace Current Files
Replace these files in the `/public` directory:
- `favicon.svg` → `favicon.ico` (or keep both)
- `favicon-16x16.svg` → `favicon-16x16.png`
- `favicon-32x32.svg` → `favicon-32x32.png`
- `apple-touch-icon.svg` → `apple-touch-icon.png`

### Step 3: Update Web Manifest
Update `/public/site.webmanifest` to reference the new PNG files instead of SVG files.

## Browser Support
- ✅ Modern browsers: ICO and PNG favicons work perfectly
- ✅ Older browsers: ICO format provides maximum compatibility
- ✅ Mobile devices: Apple touch icon and Android chrome icons configured
- ✅ PWA support: Web manifest with proper icon references

## Benefits of Using Actual Logo
- ✅ Brand consistency across all touchpoints
- ✅ Professional appearance in browser tabs
- ✅ Recognizable brand identity
- ✅ Better user experience and brand recognition

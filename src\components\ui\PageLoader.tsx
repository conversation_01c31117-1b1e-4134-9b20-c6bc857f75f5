import React from 'react';

interface PageLoaderProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

const PageLoader: React.FC<PageLoaderProps> = ({ 
  title = "Loading...", 
  subtitle = "Please wait while we prepare your experience...",
  className = ""
}) => {
  return (
    <div className={`bg-white font-sans text-dark text-[14px] leading-[1.3] antialiased ${className}`}>
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="font-cinzel text-[27px] tracking-[-1px] mb-4">{title}</div>
          <div className="animate-pulse">{subtitle}</div>
        </div>
      </div>
    </div>
  );
};

export default PageLoader;

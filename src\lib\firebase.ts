
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

// Firebase configuration - replace with your actual config
const firebaseConfig = {
   apiKey: "AIzaSyA0ADH5cc2d6qm1ZjTjX5NahRh-jC5f9Js",
  authDomain: "warriorsofafricasafari-5bb0d.firebaseapp.com",
  projectId: "warriorsofafricasafari-5bb0d",
  storageBucket: "warriorsofafricasafari-5bb0d.firebasestorage.app",
  messagingSenderId: "1073594385027",
  appId: "1:1073594385027:web:8470df53efcae58fd3d1b6",
  measurementId: "G-KV2GPTWPDE"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

export default app;

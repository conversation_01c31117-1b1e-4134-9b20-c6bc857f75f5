/* Last Section Luxury Enhancements */

/* Enhanced luxury glass container for last section */
.luxury-glass-container {
  background: linear-gradient(135deg, 
    rgba(212, 194, 164, 0.12) 0%,
    rgba(212, 194, 164, 0.08) 50%,
    rgba(212, 194, 164, 0.15) 100%);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(212, 194, 164, 0.3);
  box-shadow: 
    0 25px 50px rgba(22, 25, 29, 0.4),
    0 0 0 1px rgba(212, 194, 164, 0.1),
    inset 0 1px 0 rgba(212, 194, 164, 0.2),
    inset 0 -1px 0 rgba(212, 194, 164, 0.1);
  position: relative;
  overflow: hidden;
}

.luxury-glass-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.6), 
    transparent);
  animation: luxuryShimmer 3s ease-in-out infinite;
}

.luxury-glass-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.1), 
    transparent);
  animation: luxurySlide 4s ease-in-out infinite;
}

/* Premium Button Enhancements */
.luxury-button {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #D4C2A4 0%, #C4B294 50%, #D4C2A4 100%);
  background-size: 200% 100%;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: 
    0 4px 15px rgba(212, 194, 164, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.luxury-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent);
  transition: left 0.6s ease;
}

.luxury-button:hover::before {
  left: 100%;
}

.luxury-button:hover {
  background-position: 100% 0;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 
    0 15px 30px rgba(212, 194, 164, 0.4),
    0 0 0 1px rgba(212, 194, 164, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* VIP Glow Effects */
@keyframes luxuryGlow {
  0%, 100% {
    text-shadow: 
      0 0 10px rgba(212, 194, 164, 0.4),
      0 0 20px rgba(212, 194, 164, 0.2),
      0 0 30px rgba(212, 194, 164, 0.1);
  }
  50% {
    text-shadow: 
      0 0 15px rgba(212, 194, 164, 0.6),
      0 0 30px rgba(212, 194, 164, 0.4),
      0 0 45px rgba(212, 194, 164, 0.2);
  }
}

.luxury-glow-text {
  animation: luxuryGlow 4s ease-in-out infinite;
}

/* Premium Typography Effects */
.luxury-typography {
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.02em;
}

/* Elegant Border Animations */
@keyframes luxuryBorderGlow {
  0%, 100% {
    border-color: rgba(212, 194, 164, 0.4);
    box-shadow: 0 0 10px rgba(212, 194, 164, 0.2);
  }
  50% {
    border-color: rgba(212, 194, 164, 0.8);
    box-shadow: 0 0 25px rgba(212, 194, 164, 0.4);
  }
}

.luxury-border-glow {
  animation: luxuryBorderGlow 3s ease-in-out infinite;
}

/* Premium Shimmer Animation */
@keyframes luxuryShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Luxury Slide Animation */
@keyframes luxurySlide {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive Design for Mobile Devices */
@media (max-width: 768px) {
  .luxury-glass-container {
    backdrop-filter: blur(20px) saturate(150%);
    -webkit-backdrop-filter: blur(20px) saturate(150%);
    margin: 1rem;
    border-radius: 1rem;
  }
  
  .luxury-glow-text {
    animation-duration: 5s;
  }
  
  .luxury-button:hover {
    transform: translateY(-2px) scale(1.01);
  }
}

@media (max-width: 640px) {
  .luxury-glass-container {
    backdrop-filter: blur(15px) saturate(130%);
    -webkit-backdrop-filter: blur(15px) saturate(130%);
    padding: 1.5rem !important;
    margin: 0.75rem;
  }
  
  .luxury-button {
    min-height: 48px;
    font-size: 0.875rem;
  }
  
  .luxury-border-glow {
    animation-duration: 4s;
  }
}

@media (max-width: 480px) {
  .luxury-glass-container {
    backdrop-filter: blur(12px) saturate(120%);
    -webkit-backdrop-filter: blur(12px) saturate(120%);
    padding: 1.25rem !important;
    margin: 0.5rem;
    border-radius: 0.75rem;
  }
  
  .luxury-button:hover {
    transform: translateY(-1px);
  }
  
  .luxury-glow-text {
    animation: none;
    text-shadow: 0 0 12px rgba(212, 194, 164, 0.4);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .luxury-glass-container {
    background: rgba(212, 194, 164, 0.2);
    border-color: rgba(212, 194, 164, 0.6);
  }
  
  .luxury-button {
    background: #D4C2A4;
    border: 2px solid #16191D;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .luxury-glow-text,
  .luxury-border-glow,
  .luxury-glass-container::before,
  .luxury-glass-container::after {
    animation: none;
  }
  
  .luxury-button::before {
    transition: none;
  }
  
  .luxury-button:hover {
    transform: none;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .luxury-button:hover {
    transform: scale(0.98);
  }
  
  .luxury-glass-container {
    -webkit-tap-highlight-color: rgba(212, 194, 164, 0.1);
  }
}

/* Safari-specific enhancements */
@supports (-webkit-backdrop-filter: blur(25px)) {
  .luxury-glass-container {
    -webkit-backdrop-filter: blur(25px) saturate(180%);
  }
}

/* Enhanced focus states for accessibility */
.luxury-button:focus-visible {
  outline: 2px solid #D4C2A4;
  outline-offset: 2px;
  box-shadow: 
    0 0 0 4px rgba(212, 194, 164, 0.2),
    0 15px 30px rgba(212, 194, 164, 0.4);
}

/* Luxury particle animations */
@keyframes luxuryFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px) scale(1.1);
    opacity: 0.6;
  }
}

.luxury-particle {
  animation: luxuryFloat 6s ease-in-out infinite;
}

.luxury-particle:nth-child(2) {
  animation-delay: 2s;
}

.luxury-particle:nth-child(3) {
  animation-delay: 4s;
}

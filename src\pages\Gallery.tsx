
import React, { useState, useMemo, useEffect, useRef } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import PageLoader from '@/components/ui/PageLoader';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, MapPin, Camera, Heart, Share2, Filter, Grid, List, X } from 'lucide-react';
import { useGallery } from '@/hooks/useGallery';
import ScrollingImageGallery from '@/components/gallery/ScrollingImageGallery';
import '@/styles/gallery.css';

const Gallery = () => {
  const { images, loading, error } = useGallery();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImageData, setSelectedImageData] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'scrolling'>('scrolling');
  const [showFilters, setShowFilters] = useState(false);

  // Filter images based on search and category
  const filteredImages = useMemo(() => images.filter(image => {
      const matchesSearch = image.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           image.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           image.location.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = !selectedCategory || image.category === selectedCategory;
      return matchesSearch && matchesCategory;
    }), [images, searchTerm, selectedCategory]);

  // Get unique categories
  const categories = useMemo(() => Array.from(new Set(images.map(image => image.category))), [images]);

  const handleImageClick = (image: any) => {
    setSelectedImage(image.url);
    setSelectedImageData(image);
  };

  if (loading) {
    return (
      <PageLoader
        title="Curating Extraordinary Moments..."
        subtitle="Unveiling the artistry of African wilderness"
      />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#16191D]">
        <Header />
        <main className="">
          <div className="relative overflow-hidden bg-[#16191D] pt-16 sm:pt-20 py-12 sm:py-16 md:py-20 lg:py-24">
            {/* Elegant Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                                 radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
                backgroundSize: '40px 40px'
              }} />
            </div>
            <div className="relative z-10 container mx-auto px-4 text-center">
              <h1 className="font-cormorant text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-semibold mb-4 sm:mb-6 tracking-wide text-[#F2EEE6]">
                Gallery Unavailable
              </h1>
              <p className="font-open-sans text-sm sm:text-base md:text-lg lg:text-xl text-[#F2EEE6]/80 max-w-xl sm:max-w-2xl mx-auto leading-relaxed px-2">
                We're experiencing technical difficulties. Please try again.
              </p>
            </div>
          </div>
          <div className="container mx-auto px-4 py-12 sm:py-16 md:py-20 text-center">
            <p className="font-open-sans text-[#D4C2A4] mb-6 sm:mb-8 text-sm sm:text-base md:text-lg px-2">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              className="bg-[#D4C2A4] hover:bg-[#D4C2A4]/80 text-[#16191D] font-open-sans px-6 sm:px-8 py-2 sm:py-3 rounded-xl sm:rounded-2xl transition-all duration-300 hover:shadow-lg text-sm sm:text-base"
            >
              Try Again
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#16191D]">
      <Header />
      <main className="">
        {/* Luxury Hero Section */}
        <div className="relative overflow-hidden bg-[#16191D] pt-16 sm:pt-20">
          {/* Elegant Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                               radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
              backgroundSize: '40px 40px'
            }} />
          </div>

          <div className="relative z-10 container mx-auto px-4 py-12 sm:py-16 md:py-20 lg:py-24 text-center">
            <div className="max-w-4xl mx-auto">
              {/* Luxury Badge */}
              <div className="inline-flex items-center gap-1.5 sm:gap-2 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-3 sm:px-4 md:px-6 py-1.5 sm:py-2 mb-6 sm:mb-8">
                <Camera className="w-3 h-3 sm:w-4 sm:h-4 text-[#D4C2A4]" />
                <span className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wider uppercase">Curated Collection</span>
              </div>

              <h1 className="font-cormorant text-3xl sm:text-4xl md:text-5xl lg:text-7xl xl:text-8xl text-[#F2EEE6] mb-4 sm:mb-6 leading-tight">
                Safari
                <span className="block text-[#D4C2A4] italic">Gallery</span>
              </h1>

              <p className="font-open-sans text-sm sm:text-base md:text-lg lg:text-xl text-[#F2EEE6]/80 max-w-xl sm:max-w-2xl mx-auto leading-relaxed mb-6 sm:mb-8 px-2">
                An exquisite collection of moments where wilderness meets artistry,
                capturing the soul of Africa's untamed beauty
              </p>

              {/* Elegant Divider */}
              <div className="flex items-center justify-center gap-2 sm:gap-4 mb-8 sm:mb-12">
                <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent w-16 sm:w-24"></div>
                <Camera className="w-4 h-4 sm:w-6 sm:h-6 text-[#D4C2A4]" />
                <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent w-16 sm:w-24"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Luxury Controls Section */}
        <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border-t border-b border-[#D4C2A4]/20">
          <div className="container mx-auto px-4 py-8 sm:py-12 md:py-16">
            <div className="max-w-6xl mx-auto">

              {/* Section Header */}
              <div className="text-center mb-8 sm:mb-10 md:mb-12">
                <h2 className="font-cormorant text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold text-[#F2EEE6] mb-3 sm:mb-4">
                  Discover & Explore
                </h2>
                <p className="font-open-sans text-[#F2EEE6]/70 text-sm sm:text-base md:text-lg max-w-xl sm:max-w-2xl mx-auto leading-relaxed px-2">
                  Navigate through our carefully curated collection of extraordinary safari moments
                </p>
              </div>

              {/* Search and View Controls */}
              <div className="mb-8 sm:mb-10 md:mb-12 space-y-6 sm:space-y-8">
                <div className="flex flex-col lg:flex-row gap-4 sm:gap-6 items-center">

                  {/* Elegant Search Bar */}
                  <div className="relative flex-1 max-w-2xl w-full">
                    <div className="absolute inset-0 bg-[#D4C2A4]/5 backdrop-blur-sm rounded-xl sm:rounded-2xl"></div>
                    <div className="relative bg-[#D4C2A4]/10 border border-[#D4C2A4]/20 focus-within:border-[#D4C2A4]/40 transition-all duration-300 rounded-xl sm:rounded-2xl">
                      <Search className="absolute left-4 sm:left-6 top-1/2 h-4 w-4 sm:h-5 sm:w-5 -translate-y-1/2 text-[#D4C2A4]/80" />
                      <Input
                        type="text"
                        placeholder="Search by title, location, or photographer..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-12 sm:pl-16 pr-4 sm:pr-6 py-3 sm:py-4 text-sm sm:text-base font-open-sans border-0 bg-transparent focus:ring-0 focus:outline-none placeholder:text-[#F2EEE6]/50 text-[#F2EEE6] rounded-xl sm:rounded-2xl"
                      />
                    </div>
                  </div>

                  {/* View Mode Toggle */}
                  <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4 w-full sm:w-auto">
                    <div className="bg-[#D4C2A4]/10 border border-[#D4C2A4]/20 p-1 flex rounded-xl sm:rounded-2xl w-full sm:w-auto">
                      <button
                        onClick={() => setViewMode('scrolling')}
                        className={`flex-1 sm:flex-none px-3 sm:px-4 py-2 font-open-sans text-xs sm:text-sm transition-all duration-300 rounded-lg sm:rounded-xl ${
                          viewMode === 'scrolling'
                            ? 'bg-[#D4C2A4] text-[#16191D]'
                            : 'text-[#F2EEE6] hover:bg-[#D4C2A4]/20'
                        }`}
                      >
                        <List className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 inline" />
                        <span className="hidden sm:inline">Scrolling</span>
                        <span className="sm:hidden">Scroll</span>
                      </button>
                      <button
                        onClick={() => setViewMode('grid')}
                        className={`flex-1 sm:flex-none px-3 sm:px-4 py-2 font-open-sans text-xs sm:text-sm transition-all duration-300 rounded-lg sm:rounded-xl ${
                          viewMode === 'grid'
                            ? 'bg-[#D4C2A4] text-[#16191D]'
                            : 'text-[#F2EEE6] hover:bg-[#D4C2A4]/20'
                        }`}
                      >
                        <Grid className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 inline" />
                        Grid
                      </button>
                    </div>

                    {/* Filter Toggle */}
                    <Button
                      onClick={() => setShowFilters(!showFilters)}
                      variant="outline"
                      className="bg-[#D4C2A4] border-[#D4C2A4]/20 text-[#F2EEE6] hover:bg-[#D4C2A4] hover:text-[#16191D] transition-all duration-300 font-open-sans rounded-xl sm:rounded-2xl px-3 sm:px-4 py-2 text-xs sm:text-sm w-full sm:w-auto"
                    >
                      <Filter className="w-3  sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                      Filters
                    </Button>

                    {searchTerm && (
                      <Button
                        onClick={() => setSearchTerm('')}
                        variant="ghost"
                        className="text-[#F2EEE6] hover:bg-[#D4C2A4]/20 font-open-sans rounded-xl sm:rounded-2xl px-3 sm:px-4 py-2 text-xs sm:text-sm w-full sm:w-auto"
                      >
                        <X className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                        Clear
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {/* Luxury Category Filter */}
              {showFilters && (
                <div className="border-t border-[#D4C2A4]/20 pt-6 sm:pt-8 mt-6 sm:mt-8">
                  <h3 className="font-cormorant text-xl sm:text-2xl font-semibold text-[#F2EEE6] mb-4 sm:mb-6">
                    Categories
                  </h3>
                  <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4">
                    <button
                      onClick={() => setSelectedCategory('')}
                      className={`px-3 sm:px-4 md:px-6 py-2 sm:py-3 font-open-sans text-xs sm:text-sm transition-all duration-300 border rounded-xl sm:rounded-2xl ${
                        selectedCategory === ''
                          ? 'bg-[#D4C2A4] text-[#16191D] border-[#D4C2A4] shadow-lg'
                          : 'bg-[#D4C2A4]/10 text-[#F2EEE6] border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 hover:bg-[#D4C2A4]/20'
                      }`}
                    >
                      All Categories
                      <span className="ml-1 sm:ml-2 text-xs opacity-70">({images.length})</span>
                    </button>
                    {categories.map((category) => {
                      const categoryCount = images.filter(img => img.category === category).length;
                      return (
                        <button
                          key={category}
                          onClick={() => setSelectedCategory(category)}
                          className={`px-3 sm:px-4 md:px-6 py-2 sm:py-3 font-open-sans text-xs sm:text-sm transition-all duration-300 border rounded-xl sm:rounded-2xl ${
                            selectedCategory === category
                              ? 'bg-[#D4C2A4] text-[#16191D] border-[#D4C2A4] shadow-lg'
                              : 'bg-[#D4C2A4]/10 text-[#F2EEE6] border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 hover:bg-[#D4C2A4]/20'
                          }`}
                        >
                          {category}
                          <span className="ml-1 sm:ml-2 text-xs opacity-70">({categoryCount})</span>
                        </button>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Elegant Results Summary */}
              {(searchTerm || selectedCategory) && (
                <div className="mt-6 sm:mt-8 bg-[#D4C2A4]/10 backdrop-blur-sm border-l-4 border-[#D4C2A4] p-4 sm:p-6 rounded-r-xl sm:rounded-r-2xl">
                  <p className="font-open-sans text-[#F2EEE6] text-sm sm:text-base">
                    <span className="font-semibold text-base sm:text-lg text-[#D4C2A4]">{filteredImages.length}</span>
                    <span className="text-[#F2EEE6]/80"> exquisite moments found</span>
                    {searchTerm && <span className="text-[#F2EEE6]/80"> matching "{searchTerm}"</span>}
                    {selectedCategory && <span className="text-[#F2EEE6]/80"> in {selectedCategory}</span>}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Gallery Content */}
        <div className="bg-[#16191D] py-10 sm:py-12 md:py-16 lg:py-20">
          <div className="container mx-auto px-4">
            {filteredImages.length === 0 ? (
              <div className="text-center py-12 sm:py-16 md:py-20">
                <div className="max-w-md mx-auto">
                  <div className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 mx-auto mb-6 sm:mb-8 bg-[#D4C2A4]/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                    <Camera className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-[#D4C2A4]" />
                  </div>
                  <h3 className="font-cormorant text-2xl sm:text-3xl font-semibold text-[#F2EEE6] mb-3 sm:mb-4">
                    {images.length === 0 ? 'Gallery Coming Soon' : 'No Matches Found'}
                  </h3>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-sm sm:text-base md:text-lg mb-6 sm:mb-8 leading-relaxed px-2">
                    {images.length === 0
                      ? 'We\'re curating extraordinary safari moments for you. Please check back soon.'
                      : 'Try adjusting your search criteria or explore different categories.'}
                  </p>
                  {searchTerm && (
                    <Button
                      onClick={() => setSearchTerm('')}
                      className="bg-[#D4C2A4] hover:bg-[#D4C2A4]/80 text-[#16191D] font-open-sans px-6 sm:px-8 py-2 sm:py-3 transition-all duration-300 rounded-xl sm:rounded-2xl text-sm sm:text-base"
                    >
                      Clear Search
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <div className="mb-8 sm:mb-10 md:mb-12">
                {viewMode === 'scrolling' ? (
                  <ScrollingImageGallery
                    images={filteredImages}
                    onImageClick={handleImageClick}
                  />
                ) : (
                  <div className="masonry-container">
                    {filteredImages.map((image) => (
                      <div
                        key={image.id}
                        className="masonry-item group cursor-pointer"
                        onClick={() => handleImageClick(image)}
                      >
                        <div className="relative overflow-hidden bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 shadow-lg hover:shadow-2xl transition-all duration-500 group-hover:-translate-y-2 rounded-xl sm:rounded-2xl">
                          <img
                            src={image.url}
                            alt={image.title}
                            className="w-full h-auto object-cover transition-transform duration-700 group-hover:scale-105 rounded-xl sm:rounded-2xl"
                            loading="lazy"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl sm:rounded-2xl"></div>
                          <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 md:p-6 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                            <h3 className="font-cormorant text-lg sm:text-xl font-semibold mb-1 sm:mb-2">{image.title}</h3>
                            <p className="font-open-sans text-xs sm:text-sm opacity-90 mb-1 sm:mb-2">{image.location}</p>
                            <Badge className="bg-white/20 text-white border-white/30 font-open-sans text-xs">
                              {image.category}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Luxury Modal for full-size image */}
        {selectedImage && selectedImageData && (
          <div
            className="fixed inset-0 bg-[#16191D]/95 backdrop-blur-sm z-50 flex items-center justify-center p-3 sm:p-4 md:p-6 modal-backdrop"
            onClick={() => {
              setSelectedImage(null);
              setSelectedImageData(null);
            }}
          >
            <div className="relative max-w-7xl max-h-full w-full flex flex-col xl:flex-row gap-4 sm:gap-6 md:gap-8 modal-content">

              {/* Image Container */}
              <div className="flex-1 flex items-center justify-center">
                <div className="relative">
                  <img
                    src={selectedImage}
                    alt={selectedImageData.title}
                    className="max-w-full max-h-[70vh] sm:max-h-[75vh] md:max-h-[80vh] xl:max-h-[85vh] object-contain shadow-2xl rounded-lg sm:rounded-xl md:rounded-2xl"
                    onClick={(e) => e.stopPropagation()}
                  />
                  {/* Elegant image border */}
                  <div className="absolute inset-0 border-2 border-[#D4C2A4]/30 pointer-events-none rounded-lg sm:rounded-xl md:rounded-2xl"></div>
                </div>
              </div>

              {/* Luxury Details Sidebar */}
              <div
                className="xl:w-96 bg-[#D4C2A4]/10 backdrop-blur-xl border border-[#D4C2A4]/20 max-h-[70vh] sm:max-h-[75vh] md:max-h-[80vh] xl:max-h-[85vh] overflow-y-auto modal-sidebar rounded-xl sm:rounded-2xl"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-4 sm:p-6 md:p-8 space-y-6 sm:space-y-8">

                  {/* Header */}
                  <div className="border-b border-[#D4C2A4]/20 pb-4 sm:pb-6">
                    <h2 className="font-cormorant text-2xl sm:text-3xl font-semibold text-[#F2EEE6] mb-2 sm:mb-3 leading-tight">
                      {selectedImageData.title}
                    </h2>
                    <p className="font-open-sans text-[#F2EEE6]/80 leading-relaxed text-sm sm:text-base">
                      {selectedImageData.description}
                    </p>
                  </div>

                  {/* Details */}
                  <div className="space-y-4 sm:space-y-6">
                    <div className="flex items-start gap-3 sm:gap-4">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0">
                        <Camera className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4]" />
                      </div>
                      <div>
                        <p className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]/60 uppercase tracking-wide mb-1">
                          Photographer
                        </p>
                        <p className="font-cormorant text-base sm:text-lg text-[#F2EEE6] font-medium">
                          {selectedImageData.photographer}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 sm:gap-4">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0">
                        <MapPin className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4]" />
                      </div>
                      <div>
                        <p className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]/60 uppercase tracking-wide mb-1">
                          Location
                        </p>
                        <p className="font-cormorant text-base sm:text-lg text-[#F2EEE6] font-medium">
                          {selectedImageData.location}
                        </p>
                      </div>
                    </div>

                    {selectedImageData.dateTaken && (
                      <div className="flex items-start gap-3 sm:gap-4">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-[#D4C2A4] text-sm sm:text-base">📅</span>
                        </div>
                        <div>
                          <p className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]/60 uppercase tracking-wide mb-1">
                            Date Captured
                          </p>
                          <p className="font-cormorant text-base sm:text-lg text-[#F2EEE6] font-medium">
                            {selectedImageData.dateTaken}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Category & Tags */}
                  <div className="border-t border-[#D4C2A4]/20 pt-4 sm:pt-6">
                    <Badge className="bg-gradient-to-r from-[#D4C2A4] to-[#D4C2A4]/80 text-[#16191D] font-open-sans px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm mb-4 sm:mb-6 rounded-lg sm:rounded-xl">
                      {selectedImageData.category}
                    </Badge>

                    {selectedImageData.tags && selectedImageData.tags.length > 0 && (
                      <div>
                        <h4 className="font-cormorant text-base sm:text-lg font-semibold text-[#F2EEE6] mb-3 sm:mb-4">
                          Tags
                        </h4>
                        <div className="flex flex-wrap gap-2 sm:gap-3">
                          {selectedImageData.tags.map((tag: string, index: number) => (
                            <span
                              key={index}
                              className="font-open-sans text-xs bg-[#D4C2A4]/20 text-[#F2EEE6] px-2 sm:px-3 py-1 sm:py-2 border border-[#D4C2A4]/30 rounded-lg sm:rounded-xl"
                            >
                              #{tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="border-t border-[#D4C2A4]/20 pt-4 sm:pt-6 flex flex-col sm:flex-row gap-3 sm:gap-4">
                    <Button
                      size="sm"
                      className="flex-1 bg-[#D4C2A4] hover:bg-[#D4C2A4]/80 text-[#16191D] font-open-sans py-2 sm:py-3 transition-all duration-300 rounded-lg sm:rounded-xl text-xs sm:text-sm"
                    >
                      <Heart className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                      Favorite
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="flex-1 border-[#D4C2A4]/30 text-[#F2EEE6] hover:bg-[#D4C2A4] hover:text-[#16191D] font-open-sans py-2 sm:py-3 transition-all duration-300 rounded-lg sm:rounded-xl text-xs sm:text-sm"
                    >
                      <Share2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                      Share
                    </Button>
                  </div>
                </div>
              </div>

              {/* Elegant Close Button */}
              <button
                className="absolute top-3 right-3 sm:top-4 sm:right-4 md:top-6 md:right-6 w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 bg-[#D4C2A4]/20 backdrop-blur-sm border border-[#D4C2A4]/30 text-[#F2EEE6] hover:bg-[#D4C2A4]/30 transition-all duration-300 flex items-center justify-center group rounded-lg sm:rounded-xl"
                onClick={() => {
                  setSelectedImage(null);
                  setSelectedImageData(null);
                }}
              >
                <X className="w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform duration-200" />
              </button>
            </div>
          </div>
        )}
      </main>
      <Footer isDarkBackground={true} />
    </div>
  );
};

export default Gallery;

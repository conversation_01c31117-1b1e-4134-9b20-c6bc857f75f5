import { useEffect, useRef, useCallback } from 'react';

// Hook for managing keyboard navigation
export function useKeyboardNavigation(
  containerRef: React.RefObject<HTMLElement>,
  options: {
    selector?: string;
    loop?: boolean;
    autoFocus?: boolean;
    onEscape?: () => void;
    onEnter?: (element: HTMLElement) => void;
  } = {}
) {
  const {
    selector = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
    loop = true,
    autoFocus = false,
    onEscape,
    onEnter
  } = options;

  const currentIndexRef = useRef(-1);

  const getFocusableElements = useCallback((): HTMLElement[] => {
    if (!containerRef.current) return [];
    
    const elements = Array.from(
      containerRef.current.querySelectorAll(selector)
    ) as HTMLElement[];
    
    return elements.filter(el => 
      !el.hasAttribute('disabled') && 
      !el.getAttribute('aria-hidden') &&
      el.offsetParent !== null // Element is visible
    );
  }, [selector]);

  const focusElement = useCallback((index: number) => {
    const elements = getFocusableElements();
    if (elements.length === 0) return;

    let targetIndex = index;
    
    if (loop) {
      if (targetIndex < 0) targetIndex = elements.length - 1;
      if (targetIndex >= elements.length) targetIndex = 0;
    } else {
      targetIndex = Math.max(0, Math.min(elements.length - 1, targetIndex));
    }

    currentIndexRef.current = targetIndex;
    elements[targetIndex]?.focus();
  }, [getFocusableElements, loop]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const elements = getFocusableElements();
    if (elements.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
      case 'ArrowRight':
        event.preventDefault();
        focusElement(currentIndexRef.current + 1);
        break;
        
      case 'ArrowUp':
      case 'ArrowLeft':
        event.preventDefault();
        focusElement(currentIndexRef.current - 1);
        break;
        
      case 'Home':
        event.preventDefault();
        focusElement(0);
        break;
        
      case 'End':
        event.preventDefault();
        focusElement(elements.length - 1);
        break;
        
      case 'Escape':
        if (onEscape) {
          event.preventDefault();
          onEscape();
        }
        break;
        
      case 'Enter':
      case ' ':
        if (onEnter && document.activeElement) {
          event.preventDefault();
          onEnter(document.activeElement as HTMLElement);
        }
        break;
    }
  }, [getFocusableElements, focusElement, onEscape, onEnter]);

  const handleFocusIn = useCallback((event: FocusEvent) => {
    const elements = getFocusableElements();
    const targetIndex = elements.indexOf(event.target as HTMLElement);
    if (targetIndex !== -1) {
      currentIndexRef.current = targetIndex;
    }
  }, [getFocusableElements]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('keydown', handleKeyDown);
    container.addEventListener('focusin', handleFocusIn);

    // Auto focus first element if requested
    if (autoFocus) {
      const elements = getFocusableElements();
      if (elements.length > 0) {
        elements[0].focus();
        currentIndexRef.current = 0;
      }
    }

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
      container.removeEventListener('focusin', handleFocusIn);
    };
  }, [handleKeyDown, handleFocusIn, autoFocus, getFocusableElements]);

  return {
    focusFirst: () => focusElement(0),
    focusLast: () => focusElement(getFocusableElements().length - 1),
    focusNext: () => focusElement(currentIndexRef.current + 1),
    focusPrevious: () => focusElement(currentIndexRef.current - 1),
    getCurrentIndex: () => currentIndexRef.current,
    getElementCount: () => getFocusableElements().length
  };
}

// Hook for focus trap (useful for modals, dropdowns)
export function useFocusTrap(
  containerRef: React.RefObject<HTMLElement>,
  isActive: boolean = true
) {
  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement?.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement?.focus();
        }
      }
    };

    // Focus first element when trap becomes active
    firstElement?.focus();

    document.addEventListener('keydown', handleTabKey);

    return () => {
      document.removeEventListener('keydown', handleTabKey);
    };
  }, [isActive]);
}

// Hook for managing focus restoration
export function useFocusRestore() {
  const previousActiveElementRef = useRef<HTMLElement | null>(null);

  const saveFocus = useCallback(() => {
    previousActiveElementRef.current = document.activeElement as HTMLElement;
  }, []);

  const restoreFocus = useCallback(() => {
    if (previousActiveElementRef.current) {
      previousActiveElementRef.current.focus();
      previousActiveElementRef.current = null;
    }
  }, []);

  return { saveFocus, restoreFocus };
}

// Hook for roving tabindex pattern
export function useRovingTabIndex(
  containerRef: React.RefObject<HTMLElement>,
  itemSelector: string = '[role="option"], [role="menuitem"], [role="tab"]'
) {
  const activeIndexRef = useRef(0);

  const updateTabIndex = useCallback(() => {
    if (!containerRef.current) return;

    const items = Array.from(
      containerRef.current.querySelectorAll(itemSelector)
    ) as HTMLElement[];

    items.forEach((item, index) => {
      if (index === activeIndexRef.current) {
        item.setAttribute('tabindex', '0');
      } else {
        item.setAttribute('tabindex', '-1');
      }
    });
  }, [itemSelector]);

  const setActiveIndex = useCallback((index: number) => {
    activeIndexRef.current = index;
    updateTabIndex();
  }, [updateTabIndex]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!containerRef.current) return;

    const items = Array.from(
      containerRef.current.querySelectorAll(itemSelector)
    ) as HTMLElement[];

    if (items.length === 0) return;

    let newIndex = activeIndexRef.current;

    switch (event.key) {
      case 'ArrowDown':
      case 'ArrowRight':
        event.preventDefault();
        newIndex = (activeIndexRef.current + 1) % items.length;
        break;
        
      case 'ArrowUp':
      case 'ArrowLeft':
        event.preventDefault();
        newIndex = activeIndexRef.current === 0 
          ? items.length - 1 
          : activeIndexRef.current - 1;
        break;
        
      case 'Home':
        event.preventDefault();
        newIndex = 0;
        break;
        
      case 'End':
        event.preventDefault();
        newIndex = items.length - 1;
        break;
        
      default:
        return;
    }

    setActiveIndex(newIndex);
    items[newIndex]?.focus();
  }, [itemSelector, setActiveIndex]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    updateTabIndex();
    container.addEventListener('keydown', handleKeyDown);

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, updateTabIndex]);

  return { setActiveIndex, activeIndex: activeIndexRef.current };
}

// Hook for skip links functionality
export function useSkipLinks() {
  const skipToContent = useCallback((targetId: string) => {
    const target = document.getElementById(targetId);
    if (target) {
      target.focus();
      target.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  const registerSkipTarget = useCallback((element: HTMLElement, id: string) => {
    element.id = id;
    element.setAttribute('tabindex', '-1');
    
    // Remove tabindex after focus to restore normal tab flow
    const handleFocus = () => {
      setTimeout(() => {
        element.removeAttribute('tabindex');
      }, 100);
    };
    
    element.addEventListener('focus', handleFocus, { once: true });
    
    return () => {
      element.removeEventListener('focus', handleFocus);
    };
  }, []);

  return { skipToContent, registerSkipTarget };
}

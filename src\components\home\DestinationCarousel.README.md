# DestinationCarousel Component

A premium, full-screen destination carousel component built with React, TypeScript, and Framer Motion. This component creates an immersive travel experience with smooth animations and glass morphism effects.

## Features

### Visual Design
- **Full-screen hero section** with dynamic background images
- **Horizontal card carousel** at the bottom showing destination thumbnails
- **Large destination name** displayed prominently in the center-left
- **Location name** displayed above the destination name in smaller text
- **Pagination counter** (01, 02, etc.) in bottom right corner
- **Semi-transparent gradient overlay** for better text readability
- **Minimalist navigation arrows** on either side of the carousel
- **Glass morphism effects** with transparent, clean design

### Animation Features

#### Background Transitions
- **Slide animation** when changing destinations (direction matches navigation)
- **Subtle zoom effect** (scale from 1.05 to 1) when new background appears
- **Gradient overlay** that animates with the background

#### Text Animations
- **Character-by-character reveal** animation for destination name
- **Staggered fade-in** for location text
- **Smooth counter animation** for pagination numbers

#### Card Animations
- **Active card** scales up to 1.05x and has full opacity
- **Inactive cards** scale to 0.95x with reduced opacity
- **Cards lift slightly** on hover with spring physics
- **Animated underline/highlight** appears on active card
- **Smooth transitions** between states using spring animations

### Functionality
- **Auto-scrolling carousel** that changes every 5 seconds (configurable)
- **Auto-scroll pauses** for 10 seconds when user manually selects a card (configurable)
- **Click to navigate** - clicking a card changes the main background and text
- **Navigation arrows** for manual browsing
- **Responsive design** that works on mobile and desktop
- **Direction tracking** to inform animation direction

## Usage

### Basic Usage

```tsx
import DestinationCarousel from '@/components/home/<USER>';

const MyComponent = () => {
  return (
    <DestinationCarousel />
  );
};
```

### With Custom Data

```tsx
import DestinationCarousel from '@/components/home/<USER>';

const destinations = [
  {
    id: '1',
    name: 'SAINT ANTONIEN',
    location: 'Switzerland Alps',
    backgroundImage: 'https://example.com/bg-image.jpg',
    cardImage: 'https://example.com/card-image.jpg'
  },
  // ... more destinations
];

const MyComponent = () => {
  return (
    <DestinationCarousel 
      destinations={destinations}
      autoScrollInterval={5000}
      pauseDuration={10000}
    />
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `destinations` | `Destination[]` | `defaultDestinations` | Array of destination objects |
| `autoScrollInterval` | `number` | `5000` | Auto-scroll interval in milliseconds |
| `pauseDuration` | `number` | `10000` | Pause duration after manual navigation in milliseconds |
| `className` | `string` | `undefined` | Additional CSS classes |

## Data Structure

### Destination Interface

```typescript
interface Destination {
  id: string;                    // Unique identifier
  name: string;                  // Destination name in all caps (e.g., "NAGANO PREFECTURE")
  location: string;              // Region or country (e.g., "Japan Alps")
  backgroundImage: string;       // URL to full-screen background image
  cardImage: string;            // URL to thumbnail image for carousel card
}
```

## Technical Implementation

### Dependencies
- **React** (18+)
- **Framer Motion** (12+) - for animations
- **Lucide React** - for icons
- **Tailwind CSS** - for styling
- **shadcn/ui** - for base components (Card, Button)

### Key Technologies
- **TypeScript** for type safety
- **CSS Modules** for scoped styling
- **React Hooks** (useState, useEffect, useRef)
- **Framer Motion** animation controls
- **Spring physics** for natural animations
- **Glass morphism** CSS effects

### Performance Optimizations
- **Lazy loading** of non-visible cards
- **Optimized re-renders** with proper dependency arrays
- **Smooth 60fps animations** with GPU acceleration
- **Responsive images** with proper sizing

## Styling

The component uses CSS Modules for styling with the following key classes:

- `.carouselContainer` - Main container
- `.backgroundImage` - Background image styling
- `.glassOverlay` - Glass morphism overlay
- `.destinationText` - Text container
- `.destinationName` - Large destination name
- `.destinationLocation` - Location text
- `.carouselCard` - Individual card styling
- `.navigationButton` - Navigation arrow buttons
- `.paginationCounter` - Counter styling

## Accessibility

- **ARIA labels** for screen readers
- **Keyboard navigation** support
- **Focus management** for interactive elements
- **Semantic HTML** structure
- **High contrast** text for readability

## Browser Support

- **Modern browsers** (Chrome 88+, Firefox 85+, Safari 14+, Edge 88+)
- **Mobile browsers** (iOS Safari 14+, Chrome Mobile 88+)
- **Responsive design** for all screen sizes

## Customization

### Custom Animations
You can customize animations by modifying the motion variants in the component:

```typescript
const customNameVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1, // Slower character reveal
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  })
};
```

### Custom Styling
Override CSS classes using the `className` prop or by modifying the CSS module file.

## Examples

### Travel Website Hero
Perfect for travel websites, tourism boards, and destination marketing.

### Real Estate Showcases
Can be adapted for property showcases with location-based content.

### Portfolio Galleries
Suitable for creative portfolios with project showcases.

## License

This component is part of the Warriors of Africa Safari project and follows the project's licensing terms.


import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  Calendar as CalendarIcon, 
  Users, 
  Sun, 
  Cloud, 
  Eye, 
  Camera,
  Info,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { format, addDays, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay } from 'date-fns';
import { TourAvailability } from '@/types/firebase';

interface AvailabilityCalendarProps {
  tourId: string;
  selectedDate: Date | null;
  onDateSelect: (date: Date) => void;
  groupSize: number;
}

const AvailabilityCalendar: React.FC<AvailabilityCalendarProps> = ({
  tourId,
  selectedDate,
  onDateSelect,
  groupSize
}) => {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [availability, setAvailability] = useState<TourAvailability[]>([]);
  const [loading, setLoading] = useState(false);

  // Generate sample availability data
  useEffect(() => {
    const generateAvailability = () => {
      const start = startOfMonth(currentMonth);
      const end = endOfMonth(addDays(currentMonth, 90)); // Next 3 months
      const days = eachDayOfInterval({ start, end });
      
      const sampleAvailability: TourAvailability[] = days.map(date => {
        const dayOfYear = date.getTime();
        const randomSeed = dayOfYear % 100;
        
        // Simulate seasonal patterns
        const month = date.getMonth();
        const isDrySeason = month >= 5 && month <= 10; // June to November
        const isHighSeason = month === 6 || month === 7 || month === 8; // July, August, September
        
        // Base availability
        let spotsRemaining = Math.floor(Math.random() * 12) + 1;
        let available = spotsRemaining >= groupSize;
        
        // Reduce availability on weekends and high season
        if (date.getDay() === 0 || date.getDay() === 6) {
          spotsRemaining = Math.max(0, spotsRemaining - 3);
        }
        
        if (isHighSeason) {
          spotsRemaining = Math.max(0, spotsRemaining - 2);
        }
        
        available = spotsRemaining >= groupSize && spotsRemaining > 0;
        
        // Price modifiers
        let priceModifier = 1;
        if (isHighSeason) priceModifier = 1.2;
        else if (isDrySeason) priceModifier = 1.1;
        else priceModifier = 0.9;
        
        // Wildlife and weather conditions
        const wildlifeActivity = isDrySeason 
          ? (randomSeed > 70 ? 'excellent' : randomSeed > 40 ? 'good' : 'fair')
          : (randomSeed > 50 ? 'good' : randomSeed > 25 ? 'fair' : 'poor');
          
        const weatherConditions = isDrySeason
          ? (randomSeed > 60 ? 'excellent' : 'good')
          : (randomSeed > 40 ? 'fair' : 'poor');
        
        return {
          tourId,
          date: format(date, 'yyyy-MM-dd'),
          available,
          spotsRemaining,
          priceModifier,
          season: isHighSeason ? 'high' : isDrySeason ? 'medium' : 'low',
          wildlifeActivity,
          weatherConditions
        } as TourAvailability;
      });
      
      setAvailability(sampleAvailability);
    };

    generateAvailability();
  }, [currentMonth, tourId, groupSize]);

  const getAvailabilityForDate = (date: Date): TourAvailability | undefined => {
    return availability.find(av => av.date === format(date, 'yyyy-MM-dd'));
  };

  const getDateClassName = (date: Date): string => {
    const avail = getAvailabilityForDate(date);
    if (!avail) return '';
    
    if (!avail.available) return 'bg-red-100 text-red-800 hover:bg-red-200';
    if (avail.spotsRemaining <= 3) return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
    return 'bg-green-100 text-green-800 hover:bg-green-200';
  };

  const getSeasonBadgeColor = (season: string) => {
    switch (season) {
      case 'high': return 'bg-red-500 text-white';
      case 'medium': return 'bg-orange-500 text-white';
      case 'low': return 'bg-green-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getConditionIcon = (condition: string) => {
    switch (condition) {
      case 'excellent': return <Sun className="h-4 w-4 text-yellow-500" />;
      case 'good': return <Sun className="h-4 w-4 text-orange-500" />;
      case 'fair': return <Cloud className="h-4 w-4 text-gray-500" />;
      case 'poor': return <Cloud className="h-4 w-4 text-blue-500" />;
      default: return null;
    }
  };

  const selectedAvailability = selectedDate ? getAvailabilityForDate(selectedDate) : null;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            Select Your Departure Date
          </CardTitle>
          <p className="text-sm text-gray-600">
            Choose from available dates. Prices and wildlife viewing opportunities vary by season.
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Calendar */}
            <div>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && onDateSelect(date)}
                month={currentMonth}
                onMonthChange={setCurrentMonth}
                disabled={(date) => {
                  const avail = getAvailabilityForDate(date);
                  return !avail?.available || date < new Date();
                }}
                modifiers={{
                  available: (date) => {
                    const avail = getAvailabilityForDate(date);
                    return avail?.available || false;
                  },
                  limited: (date) => {
                    const avail = getAvailabilityForDate(date);
                    return avail?.available && avail.spotsRemaining <= 3;
                  }
                }}
                modifiersClassNames={{
                  available: 'bg-green-100 text-green-800 hover:bg-green-200',
                  limited: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                }}
                className="rounded-md border"
              />
              
              {/* Legend */}
              <div className="mt-4 space-y-2">
                <h4 className="font-medium text-sm">Availability Legend</h4>
                <div className="grid grid-cols-1 gap-2 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-green-100 border border-green-200 rounded"></div>
                    <span>Available ({groupSize}+ spots)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-yellow-100 border border-yellow-200 rounded"></div>
                    <span>Limited availability (1-3 spots)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-red-100 border border-red-200 rounded"></div>
                    <span>Fully booked</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Date Details */}
            <div className="space-y-4">
              {selectedAvailability ? (
                <>
                  <div className="p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200">
                    <h4 className="font-semibold text-lg mb-2">
                      {format(selectedDate!, 'EEEE, MMMM do, yyyy')}
                    </h4>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {selectedAvailability.spotsRemaining}
                        </div>
                        <div className="text-sm text-gray-600">Spots Available</div>
                      </div>
                      <div className="text-center">
                        <Badge className={getSeasonBadgeColor(selectedAvailability.season)}>
                          {selectedAvailability.season.charAt(0).toUpperCase() + selectedAvailability.season.slice(1)} Season
                        </Badge>
                        <div className="text-sm text-gray-600 mt-1">
                          {selectedAvailability.priceModifier > 1 
                            ? `+${Math.round((selectedAvailability.priceModifier - 1) * 100)}% price`
                            : selectedAvailability.priceModifier < 1
                            ? `-${Math.round((1 - selectedAvailability.priceModifier) * 100)}% price`
                            : 'Standard price'
                          }
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-white rounded-lg">
                        <div className="flex items-center justify-center gap-2 mb-1">
                          <Eye className="h-4 w-4" />
                          {getConditionIcon(selectedAvailability.wildlifeActivity)}
                        </div>
                        <div className="text-sm font-medium">Wildlife Viewing</div>
                        <div className="text-xs text-gray-600 capitalize">
                          {selectedAvailability.wildlifeActivity}
                        </div>
                      </div>
                      <div className="text-center p-3 bg-white rounded-lg">
                        <div className="flex items-center justify-center gap-2 mb-1">
                          <Camera className="h-4 w-4" />
                          {getConditionIcon(selectedAvailability.weatherConditions)}
                        </div>
                        <div className="text-sm font-medium">Photography</div>
                        <div className="text-xs text-gray-600 capitalize">
                          {selectedAvailability.weatherConditions}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Seasonal Information */}
                  <Card>
                    <CardContent className="p-4">
                      <h5 className="font-medium mb-3 flex items-center gap-2">
                        <Info className="h-4 w-4" />
                        What to Expect This Time of Year
                      </h5>
                      
                      {selectedDate && selectedDate.getMonth() >= 5 && selectedDate.getMonth() <= 10 ? (
                        <div className="space-y-2 text-sm">
                          <div className="flex items-start gap-2">
                            <Sun className="h-4 w-4 text-yellow-500 mt-0.5" />
                            <div>
                              <div className="font-medium">Dry Season (June - November)</div>
                              <div className="text-gray-600">Excellent game viewing, clear skies, minimal rainfall</div>
                            </div>
                          </div>
                          <div className="flex items-start gap-2">
                            <Eye className="h-4 w-4 text-green-500 mt-0.5" />
                            <div>
                              <div className="font-medium">Wildlife Concentration</div>
                              <div className="text-gray-600">Animals gather around water sources, easier spotting</div>
                            </div>
                          </div>
                          <div className="flex items-start gap-2">
                            <Camera className="h-4 w-4 text-blue-500 mt-0.5" />
                            <div>
                              <div className="font-medium">Photography</div>
                              <div className="text-gray-600">Clear visibility, golden light, dust effects</div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-2 text-sm">
                          <div className="flex items-start gap-2">
                            <Cloud className="h-4 w-4 text-blue-500 mt-0.5" />
                            <div>
                              <div className="font-medium">Green Season (December - May)</div>
                              <div className="text-gray-600">Lush landscapes, calving season, fewer crowds</div>
                            </div>
                          </div>
                          <div className="flex items-start gap-2">
                            <Eye className="h-4 w-4 text-green-500 mt-0.5" />
                            <div>
                              <div className="font-medium">Unique Experiences</div>
                              <div className="text-gray-600">Newborn animals, migrant birds, dramatic skies</div>
                            </div>
                          </div>
                          <div className="flex items-start gap-2">
                            <TrendingDown className="h-4 w-4 text-green-500 mt-0.5" />
                            <div>
                              <div className="font-medium">Better Value</div>
                              <div className="text-gray-600">Lower prices, special offers, intimate experiences</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </>
              ) : (
                <div className="p-8 text-center text-gray-500">
                  <CalendarIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Select a date to see availability and seasonal information</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Date Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Popular Departure Times</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {['Next Weekend', 'In 2 Weeks', 'Next Month', 'Peak Season'].map((option, index) => {
              let targetDate: Date;
              const now = new Date();
              
              switch (option) {
                case 'Next Weekend':
                  targetDate = addDays(now, 7 - now.getDay() + 6); // Next Saturday
                  break;
                case 'In 2 Weeks':
                  targetDate = addDays(now, 14);
                  break;
                case 'Next Month':
                  targetDate = addDays(now, 30);
                  break;
                case 'Peak Season':
                  targetDate = new Date(now.getFullYear(), 6, 15); // July 15th
                  if (targetDate < now) targetDate = new Date(now.getFullYear() + 1, 6, 15);
                  break;
                default:
                  targetDate = now;
              }
              
              const avail = getAvailabilityForDate(targetDate);
              
              return (
                <Button
                  key={option}
                  variant="outline"
                  className="p-3 h-auto flex flex-col items-center space-y-1"
                  onClick={() => onDateSelect(targetDate)}
                  disabled={!avail?.available}
                >
                  <span className="font-medium text-sm">{option}</span>
                  <span className="text-xs text-gray-500">
                    {format(targetDate, 'MMM dd')}
                  </span>
                  {avail && (
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${avail.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                    >
                      {avail.available ? `${avail.spotsRemaining} spots` : 'Full'}
                    </Badge>
                  )}
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AvailabilityCalendar;

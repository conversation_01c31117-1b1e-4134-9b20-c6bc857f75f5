
import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, X, MapPin, Calendar, Users, DollarSign } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Link } from 'react-router-dom';

interface SearchResult {
  id: string;
  title: string;
  type: 'tour' | 'destination';
  price?: number;
  duration?: number;
  maxGroup?: number;
  difficulty?: 'easy' | 'moderate' | 'challenging';
  image: string;
  description: string;
  location: string;
  rating: number;
}

const AdvancedSearch = () => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    priceRange: [0, 5000],
    duration: '',
    difficulty: '',
    groupSize: '',
    destination: ''
  });

  const sampleResults: SearchResult[] = [
    {
      id: '1',
      title: 'Serengeti Classic Safari',
      type: 'tour',
      price: 2499,
      duration: 5,
      maxGroup: 8,
      difficulty: 'easy',
      image: 'photo-1472396961693-142e6e269027',
      description: 'Experience the Great Migration in the world-famous Serengeti',
      location: 'Serengeti National Park',
      rating: 4.9
    },
    {
      id: '2',
      title: 'Ngorongoro Crater Adventure',
      type: 'tour',
      price: 1899,
      duration: 3,
      maxGroup: 6,
      difficulty: 'moderate',
      image: 'photo-1466721591366-2d5fba72006d',
      description: 'Explore the world\'s largest intact volcanic caldera',
      location: 'Ngorongoro Conservation Area',
      rating: 4.8
    },
    {
      id: '3',
      title: 'Kilimanjaro Trek',
      type: 'tour',
      price: 3299,
      duration: 7,
      maxGroup: 4,
      difficulty: 'challenging',
      image: 'photo-1485833077593-4278bba3f11f',
      description: 'Climb Africa\'s highest peak via the Machame route',
      location: 'Mount Kilimanjaro',
      rating: 4.7
    },
    {
      id: '4',
      title: 'Tarangire Elephant Safari',
      type: 'tour',
      price: 1599,
      duration: 2,
      maxGroup: 10,
      difficulty: 'easy',
      image: 'photo-1493962853295-0fd70327578a',
      description: 'Get up close with large elephant herds and ancient baobab trees',
      location: 'Tarangire National Park',
      rating: 4.6
    },
    {
      id: '5',
      title: 'Complete Tanzania Circuit',
      type: 'tour',
      price: 4599,
      duration: 10,
      maxGroup: 8,
      difficulty: 'moderate',
      image: 'photo-1472396961693-142e6e269027',
      description: 'Visit all major parks in Northern Tanzania',
      location: 'Multiple Destinations',
      rating: 4.9
    }
  ];

  const filterResults = (searchResults: SearchResult[]) => {
    return searchResults.filter(item => {
      const matchesPrice = !item.price || (item.price >= filters.priceRange[0] && item.price <= filters.priceRange[1]);
      const matchesDuration = !filters.duration || item.duration?.toString() === filters.duration;
      const matchesDifficulty = !filters.difficulty || item.difficulty === filters.difficulty;
      const matchesGroupSize = !filters.groupSize || (item.maxGroup && item.maxGroup >= parseInt(filters.groupSize));
      const matchesDestination = !filters.destination || item.location.toLowerCase().includes(filters.destination.toLowerCase());

      return matchesPrice && matchesDuration && matchesDifficulty && matchesGroupSize && matchesDestination;
    });
  };

  useEffect(() => {
    if (searchTerm.length > 2) {
      const filtered = sampleResults.filter(item => 
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
      const finalResults = filterResults(filtered);
      setResults(finalResults);
      setShowResults(true);
    } else if (searchTerm.length === 0 && showFilters) {
      const finalResults = filterResults(sampleResults);
      setResults(finalResults);
      setShowResults(true);
    } else {
      setShowResults(false);
    }
  }, [searchTerm, filters]);

  const clearFilters = () => {
    setFilters({
      priceRange: [0, 5000],
      duration: '',
      difficulty: '',
      groupSize: '',
      destination: ''
    });
  };

  return (
    <div className="relative w-full max-w-md">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
        <Input
          type="text"
          placeholder={t('search.placeholder')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 pr-20"
        />
        <div className="absolute right-1 top-1/2 -translate-y-1/2 flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className={`h-8 w-8 p-0 ${showFilters ? 'bg-orange-100 text-orange-600' : ''}`}
          >
            <Filter className="h-4 w-4" />
          </Button>
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSearchTerm('')}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <Card className="absolute top-full mt-2 w-80 z-50 bg-white shadow-lg">
          <CardContent className="p-4 space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold">Filters</h3>
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                Clear All
              </Button>
            </div>

            {/* Price Range */}
            <div>
              <label className="text-sm font-medium">Price Range</label>
              <div className="mt-2">
                <Slider
                  value={filters.priceRange}
                  onValueChange={(value) => setFilters({...filters, priceRange: value})}
                  max={5000}
                  min={0}
                  step={100}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>${filters.priceRange[0]}</span>
                  <span>${filters.priceRange[1]}</span>
                </div>
              </div>
            </div>

            {/* Duration */}
            <div>
              <label className="text-sm font-medium">Duration (days)</label>
              <Select value={filters.duration} onValueChange={(value) => setFilters({...filters, duration: value})}>
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Any duration" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any duration</SelectItem>
                  <SelectItem value="1">1 day</SelectItem>
                  <SelectItem value="2">2 days</SelectItem>
                  <SelectItem value="3">3 days</SelectItem>
                  <SelectItem value="5">5 days</SelectItem>
                  <SelectItem value="7">7 days</SelectItem>
                  <SelectItem value="10">10+ days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Difficulty */}
            <div>
              <label className="text-sm font-medium">Difficulty</label>
              <Select value={filters.difficulty} onValueChange={(value) => setFilters({...filters, difficulty: value})}>
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Any difficulty" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any difficulty</SelectItem>
                  <SelectItem value="easy">Easy</SelectItem>
                  <SelectItem value="moderate">Moderate</SelectItem>
                  <SelectItem value="challenging">Challenging</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Group Size */}
            <div>
              <label className="text-sm font-medium">Minimum Group Size</label>
              <Select value={filters.groupSize} onValueChange={(value) => setFilters({...filters, groupSize: value})}>
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Any group size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any group size</SelectItem>
                  <SelectItem value="2">2+ people</SelectItem>
                  <SelectItem value="4">4+ people</SelectItem>
                  <SelectItem value="6">6+ people</SelectItem>
                  <SelectItem value="8">8+ people</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Destination */}
            <div>
              <label className="text-sm font-medium">Destination</label>
              <Input
                placeholder="Enter destination"
                value={filters.destination}
                onChange={(e) => setFilters({...filters, destination: e.target.value})}
                className="mt-1"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Results */}
      {showResults && (
        <Card className="absolute top-full mt-2 w-full z-50 bg-white shadow-lg max-h-96 overflow-y-auto">
          <CardContent className="p-2">
            {results.length > 0 ? (
              <div className="space-y-2">
                <div className="px-2 py-1 text-sm text-gray-600 border-b">
                  {results.length} result{results.length !== 1 ? 's' : ''} found
                </div>
                {results.map((result) => (
                  <Link key={result.id} to={`/tours/${result.id}`} className="block">
                    <div className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <img
                        src={`https://images.unsplash.com/${result.image}?auto=format&fit=crop&w=80&h=80`}
                        alt={result.title}
                        className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <h4 className="font-medium text-sm text-gray-900 truncate">{result.title}</h4>
                          <div className="text-right ml-2 flex-shrink-0">
                            {result.price && (
                              <div className="text-sm font-semibold text-orange-600">${result.price}</div>
                            )}
                            <div className="text-xs text-gray-500">⭐ {result.rating}</div>
                          </div>
                        </div>
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">{result.description}</p>
                        <div className="flex items-center gap-2 mt-2 flex-wrap">
                          <Badge variant="outline" className="text-xs">
                            <MapPin className="w-3 h-3 mr-1" />
                            {result.location}
                          </Badge>
                          {result.duration && (
                            <Badge variant="outline" className="text-xs">
                              <Calendar className="w-3 h-3 mr-1" />
                              {result.duration} days
                            </Badge>
                          )}
                          {result.difficulty && (
                            <Badge variant="outline" className="text-xs capitalize">
                              {result.difficulty}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center">
                <p className="text-sm text-gray-500">No results found</p>
                <p className="text-xs text-gray-400 mt-1">Try adjusting your search or filters</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AdvancedSearch;

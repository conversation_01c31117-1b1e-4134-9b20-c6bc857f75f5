import { LiquidButton } from "@/components/ui/liquid-glass-button";
import { GlassHeader } from "@/components/ui/glass-header";
import { Camera, Heart, User, Menu } from "lucide-react";

export default function UltraGlassDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      {/* Background elements to show glass effect */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute top-40 right-20 w-96 h-96 bg-gradient-to-r from-pink-400 to-red-500 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute bottom-20 left-1/2 w-80 h-80 bg-gradient-to-r from-green-400 to-blue-500 rounded-full opacity-20 blur-3xl"></div>
      </div>

      {/* Ultra Glass Header Demo */}
      <GlassHeader variant="ultra-glass" intensity="extreme" className="shadow-2xl">
        <div className="container mx-auto px-4 max-w-full">
          <div className="flex items-center justify-between h-16 min-w-0">
            {/* Logo with Liquid Glass Effect */}
            <div className="flex items-center space-x-3 group flex-shrink-0 min-w-0">
              <LiquidButton 
                size="icon" 
                variant="default" 
                className="text-gray-800 dark:text-white p-3 group-hover:scale-105 transition-all duration-300 shadow-xl flex-shrink-0 border border-black/20 dark:border-white/30 group-hover:shadow-2xl bg-white/20 hover:bg-white/30 backdrop-blur-sm"
              >
                <Camera className="h-6 w-6 drop-shadow-sm" />
              </LiquidButton>
              <div className="min-w-0">
                <span className="font-bold text-xl text-gray-900 dark:text-white drop-shadow-xl whitespace-nowrap tracking-wide backdrop-blur-sm bg-white/10 px-3 py-1 rounded-lg border border-black/10 dark:border-white/20">
                  Ultra Glass Demo
                </span>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              {['Home', 'About', 'Services', 'Contact'].map((item) => (
                <div
                  key={item}
                  className="relative text-gray-800 dark:text-white/90 hover:text-gray-900 dark:hover:text-white font-medium transition-all duration-300 py-2 group text-sm drop-shadow-sm backdrop-blur-sm bg-white/10 px-3 rounded-md border border-black/10 dark:border-white/20 hover:bg-white/20 cursor-pointer"
                >
                  {item}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-800 dark:bg-white/80 transition-all duration-300 group-hover:w-full"></span>
                </div>
              ))}
            </nav>

            {/* Actions */}
            <div className="flex items-center space-x-4 flex-shrink-0">
              {/* Wishlist */}
              <LiquidButton 
                variant="default" 
                size="icon" 
                className="relative text-gray-800 dark:text-white/95 hover:text-gray-900 dark:hover:text-white p-2 bg-white/20 hover:bg-white/30 border border-black/20 dark:border-white/20 hover:border-black/30 dark:hover:border-white/40 transition-all duration-300 backdrop-blur-sm"
              >
                <Heart className="h-5 w-5 drop-shadow-sm" />
                <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-full text-xs w-5 h-5 flex items-center justify-center shadow-xl backdrop-blur-sm border border-white/30">
                  3
                </span>
              </LiquidButton>

              {/* Account */}
              <LiquidButton 
                size="default" 
                variant="default" 
                className="text-gray-800 dark:text-white shadow-xl hover:shadow-2xl px-6 text-sm border border-black/20 dark:border-white/30 bg-white/20 hover:bg-white/30 backdrop-blur-sm"
              >
                <User className="h-4 w-4 mr-2 drop-shadow-sm" />
                <span className="drop-shadow-sm">Account</span>
              </LiquidButton>

              {/* Mobile menu */}
              <LiquidButton
                size="icon"
                variant="default"
                className="md:hidden p-2 text-gray-800 dark:text-white/95 hover:text-gray-900 dark:hover:text-white flex-shrink-0 border border-black/20 dark:border-white/20 hover:border-black/30 dark:hover:border-white/40 bg-white/20 hover:bg-white/30 backdrop-blur-sm"
              >
                <Menu className="h-5 w-5 drop-shadow-sm" />
              </LiquidButton>
            </div>
          </div>
        </div>
      </GlassHeader>

      {/* Content to show glass effect */}
      <div className="pt-24 px-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Ultra Glass Morphism Header
          </h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-xl">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Glass Card {i}</h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  This card demonstrates the glass morphism effect with backdrop blur and transparency.
                </p>
                <LiquidButton size="sm" variant="default" className="bg-white/20 hover:bg-white/30 text-gray-800 dark:text-white border border-black/20 dark:border-white/30">
                  Learn More
                </LiquidButton>
              </div>
            ))}
          </div>

          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Features of Ultra Glass Header
            </h2>
            <ul className="text-gray-700 dark:text-gray-300 space-y-2 max-w-2xl mx-auto">
              <li>✨ Ultra-realistic glass morphism with multiple blur layers</li>
              <li>🎨 Advanced SVG filters for glass distortion effects</li>
              <li>🌈 Perfect transparency and backdrop blur</li>
              <li>📱 Fully responsive design</li>
              <li>🎯 Liquid glass buttons with realistic effects</li>
              <li>🔄 Smooth animations and transitions</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Warriors of Africa Safari</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #FDF6E3 0%, #F4E4BC 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8B4513;
            line-height: 1.6;
        }
        
        .container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #B85C38;
        }
        
        p {
            margin-bottom: 1.5rem;
            color: #6B4423;
            font-size: 1.1rem;
        }
        
        .retry-btn {
            background: #B85C38;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin: 0.5rem;
        }
        
        .retry-btn:hover {
            background: #A0522D;
        }
        
        .features {
            margin-top: 2rem;
            text-align: left;
        }
        
        .feature {
            margin: 0.5rem 0;
            display: flex;
            align-items: center;
        }
        
        .feature-icon {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🦁</div>
        <h1>You're Offline</h1>
        <p>It looks like you've lost your internet connection. Don't worry - your safari adventure is just a connection away!</p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            Try Again
        </button>
        
        <button class="retry-btn" onclick="window.history.back()">
            Go Back
        </button>
        
        <div class="features">
            <h3 style="margin-bottom: 1rem; color: #B85C38;">While you're offline:</h3>
            <div class="feature">
                <span class="feature-icon">📱</span>
                <span>Some cached pages may still be available</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔄</span>
                <span>Your data will sync when you're back online</span>
            </div>
            <div class="feature">
                <span class="feature-icon">💾</span>
                <span>Your preferences are saved locally</span>
            </div>
        </div>
    </div>
    
    <script>
        // Check for connection and auto-reload
        window.addEventListener('online', () => {
            window.location.reload();
        });
        
        // Show connection status
        function updateConnectionStatus() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }
        
        // Check connection every 5 seconds
        setInterval(updateConnectionStatus, 5000);
    </script>
</body>
</html>

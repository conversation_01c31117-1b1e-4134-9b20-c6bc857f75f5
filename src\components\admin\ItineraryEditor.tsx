import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Trash2, 
  Calendar, 
  Clock,
  MapPin,
  ArrowUp,
  ArrowDown,
  Edit,
  Save,
  X
} from 'lucide-react';

interface ItineraryActivity {
  time: string;
  activity: string;
  description: string;
  duration: string;
  location: string;
}

interface ItineraryDay {
  day: number;
  title: string;
  description: string;
  accommodation: string;
  meals: string[];
  activities: ItineraryActivity[];
  drivingTime: string;
  highlights: string[];
}

interface ItineraryEditorProps {
  itinerary: ItineraryDay[];
  onItineraryChange: (itinerary: ItineraryDay[]) => void;
}

const ItineraryEditor: React.FC<ItineraryEditorProps> = ({ itinerary, onItineraryChange }) => {
  const [editingDay, setEditingDay] = useState<number | null>(null);

  const addDay = () => {
    const newDay: ItineraryDay = {
      day: itinerary.length + 1,
      title: `Day ${itinerary.length + 1}`,
      description: '',
      accommodation: '',
      meals: [],
      activities: [],
      drivingTime: '',
      highlights: []
    };
    onItineraryChange([...itinerary, newDay]);
  };

  const removeDay = (dayIndex: number) => {
    const updatedItinerary = itinerary.filter((_, index) => index !== dayIndex);
    // Renumber days
    const renumberedItinerary = updatedItinerary.map((day, index) => ({
      ...day,
      day: index + 1
    }));
    onItineraryChange(renumberedItinerary);
  };

  const moveDay = (dayIndex: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && dayIndex === 0) ||
      (direction === 'down' && dayIndex === itinerary.length - 1)
    ) {
      return;
    }

    const newItinerary = [...itinerary];
    const targetIndex = direction === 'up' ? dayIndex - 1 : dayIndex + 1;
    
    // Swap days
    [newItinerary[dayIndex], newItinerary[targetIndex]] = 
    [newItinerary[targetIndex], newItinerary[dayIndex]];
    
    // Renumber days
    const renumberedItinerary = newItinerary.map((day, index) => ({
      ...day,
      day: index + 1
    }));
    
    onItineraryChange(renumberedItinerary);
  };

  const updateDay = (dayIndex: number, field: keyof ItineraryDay, value: any) => {
    const updatedItinerary = [...itinerary];
    updatedItinerary[dayIndex] = { ...updatedItinerary[dayIndex], [field]: value };
    onItineraryChange(updatedItinerary);
  };

  const handleArrayInput = (dayIndex: number, field: 'meals' | 'highlights', value: string) => {
    const arrayValue = value.split(',').map(item => item.trim()).filter(item => item);
    updateDay(dayIndex, field, arrayValue);
  };

  const addActivity = (dayIndex: number) => {
    const newActivity: ItineraryActivity = {
      time: '',
      activity: '',
      description: '',
      duration: '',
      location: ''
    };
    const updatedDay = { ...itinerary[dayIndex] };
    updatedDay.activities = [...updatedDay.activities, newActivity];
    updateDay(dayIndex, 'activities', updatedDay.activities);
  };

  const removeActivity = (dayIndex: number, activityIndex: number) => {
    const updatedDay = { ...itinerary[dayIndex] };
    updatedDay.activities = updatedDay.activities.filter((_, index) => index !== activityIndex);
    updateDay(dayIndex, 'activities', updatedDay.activities);
  };

  const updateActivity = (dayIndex: number, activityIndex: number, field: keyof ItineraryActivity, value: string) => {
    const updatedDay = { ...itinerary[dayIndex] };
    updatedDay.activities[activityIndex] = { ...updatedDay.activities[activityIndex], [field]: value };
    updateDay(dayIndex, 'activities', updatedDay.activities);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Tour Itinerary</h3>
          <p className="text-sm text-gray-600">Create a detailed day-by-day itinerary for your tour</p>
        </div>
        <Button onClick={addDay} size="sm">
          <Plus className="w-4 h-4 mr-2" />
          Add Day
        </Button>
      </div>

      {itinerary.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center text-gray-500">
            <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No itinerary days added yet. Click "Add Day" to get started.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {itinerary.map((day, dayIndex) => (
            <Card key={day.day} className="relative">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <span className="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 text-sm">
                      {day.day}
                    </span>
                    {editingDay === dayIndex ? (
                      <Input
                        value={day.title}
                        onChange={(e) => updateDay(dayIndex, 'title', e.target.value)}
                        className="ml-2 max-w-xs"
                        placeholder="Day title"
                      />
                    ) : (
                      <span>{day.title}</span>
                    )}
                  </CardTitle>
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => moveDay(dayIndex, 'up')}
                      disabled={dayIndex === 0}
                    >
                      <ArrowUp className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => moveDay(dayIndex, 'down')}
                      disabled={dayIndex === itinerary.length - 1}
                    >
                      <ArrowDown className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingDay(editingDay === dayIndex ? null : dayIndex)}
                    >
                      {editingDay === dayIndex ? <Save className="w-4 h-4" /> : <Edit className="w-4 h-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeDay(dayIndex)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              {editingDay === dayIndex && (
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor={`description-${dayIndex}`}>Description</Label>
                    <Textarea
                      id={`description-${dayIndex}`}
                      value={day.description}
                      onChange={(e) => updateDay(dayIndex, 'description', e.target.value)}
                      placeholder="Describe what happens on this day..."
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor={`accommodation-${dayIndex}`}>Accommodation</Label>
                      <Input
                        id={`accommodation-${dayIndex}`}
                        value={day.accommodation}
                        onChange={(e) => updateDay(dayIndex, 'accommodation', e.target.value)}
                        placeholder="Lodge name or accommodation type"
                      />
                    </div>
                    <div>
                      <Label htmlFor={`drivingTime-${dayIndex}`}>Driving Time</Label>
                      <Input
                        id={`drivingTime-${dayIndex}`}
                        value={day.drivingTime}
                        onChange={(e) => updateDay(dayIndex, 'drivingTime', e.target.value)}
                        placeholder="e.g., 2 hours"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor={`meals-${dayIndex}`}>Meals (comma-separated)</Label>
                    <Input
                      id={`meals-${dayIndex}`}
                      value={day.meals.join(', ')}
                      onChange={(e) => handleArrayInput(dayIndex, 'meals', e.target.value)}
                      placeholder="Breakfast, Lunch, Dinner"
                    />
                  </div>

                  <div>
                    <Label htmlFor={`highlights-${dayIndex}`}>Highlights (comma-separated)</Label>
                    <Input
                      id={`highlights-${dayIndex}`}
                      value={day.highlights.join(', ')}
                      onChange={(e) => handleArrayInput(dayIndex, 'highlights', e.target.value)}
                      placeholder="Big Five sighting, Sunset at crater rim, Cultural visit"
                    />
                  </div>

                  {/* Activities Section */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label className="text-base font-medium">Activities</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addActivity(dayIndex)}
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Add Activity
                      </Button>
                    </div>

                    {day.activities.length === 0 ? (
                      <div className="text-center py-4 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
                        <Clock className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                        <p>No activities added yet</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {day.activities.map((activity, activityIndex) => (
                          <Card key={activityIndex} className="bg-gray-50">
                            <CardContent className="p-4">
                              <div className="flex justify-between items-start mb-3">
                                <Badge variant="outline" className="text-xs">
                                  Activity {activityIndex + 1}
                                </Badge>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeActivity(dayIndex, activityIndex)}
                                  className="text-red-600 hover:text-red-700 p-1"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label className="text-xs">Time</Label>
                                  <Input
                                    value={activity.time}
                                    onChange={(e) => updateActivity(dayIndex, activityIndex, 'time', e.target.value)}
                                    placeholder="e.g., 06:00 AM"
                                    className="text-sm"
                                  />
                                </div>
                                <div>
                                  <Label className="text-xs">Duration</Label>
                                  <Input
                                    value={activity.duration}
                                    onChange={(e) => updateActivity(dayIndex, activityIndex, 'duration', e.target.value)}
                                    placeholder="e.g., 3 hours"
                                    className="text-sm"
                                  />
                                </div>
                              </div>

                              <div className="mt-3">
                                <Label className="text-xs">Activity Name</Label>
                                <Input
                                  value={activity.activity}
                                  onChange={(e) => updateActivity(dayIndex, activityIndex, 'activity', e.target.value)}
                                  placeholder="e.g., Morning Game Drive"
                                  className="text-sm"
                                />
                              </div>

                              <div className="mt-3">
                                <Label className="text-xs">Location</Label>
                                <Input
                                  value={activity.location}
                                  onChange={(e) => updateActivity(dayIndex, activityIndex, 'location', e.target.value)}
                                  placeholder="e.g., Serengeti Central"
                                  className="text-sm"
                                />
                              </div>

                              <div className="mt-3">
                                <Label className="text-xs">Description</Label>
                                <Textarea
                                  value={activity.description}
                                  onChange={(e) => updateActivity(dayIndex, activityIndex, 'description', e.target.value)}
                                  placeholder="Describe the activity in detail..."
                                  rows={2}
                                  className="text-sm"
                                />
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ItineraryEditor;


import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Camera } from 'lucide-react';
import LeafletMap from '@/components/maps/LeafletMap';
import { WorldMap } from '@/components/ui/world-map';

const DestinationShowcase = () => {
  const [selectedDestination, setSelectedDestination] = useState<any>(null);

  const destinations = [
    {
      id: 'serengeti',
      name: 'Serengeti National Park',
      lat: -2.153389,
      lng: 34.6857,
      image: 'https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=800&h=600',
      description: 'Home to the Great Migration and endless plains teeming with wildlife',
      highlights: ['Great Migration', 'Big Five', 'Endless Plains'],
      bestTime: 'June - October',
      rating: 4.9,
      area: '14,750 km²',
      established: '1951',
      tours: 12
    },
    {
      id: 'ngorongoro',
      name: 'Ngorongoro Crater',
      lat: -3.2175,
      lng: 35.5,
      image: 'https://images.unsplash.com/photo-1466721591366-2d5fba72006d?auto=format&fit=crop&w=800&h=600',
      description: 'A natural wonder featuring the world\'s largest intact volcanic caldera',
      highlights: ['Black Rhinos', 'Crater Floor', 'Flamingo Lake'],
      bestTime: 'Year Round',
      rating: 4.8,
      area: '8,292 km²',
      established: '1959',
      tours: 8
    },
    {
      id: 'tarangire',
      name: 'Tarangire National Park',
      lat: -3.8333,
      lng: 35.85,
      image: 'https://images.unsplash.com/photo-1493962853295-0fd70327578a?auto=format&fit=crop&w=800&h=600',
      description: 'Famous for large elephant herds and iconic baobab trees',
      highlights: ['Elephant Herds', 'Baobab Trees', 'Bird Watching'],
      bestTime: 'June - November',
      rating: 4.7,
      area: '2,850 km²',
      established: '1970',
      tours: 6
    },
    {
      id: 'manyara',
      name: 'Lake Manyara',
      lat: -3.3667,
      lng: 35.8167,
      image: 'https://images.unsplash.com/photo-1485833077593-4278bba3f11f?auto=format&fit=crop&w=800&h=600',
      description: 'Known for tree-climbing lions and diverse bird species',
      highlights: ['Tree-climbing Lions', 'Hot Springs', 'Pink Flamingos'],
      bestTime: 'November - April',
      rating: 4.6,
      area: '330 km²',
      established: '1960',
      tours: 5
    }
  ];

  const handleDestinationClick = (destination: any) => {
    setSelectedDestination(destination);
  };

  return (
    <section className="py-20 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2310b981' fill-opacity='0.1'%3E%3Cpath d='m0 40l40-40v40z'/%3E%3Cpath d='m40 0v40l-40-40z'/%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-safari-warm-amber/20 text-safari-coffee-brown px-4 py-2">
            <MapPin className="w-4 h-4 mr-2" />
            Destinations
          </Badge>
          <h2
            className="text-4xl md:text-5xl font-bold text-foreground mb-6"
            style={{ fontFamily: 'Cormorant Garamond, serif' }}
          >
            Iconic Tanzania <span className="text-safari-sunset">Destinations</span>
          </h2>
          <p
            className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
            style={{ fontFamily: 'Open Sans, sans-serif' }}
          >
            Explore the most spectacular national parks and conservation areas
            that make Tanzania a premier safari destination in East Africa.
          </p>
        </div>
      </div>

      {/* Interactive Leaflet Map - Full Width */}
      <div className="mb-16 w-full relative z-10">
        <LeafletMap
          destinations={destinations}
          onDestinationClick={handleDestinationClick}
          showRoutes={false}
          height="500px"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">

        {/* Selected Destination Details */}
        {selectedDestination && (
          <div className="mb-12 p-6 bg-safari-warm-amber/10 border border-safari-warm-amber/30 rounded-2xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-2xl font-bold text-safari-coffee-brown">
                {selectedDestination.name}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedDestination(null)}
              >
                Close
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <img
                  src={selectedDestination.image}
                  alt={selectedDestination.name}
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>
              <div className="space-y-4">
                <p className="text-gray-700">{selectedDestination.description}</p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="block text-gray-500">Area</span>
                    <span className="font-semibold">{selectedDestination.area}</span>
                  </div>
                  <div>
                    <span className="block text-gray-500">Established</span>
                    <span className="font-semibold">{selectedDestination.established}</span>
                  </div>
                  <div>
                    <span className="block text-gray-500">Best Time</span>
                    <span className="font-semibold">{selectedDestination.bestTime}</span>
                  </div>
                  <div>
                    <span className="block text-gray-500">Available Tours</span>
                    <span className="font-semibold">{selectedDestination.tours}</span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button asChild size="sm">
                    <Link to={`/tours?destination=${selectedDestination.id}`}>
                      View Tours
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <Link to="/tour-builder">
                      Plan Custom Tour
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* World Map Section - Connections to Tanzania */}
        <div className="mb-8 md:mb-16 relative px-2 sm:px-0">
          <div className="text-center mb-4 md:mb-8">
            <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-foreground mb-2 md:mb-4">
              Global Connections to <span className="text-safari-sunset">Tanzania</span>
            </h3>
            <p className="text-sm sm:text-base md:text-lg text-muted-foreground max-w-2xl mx-auto px-4">
              Discover how travelers from around the world journey to experience Tanzania's incredible wildlife and landscapes.
            </p>
          </div>
          <div className="h-48 sm:h-64 md:h-80 lg:h-96 xl:h-[500px] w-full max-w-full rounded-2xl md:rounded-3xl overflow-hidden bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
            <WorldMap
              dots={[
                {
                  start: { lat: 40.7128, lng: -74.0060 }, // New York, USA
                  end: { lat: -17.153389, lng: 34.6857 }, // Serengeti, Tanzania
                },
                {
                  start: { lat: 51.5074, lng: -0.1278 }, // London, UK
                  end: { lat: -17.153389, lng: 34.6857 }, // Serengeti, Tanzania
                },
                {
                  start: { lat: 48.8566, lng: 2.3522 }, // Paris, France
                  end: { lat: -17.153389, lng: 34.6857 }, // Serengeti, Tanzania
                },
                {
                  start: { lat: 35.6762, lng: 139.6503 }, // Tokyo, Japan
                  end: { lat: -17.153389, lng: 34.6857 }, // Serengeti, Tanzania
                },
                {
                  start: { lat: -33.8688, lng: 151.2093 }, // Sydney, Australia
                  end: { lat: -17.153389, lng: 34.6857 }, // Serengeti, Tanzania
                },
                {
                  start: { lat: 55.7558, lng: 37.6176 }, // Moscow, Russia
                  end: { lat: -17.153389, lng: 34.6857 }, // Serengeti, Tanzania
                },
                {
                  start: { lat: -23.5505, lng: -46.6333 }, // São Paulo, Brazil
                  end: { lat: -17.153389, lng: 34.6857 }, // Serengeti, Tanzania
                },
                {
                  start: { lat: 28.6139, lng: 77.2090 }, // New Delhi, India
                  end: { lat: -17.153389, lng: 34.6857 }, // Serengeti, Tanzania
                },
              ]}
              lineColor="#FF6200"
            />
          </div>
        </div>

        {/* Interactive Planning CTA */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-3xl p-12 text-white text-center relative overflow-hidden">
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }} />
          </div>

          <div className="relative z-10">
            <h3 className="text-4xl font-bold mb-4">Plan Your Perfect Safari Route</h3>
            <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90 leading-relaxed">
              Use our interactive planning tools to explore all destinations and create your ideal safari itinerary
              with real-time availability, pricing, and expert recommendations.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                asChild
                size="lg"
                className="bg-white text-green-600 hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all"
              >
                <Link to="/tour-builder">
                  <MapPin className="mr-2 h-5 w-5" />
                  Interactive Planner
                </Link>
              </Button>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="border-white text-green-600 hover:bg-white hover:text-green-600 transition-colors"
              >
                <Link to="/gallery">
                  <Camera className="mr-2 h-5 w-5" />
                  View Gallery
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DestinationShowcase;

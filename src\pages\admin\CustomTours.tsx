import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import PageLoader from '@/components/ui/PageLoader';
import { 
  Calendar, 
  Users, 
  DollarSign, 
  MapPin, 
  Mail, 
  Phone, 
  Eye, 
  Edit, 
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  Filter
} from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { CustomTourRequest } from '@/types/firebase';
import { useToast } from '@/hooks/use-toast';

const CustomTours = () => {
  const [customTours, setCustomTours] = useState<CustomTourRequest[]>([]);
  const [filteredTours, setFilteredTours] = useState<CustomTourRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTour, setSelectedTour] = useState<CustomTourRequest | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    loadCustomTours();
  }, []);

  useEffect(() => {
    filterTours();
  }, [customTours, statusFilter, searchTerm]);

  const loadCustomTours = async () => {
    try {
      const data = await FirebaseService.getCustomTourRequests();
      setCustomTours(data as CustomTourRequest[]);
    } catch (error) {
      console.error('Error loading custom tours:', error);
      toast({
        title: "Error loading custom tours",
        description: "Please try again later.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const filterTours = () => {
    let filtered = customTours;

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(tour => tour.status === statusFilter);
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(tour =>
        tour.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tour.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tour.destinations.some(dest => dest.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Sort by creation date (newest first)
    filtered.sort((a, b) => {
      const dateA = a.createdAt?.toDate?.() || new Date(0);
      const dateB = b.createdAt?.toDate?.() || new Date(0);
      return dateB.getTime() - dateA.getTime();
    });

    setFilteredTours(filtered);
  };

  const handleStatusUpdate = async (tourId: string, newStatus: string, adminNotes?: string, quotedPrice?: number) => {
    try {
      const updateData: Partial<CustomTourRequest> = { 
        status: newStatus as CustomTourRequest['status']
      };
      
      if (adminNotes) updateData.adminNotes = adminNotes;
      if (quotedPrice) updateData.quotedPrice = quotedPrice;

      await FirebaseService.updateCustomTourRequest(tourId, updateData);
      
      // Update local state
      setCustomTours(prev => prev.map(tour => 
        tour.id === tourId 
          ? { ...tour, ...updateData }
          : tour
      ));

      toast({
        title: "Status updated successfully",
        description: `Tour request status changed to ${newStatus}`
      });

      setIsEditDialogOpen(false);
    } catch (error) {
      console.error('Error updating status:', error);
      toast({
        title: "Error updating status",
        description: "Please try again later.",
        variant: "destructive"
      });
    }
  };

  const handleDelete = async (tourId: string) => {
    if (!confirm('Are you sure you want to delete this custom tour request?')) {
      return;
    }

    try {
      await FirebaseService.deleteCustomTourRequest(tourId);
      setCustomTours(prev => prev.filter(tour => tour.id !== tourId));
      toast({
        title: "Tour request deleted",
        description: "The custom tour request has been removed."
      });
    } catch (error) {
      console.error('Error deleting tour:', error);
      toast({
        title: "Error deleting tour request",
        description: "Please try again later.",
        variant: "destructive"
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
      reviewed: { variant: 'outline' as const, icon: Eye, color: 'text-blue-600' },
      quoted: { variant: 'default' as const, icon: DollarSign, color: 'text-green-600' },
      confirmed: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      cancelled: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="capitalize">
        <Icon className="h-3 w-3 mr-1" />
        {status}
      </Badge>
    );
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (loading) {
    return (
      <PageLoader
        title="Loading Custom Tours..."
        subtitle="Gathering custom safari tour requests..."
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Custom Tour Requests</h1>
          <p className="text-sm md:text-base text-gray-600">Manage custom safari tour requests from customers</p>
        </div>
        <Badge variant="outline" className="text-sm md:text-lg px-3 md:px-4 py-1 md:py-2">
          {filteredTours.length} Request{filteredTours.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4 items-stretch lg:items-center justify-between bg-white p-4 rounded-lg border">
        <div className="flex flex-col sm:flex-row gap-4 items-stretch sm:items-center">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="reviewed">Reviewed</SelectItem>
                <SelectItem value="quoted">Quoted</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex-1 lg:max-w-md">
          <Input
            placeholder="Search by name, email, or destination..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Tour Requests Grid */}
      {filteredTours.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No custom tour requests found</h3>
            <p className="text-gray-500">
              {statusFilter !== 'all' || searchTerm 
                ? 'Try adjusting your filters or search criteria.'
                : 'Custom tour requests will appear here when customers submit them.'}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
          {filteredTours.map((tour) => (
            <Card key={tour.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-base md:text-lg truncate">{tour.name}</CardTitle>
                    <p className="text-xs md:text-sm text-gray-600 flex items-center mt-1">
                      <Mail className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span className="truncate">{tour.email}</span>
                    </p>
                    {tour.phone && (
                      <p className="text-xs md:text-sm text-gray-600 flex items-center mt-1">
                        <Phone className="h-3 w-3 mr-1 flex-shrink-0" />
                        {tour.phone}
                      </p>
                    )}
                  </div>
                  <div className="flex-shrink-0">
                    {getStatusBadge(tour.status)}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-3 pt-0">
                <div className="grid grid-cols-2 gap-2 text-xs md:text-sm">
                  <div className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1 text-gray-500 flex-shrink-0" />
                    <span className="truncate">{tour.duration} days</span>
                  </div>
                  <div className="flex items-center">
                    <Users className="h-3 w-3 mr-1 text-gray-500 flex-shrink-0" />
                    <span className="truncate">{tour.participants} people</span>
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="h-3 w-3 mr-1 text-gray-500 flex-shrink-0" />
                    <span className="truncate">${tour.budget[0]?.toLocaleString()}/person</span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 mr-1 text-gray-500 flex-shrink-0" />
                    <span className="truncate">{tour.destinations.length} destinations</span>
                  </div>
                </div>

                <div className="text-xs text-gray-500">
                  Submitted: {formatDate(tour.createdAt)}
                </div>

                {tour.quotedPrice && (
                  <div className="bg-green-50 p-2 rounded text-xs md:text-sm">
                    <strong>Quoted Price: ${tour.quotedPrice.toLocaleString()}</strong>
                  </div>
                )}

                <div className="flex gap-1 md:gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedTour(tour);
                      setIsViewDialogOpen(true);
                    }}
                    className="flex-1 text-xs md:text-sm p-2"
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">View</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedTour(tour);
                      setIsEditDialogOpen(true);
                    }}
                    className="flex-1 text-xs md:text-sm p-2"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">Edit</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(tour.id)}
                    className="text-red-600 hover:text-red-700 p-2"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Custom Tour Request Details</DialogTitle>
          </DialogHeader>
          {selectedTour && (
            <div className="space-y-6">
              {/* Customer Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Customer Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div><strong>Name:</strong> {selectedTour.name}</div>
                    <div><strong>Email:</strong> {selectedTour.email}</div>
                    <div><strong>Phone:</strong> {selectedTour.phone || 'Not provided'}</div>
                    <div><strong>Status:</strong> {getStatusBadge(selectedTour.status)}</div>
                    <div><strong>Submitted:</strong> {formatDate(selectedTour.createdAt)}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Trip Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div><strong>Duration:</strong> {selectedTour.duration} days</div>
                    <div><strong>Participants:</strong> {selectedTour.participants} people</div>
                    <div><strong>Budget:</strong> ${selectedTour.budget[0]?.toLocaleString()}/person</div>
                    <div><strong>Start Date:</strong> {selectedTour.startDate || 'Flexible'}</div>
                    <div><strong>Accommodation:</strong> {selectedTour.accommodation}</div>
                    <div><strong>Fitness Level:</strong> {selectedTour.fitnessLevel}</div>
                  </CardContent>
                </Card>
              </div>

              {/* Destinations & Preferences */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Destinations ({selectedTour.destinations.length})</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {selectedTour.destinations.map((dest, index) => (
                        <Badge key={index} variant="outline">{dest}</Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Interests ({selectedTour.interests.length})</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {selectedTour.interests.map((interest, index) => (
                        <Badge key={index} variant="secondary">{interest}</Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Activities & Special Requests */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Activities ({selectedTour.activities.length})</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {selectedTour.activities.map((activity, index) => (
                        <Badge key={index} variant="outline">{activity}</Badge>
                      ))}
                    </div>
                    {selectedTour.photographyInterest && (
                      <div className="mt-2">
                        <Badge className="bg-blue-100 text-blue-800">Photography Interest</Badge>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Special Requests</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">
                      {selectedTour.specialRequests || 'No special requests'}
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Admin Notes & Pricing */}
              {(selectedTour.adminNotes || selectedTour.quotedPrice) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Admin Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {selectedTour.quotedPrice && (
                      <div><strong>Quoted Price:</strong> ${selectedTour.quotedPrice.toLocaleString()}</div>
                    )}
                    {selectedTour.adminNotes && (
                      <div>
                        <strong>Admin Notes:</strong>
                        <p className="mt-1 text-sm bg-gray-50 p-2 rounded">{selectedTour.adminNotes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Update Tour Request</DialogTitle>
          </DialogHeader>
          {selectedTour && (
            <EditTourForm
              tour={selectedTour}
              onUpdate={handleStatusUpdate}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Edit Form Component
const EditTourForm: React.FC<{
  tour: CustomTourRequest;
  onUpdate: (tourId: string, status: string, adminNotes?: string, quotedPrice?: number) => void;
  onCancel: () => void;
}> = ({ tour, onUpdate, onCancel }) => {
  const [status, setStatus] = useState(tour.status);
  const [adminNotes, setAdminNotes] = useState(tour.adminNotes || '');
  const [quotedPrice, setQuotedPrice] = useState(tour.quotedPrice?.toString() || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate(
      tour.id,
      status,
      adminNotes,
      quotedPrice ? parseFloat(quotedPrice) : undefined
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="status">Status</Label>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="reviewed">Reviewed</SelectItem>
            <SelectItem value="quoted">Quoted</SelectItem>
            <SelectItem value="confirmed">Confirmed</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="quotedPrice">Quoted Price (USD)</Label>
        <Input
          id="quotedPrice"
          type="number"
          value={quotedPrice}
          onChange={(e) => setQuotedPrice(e.target.value)}
          placeholder="Enter quoted price"
        />
      </div>

      <div>
        <Label htmlFor="adminNotes">Admin Notes</Label>
        <Textarea
          id="adminNotes"
          value={adminNotes}
          onChange={(e) => setAdminNotes(e.target.value)}
          placeholder="Add notes about this request..."
          rows={3}
        />
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Update Request
        </Button>
      </div>
    </form>
  );
};

export default CustomTours;

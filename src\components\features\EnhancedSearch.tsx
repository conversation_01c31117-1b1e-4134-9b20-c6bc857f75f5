import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  X, 
  MapPin, 
  Calendar, 
  DollarSign, 
  Users, 
  Loader2,
  Filter,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useEnhancedSearch } from '@/hooks/useEnhancedSearch';
import { SearchResult } from '@/types/firebase';

interface EnhancedSearchProps {
  placeholder?: string;
  className?: string;
  onResultClick?: () => void;
  compact?: boolean; // For floating glass header
}

const EnhancedSearch: React.FC<EnhancedSearchProps> = ({
  placeholder = "Search tours and destinations...",
  className = "",
  onResultClick,
  compact = false
}) => {
  const navigate = useNavigate();
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [showFilters, setShowFilters] = useState(false);

  const {
    query,
    results,
    loading,
    error,
    showResults,
    filters,
    setQuery,
    setFilters,
    clearSearch,
    hideResults,
    showResults: showSearchResults,
  } = useEnhancedSearch();

  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        hideResults();
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [hideResults]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showResults && e.key !== 'ArrowDown') return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        if (!showResults && query.length >= 2) {
          showSearchResults();
          setSelectedIndex(0);
        } else if (selectedIndex < results.length - 1) {
          setSelectedIndex(prev => prev + 1);
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handleResultClick(results[selectedIndex]);
        } else if (query.length >= 2 && results.length > 0) {
          // If no item selected but there are results, select first one
          handleResultClick(results[0]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        hideResults();
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
      case 'Tab':
        // Allow tab to close results
        hideResults();
        setSelectedIndex(-1);
        break;
    }
  };

  // Handle result click
  const handleResultClick = (result: SearchResult) => {
    const path = result.type === 'tour' 
      ? `/tours/${result.id}` 
      : `/destinations/${result.id}`;
    
    navigate(path);
    hideResults();
    setSelectedIndex(-1);
    onResultClick?.();
  };

  // Handle input focus
  const handleInputFocus = () => {
    if (query.length >= 2 && (results.length > 0 || error)) {
      showSearchResults();
    }
  };

  // Clear search
  const handleClearSearch = () => {
    clearSearch();
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // Group results by type
  const groupedResults = results.reduce((acc, result) => {
    if (!acc[result.type]) {
      acc[result.type] = [];
    }
    acc[result.type].push(result);
    return acc;
  }, {} as Record<string, SearchResult[]>);

  return (
    <div ref={searchRef} className={`relative ${compact ? 'w-64' : 'w-full max-w-md'} ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <Search className={`absolute left-3 top-1/2 -translate-y-1/2 ${compact ? 'h-3 w-3 text-gray-500' : 'h-4 w-4 text-gray-400'}`} />
        <Input
          ref={inputRef}
          type="text"
          placeholder={compact ? "Search..." : placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          className={`${compact ? 'pl-8 pr-16 h-9 text-sm bg-white/25 border-white/40 placeholder:text-gray-600 text-gray-800 rounded-full focus:bg-white/35 focus:border-white/60 transition-all duration-300' : 'pl-10 pr-20'}`}
          style={compact ? {
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1)'
          } : {}}
        />
        <div className={`absolute right-1 top-1/2 -translate-y-1/2 flex gap-1`}>
          {/* Filter Toggle */}
          {!compact && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className={`h-8 w-8 p-0 rounded-full transition-all duration-300 ${
                showFilters ? 'transform scale-110' : 'hover:scale-105'
              }`}
              style={{
                background: showFilters
                  ? 'linear-gradient(135deg, #4ade80 0%, #22c55e 100%)'
                  : 'rgba(255, 255, 255, 0.4)',
                backdropFilter: 'blur(10px)',
                WebkitBackdropFilter: 'blur(10px)',
                border: showFilters
                  ? '1px solid rgba(34, 197, 94, 0.3)'
                  : '1px solid rgba(255, 255, 255, 0.4)',
                boxShadow: showFilters
                  ? '0 4px 16px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
                  : '0 2px 8px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
                color: showFilters ? 'white' : '#6b7280',
              }}
            >
              <Filter className="h-4 w-4" />
            </Button>
          )}

          {/* Loading Indicator */}
          {loading && (
            <div
              className="h-8 w-8 flex items-center justify-center rounded-full"
              style={{
                background: 'rgba(255, 255, 255, 0.4)',
                backdropFilter: 'blur(10px)',
                WebkitBackdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.4)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
              }}
            >
              <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
            </div>
          )}

          {/* Clear Button */}
          {query && !loading && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearSearch}
              className="h-8 w-8 p-0 rounded-full transition-all duration-300 hover:scale-105"
              style={{
                background: 'rgba(255, 255, 255, 0.4)',
                backdropFilter: 'blur(10px)',
                WebkitBackdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.4)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
                color: '#6b7280',
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div
          className="absolute top-full mt-2 w-80 z-50 rounded-2xl shadow-2xl border border-white/30"
          style={{
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px) saturate(180%) brightness(110%)',
            WebkitBackdropFilter: 'blur(20px) saturate(180%) brightness(110%)',
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 0 rgba(255, 255, 255, 0.3)',
          }}
        >
          <div className="p-4 space-y-3">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-xs text-gray-800">Search Filters</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setFilters({ type: 'all' })}
                className="text-[10px] bg-white/40 hover:bg-white/60 border border-white/40 hover:border-white/60 transition-all duration-300 px-2 py-1 h-6"
                style={{
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                }}
              >
                Clear
              </Button>
            </div>

            {/* Search Type */}
            <div>
              <label className="text-[10px] font-medium text-gray-700 mb-2 block">Search In</label>
              <div className="flex gap-2">
                {[
                  { value: 'all', label: 'All' },
                  { value: 'tour', label: 'Tours' },
                  { value: 'destination', label: 'Destinations' }
                ].map((option) => (
                  <Button
                    key={option.value}
                    size="sm"
                    onClick={() => setFilters({ type: option.value as any })}
                    className={`text-[10px] transition-all duration-300 px-2 py-1 h-6 ${
                      filters.type === option.value
                        ? 'text-white shadow-lg transform scale-105'
                        : 'text-gray-700 hover:text-gray-900 hover:scale-105'
                    }`}
                    style={filters.type === option.value ? {
                      background: 'linear-gradient(135deg, #4ade80 0%, #22c55e 100%)',
                      boxShadow: '0 4px 16px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
                      border: '1px solid rgba(34, 197, 94, 0.3)',
                    } : {
                      background: 'rgba(255, 255, 255, 0.4)',
                      backdropFilter: 'blur(10px)',
                      WebkitBackdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.4)',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
                    }}
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search Results */}
      {showResults && (results.length > 0 || error) && (
        <div
          className="absolute top-full mt-2 w-full z-50 max-h-96 overflow-y-auto rounded-2xl shadow-2xl border border-white/30"
          style={{
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px) saturate(180%) brightness(110%)',
            WebkitBackdropFilter: 'blur(20px) saturate(180%) brightness(110%)',
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 0 rgba(255, 255, 255, 0.3)',
          }}
        >
          <div className="p-3">
            {error ? (
              <div className="p-3 text-center">
                <p className="text-xs text-red-600 mb-2 font-medium">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuery(query)} // Trigger retry
                  className="text-[10px] bg-white/60 hover:bg-white/80 border-white/40 hover:border-white/60 transition-all duration-300 px-2 py-1 h-6"
                  style={{
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                  }}
                >
                  Try Again
                </Button>
              </div>
            ) : results.length > 0 ? (
              <div className="space-y-2">
                {/* Results Header */}
                <div className="px-3 py-2 text-xs font-medium text-gray-600 border-b border-white/40">
                  {results.length} result{results.length !== 1 ? 's' : ''} found
                </div>

                {/* Tours Section */}
                {groupedResults.tour && (
                  <div className="mb-3">
                    <div className="px-2 py-1.5 text-[10px] font-semibold text-gray-600 uppercase tracking-wider bg-white/30 rounded-lg mb-2">
                      Tours ({groupedResults.tour.length})
                    </div>
                    <div className="space-y-1">
                      {groupedResults.tour.map((result, index) => {
                        const globalIndex = results.indexOf(result);
                        return (
                          <SearchResultItem
                            key={result.id}
                            result={result}
                            isSelected={selectedIndex === globalIndex}
                            onClick={() => handleResultClick(result)}
                          />
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Destinations Section */}
                {groupedResults.destination && (
                  <div className="mb-3">
                    <div className="px-2 py-1.5 text-[10px] font-semibold text-gray-600 uppercase tracking-wider bg-white/30 rounded-lg mb-2">
                      Destinations ({groupedResults.destination.length})
                    </div>
                    <div className="space-y-1">
                      {groupedResults.destination.map((result, index) => {
                        const globalIndex = results.indexOf(result);
                        return (
                          <SearchResultItem
                            key={result.id}
                            result={result}
                            isSelected={selectedIndex === globalIndex}
                            onClick={() => handleResultClick(result)}
                          />
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            ) : query.length >= 2 ? (
              <div className="p-4 text-center">
                <p className="text-xs text-gray-600 font-medium">No results found</p>
                <p className="text-[10px] text-gray-500 mt-1">
                  Try adjusting your search terms
                </p>
              </div>
            ) : null}
          </div>
        </div>
      )}
    </div>
  );
};

// Search Result Item Component
interface SearchResultItemProps {
  result: SearchResult;
  isSelected: boolean;
  onClick: () => void;
}

const SearchResultItem: React.FC<SearchResultItemProps> = ({
  result,
  isSelected,
  onClick
}) => {
  return (
    <div
      className={`flex items-start space-x-2 p-2 rounded-xl cursor-pointer transition-all duration-300 ${
        isSelected
          ? 'transform scale-[1.02] shadow-lg'
          : 'hover:transform hover:scale-[1.01] hover:shadow-md'
      }`}
      style={{
        background: isSelected
          ? 'rgba(34, 197, 94, 0.15)'
          : 'rgba(255, 255, 255, 0.4)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        border: isSelected
          ? '1px solid rgba(34, 197, 94, 0.3)'
          : '1px solid rgba(255, 255, 255, 0.3)',
        boxShadow: isSelected
          ? '0 4px 16px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3)'
          : '0 2px 8px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
      }}
      onClick={onClick}
      onMouseEnter={(e) => {
        if (!isSelected) {
          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.6)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3)';
        }
      }}
      onMouseLeave={(e) => {
        if (!isSelected) {
          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.4)';
          e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.2)';
        }
      }}
    >
      {/* Image */}
      <div
        className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0 shadow-md"
        style={{
          background: 'rgba(255, 255, 255, 0.3)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.4)',
        }}
      >
        {result.image ? (
          <img
            src={result.image.startsWith('http')
              ? result.image
              : `https://images.unsplash.com/${result.image}?auto=format&fit=crop&w=80&h=80`
            }
            alt={result.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <MapPin className="h-4 w-4 text-gray-500" />
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <h4 className="font-semibold text-xs text-gray-800 truncate">
            {result.title}
          </h4>
          <div className="text-right ml-2 flex-shrink-0">
            {result.price && (
              <div
                className="text-xs font-bold text-green-600 px-1.5 py-0.5 rounded-md"
                style={{
                  background: 'rgba(34, 197, 94, 0.1)',
                  border: '1px solid rgba(34, 197, 94, 0.2)',
                }}
              >
                ${result.price}
              </div>
            )}
            {result.rating && (
              <div className="text-[10px] text-gray-600 mt-1 font-medium">⭐ {result.rating}</div>
            )}
          </div>
        </div>

        <p className="text-[10px] text-gray-700 mt-1.5 overflow-hidden leading-relaxed" style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical'
        }}>
          {result.description}
        </p>

        <div className="flex items-center gap-1 mt-2 flex-wrap">
          <Badge
            variant="outline"
            className="text-[10px] bg-white/50 border-white/60 text-gray-700 hover:bg-white/70 transition-colors px-1.5 py-0.5"
            style={{
              backdropFilter: 'blur(5px)',
              WebkitBackdropFilter: 'blur(5px)',
            }}
          >
            <MapPin className="w-2.5 h-2.5 mr-0.5" />
            {result.location}
          </Badge>

          {result.duration && (
            <Badge
              variant="outline"
              className="text-[10px] bg-white/50 border-white/60 text-gray-700 hover:bg-white/70 transition-colors px-1.5 py-0.5"
              style={{
                backdropFilter: 'blur(5px)',
                WebkitBackdropFilter: 'blur(5px)',
              }}
            >
              <Calendar className="w-2.5 h-2.5 mr-0.5" />
              {result.duration}
            </Badge>
          )}

          {result.difficulty && (
            <Badge
              variant="outline"
              className="text-[10px] capitalize bg-white/50 border-white/60 text-gray-700 hover:bg-white/70 transition-colors px-1.5 py-0.5"
              style={{
                backdropFilter: 'blur(5px)',
                WebkitBackdropFilter: 'blur(5px)',
              }}
            >
              {result.difficulty}
            </Badge>
          )}

          {result.maxGroupSize && (
            <Badge
              variant="outline"
              className="text-[10px] bg-white/50 border-white/60 text-gray-700 hover:bg-white/70 transition-colors px-1.5 py-0.5"
              style={{
                backdropFilter: 'blur(5px)',
                WebkitBackdropFilter: 'blur(5px)',
              }}
            >
              <Users className="w-2.5 h-2.5 mr-0.5" />
              Max {result.maxGroupSize}
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedSearch;

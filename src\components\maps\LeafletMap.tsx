import React, { useEffect, useLayoutEffect, useRef } from 'react';
import { MapPin } from 'lucide-react';

interface Destination {
  id: string;
  name: string;
  lat: number;
  lng: number;
  description: string;
  image: string;
}

interface LeafletMapProps {
  destinations: Destination[];
  onDestinationClick?: (destination: Destination) => void;
  showRoutes?: boolean;
  height?: string;
}

const LeafletMap: React.FC<LeafletMapProps> = ({ 
  destinations, 
  onDestinationClick, 
  showRoutes = false,
  height = "400px" 
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);

  useLayoutEffect(() => {
    // Dynamically import Leaflet to avoid SSR issues
    const initializeMap = async () => {
      

      if (!mapRef.current) {
        
        return;
      }

      // Check if container has dimensions
      const containerRect = mapRef.current.getBoundingClientRect();
   

      if (containerRect.width === 0 || containerRect.height === 0) {
   
        setTimeout(initializeMap, 100);
        return;
      }

      if (destinations.length === 0) {
    
        return;
      }

      try {
        
        const L = await import('leaflet');
    

        // Clear any existing map instance
        if (mapInstanceRef.current) {
          
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
        }

        // Fix for default markers in Leaflet with webpack
       
        delete (L.Icon.Default.prototype as any)._getIconUrl;
        L.Icon.Default.mergeOptions({
          iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
          iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
          shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
        });

        
        // Initialize map with proper options
        const map = L.map(mapRef.current, {
          center: [-3.5, 35.0],
          zoom: 7,
          zoomControl: true,
          attributionControl: true,
          preferCanvas: false
        });
      
        // Add tile layer with error handling
        const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors',
          maxZoom: 18,
          minZoom: 1,
          errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        });

        tileLayer.addTo(map);
       

        // Force map to invalidate size after a short delay
        setTimeout(() => {
          
          map.invalidateSize();
          
        }, 100);

        // Wait for map to be ready
        map.whenReady(() => {
          // Force another size invalidation when ready
          setTimeout(() => {
            map.invalidateSize();
          }, 50);
        });



        // Custom marker icon
        const customIcon = L.divIcon({
          html: `<div style="background-color: #ea580c; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center;">
                   <svg width="12" height="12" viewBox="0 0 24 24" fill="white">
                     <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                     <circle cx="12" cy="10" r="3"/>
                   </svg>
                 </div>`,
          className: 'custom-div-icon',
          iconSize: [24, 24],
          iconAnchor: [12, 12]
        });
       

        // Add markers for each destination
        
        destinations.forEach((destination, index) => {
          

          // Validate coordinates
          if (typeof destination.lat !== 'number' || typeof destination.lng !== 'number') {
 
            return;
          }

          if (isNaN(destination.lat) || isNaN(destination.lng)) {
    
            return;
          }

          try {
            // Try custom icon first, fallback to default if it fails
            let marker;
            try {
              marker = L.marker([destination.lat, destination.lng], { icon: customIcon });
            } catch (iconError) {
              marker = L.marker([destination.lat, destination.lng]);
            }

            marker.addTo(map);
            

            // Create popup content with proper image URL handling
            const imageUrl = destination.image.startsWith('http')
              ? destination.image
              : `https://images.unsplash.com/${destination.image}?auto=format&fit=crop&w=200&h=120`;

            const popupContent = `
              <div style="padding: 10px; max-width: 200px;">
                <img src="${imageUrl}"
                     style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px; margin-bottom: 8px;"
                     onerror="this.style.display='none'" />
                <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 16px; font-weight: bold;">${destination.name}</h3>
                <p style="margin: 0; color: #6b7280; font-size: 14px; line-height: 1.4;">${destination.description}</p>
              </div>
            `;

            marker.bindPopup(popupContent);
           

            // Add click handler
            marker.on('click', () => {
              
              if (onDestinationClick) {
                onDestinationClick(destination);
              }
            });

    
          } catch (markerError) {
            
          }
        });



        // Store map instance
        mapInstanceRef.current = map;

        // Handle routes if enabled
        if (showRoutes && destinations.length > 1) {
          const latlngs = destinations.map(dest => [dest.lat, dest.lng] as [number, number]);
          L.polyline(latlngs, {
            color: '#ea580c',
            weight: 3,
            opacity: 0.8
          }).addTo(map);
        }

      } catch (error) {
        // Map initialization failed silently
      }
    };

    initializeMap();

    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [destinations, onDestinationClick, showRoutes]);

  return (
    <div
      ref={mapRef}
      style={{
        height,
        width: '100%',
        minHeight: '400px',
        position: 'relative',
        zIndex: 1
      }}
      className="w-full"
    />
  );
};

export default LeafletMap;

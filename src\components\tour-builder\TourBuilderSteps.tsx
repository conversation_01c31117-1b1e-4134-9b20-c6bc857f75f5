
import React from 'react';
import { Check } from 'lucide-react';

interface Step {
  id: number;
  title: string;
  description: string;
}

interface TourBuilderStepsProps {
  steps: Step[];
  currentStep: number;
  onStepChange: (step: number) => void;
}

const TourBuilderSteps: React.FC<TourBuilderStepsProps> = ({ 
  steps, 
  currentStep, 
  onStepChange 
}) => {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center space-x-4 flex-1">
          <div 
            className={`relative flex items-center justify-center w-10 h-10 rounded-full border-2 cursor-pointer transition-colors ${
              currentStep > step.id 
                ? 'bg-green-600 border-green-600 text-white' 
                : currentStep === step.id 
                  ? 'bg-orange-600 border-orange-600 text-white' 
                  : 'border-gray-300 text-gray-300'
            }`}
            onClick={() => onStepChange(step.id)}
          >
            {currentStep > step.id ? (
              <Check className="h-5 w-5" />
            ) : (
              <span className="text-sm font-semibold">{step.id}</span>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className={`text-sm font-medium ${
              currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'
            }`}>
              {step.title}
            </h3>
            <p className={`text-xs ${
              currentStep >= step.id ? 'text-gray-600' : 'text-gray-400'
            }`}>
              {step.description}
            </p>
          </div>
          
          {index < steps.length - 1 && (
            <div className={`hidden md:block w-full h-0.5 mx-4 ${
              currentStep > step.id ? 'bg-green-600' : 'bg-gray-300'
            }`} />
          )}
        </div>
      ))}
    </div>
  );
};

export default TourBuilderSteps;

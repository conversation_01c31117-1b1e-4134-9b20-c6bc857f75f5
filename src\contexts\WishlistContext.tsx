
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { FirebaseService } from '@/services/firebase';
import { useToast } from '@/hooks/use-toast';

interface WishlistItem {
  id: string;
  title: string;
  price: number;
  image: string;
  type: 'tour' | 'destination';
}

interface WishlistContextType {
  wishlist: WishlistItem[];
  addToWishlist: (item: WishlistItem) => void;
  removeFromWishlist: (id: string) => void;
  isInWishlist: (id: string) => boolean;
  loading: boolean;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export const WishlistProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [wishlist, setWishlist] = useState<WishlistItem[]>([]);
  const [loading, setLoading] = useState(false);
  const { currentUser } = useAuth();
  const { toast } = useToast();

  // Load wishlist from Firebase when user logs in
  useEffect(() => {
    if (currentUser) {
      loadWishlist();
    } else {
      setWishlist([]);
    }
  }, [currentUser]);

  const loadWishlist = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      const userWishlist = await FirebaseService.getUserWishlist(currentUser.uid);
      if (userWishlist) {
        // Convert Firebase wishlist format to our format
        const wishlistItems: WishlistItem[] = [
          ...(userWishlist.tourIds || []).map((tourId: string) => ({
            id: tourId,
            title: 'Loading...', // Will be populated when we fetch tour details
            price: 0,
            image: '',
            type: 'tour' as const
          })),
          ...(userWishlist.destinationIds || []).map((destId: string) => ({
            id: destId,
            title: 'Loading...',
            price: 0,
            image: '',
            type: 'destination' as const
          }))
        ];
        setWishlist(wishlistItems);

        // Fetch full details for wishlist items
        await populateWishlistDetails(wishlistItems);
      }
    } catch (error) {
      console.error('Error loading wishlist:', error);
    } finally {
      setLoading(false);
    }
  };

  const populateWishlistDetails = async (items: WishlistItem[]) => {
    try {
      const updatedItems = await Promise.all(
        items.map(async (item) => {
          if (item.type === 'tour') {
            const tour = await FirebaseService.getTour(item.id);
            if (tour) {
              return {
                ...item,
                title: tour.title,
                price: tour.price,
                image: tour.images?.[0] || 'photo-1516426122078-c23e76319801'
              };
            }
          } else if (item.type === 'destination') {
            const destination = await FirebaseService.getDestination(item.id);
            if (destination) {
              return {
                ...item,
                title: destination.name,
                price: 0, // Destinations don't have prices
                image: destination.images?.[0] || 'photo-1516426122078-c23e76319801'
              };
            }
          }
          return item;
        })
      );
      setWishlist(updatedItems);
    } catch (error) {
      console.error('Error populating wishlist details:', error);
    }
  };

  const addToWishlist = async (item: WishlistItem) => {
    if (!currentUser) {
      toast({
        title: "Please log in",
        description: "You need to be logged in to add items to your wishlist.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Add to local state immediately for better UX
      setWishlist(prev => [...prev.filter(w => w.id !== item.id), item]);

      // Update Firebase
      const currentWishlist = await FirebaseService.getUserWishlist(currentUser.uid);
      const tourIds = currentWishlist?.tourIds || [];
      const destinationIds = currentWishlist?.destinationIds || [];

      if (item.type === 'tour') {
        if (!tourIds.includes(item.id)) {
          tourIds.push(item.id);
        }
      } else {
        if (!destinationIds.includes(item.id)) {
          destinationIds.push(item.id);
        }
      }

      await FirebaseService.updateUserWishlist(currentUser.uid, tourIds, destinationIds);

      toast({
        title: "Added to wishlist",
        description: `${item.title} has been added to your wishlist.`
      });
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      // Revert local state on error
      setWishlist(prev => prev.filter(w => w.id !== item.id));
      toast({
        title: "Error",
        description: "Failed to add item to wishlist. Please try again.",
        variant: "destructive"
      });
    }
  };

  const removeFromWishlist = async (id: string) => {
    if (!currentUser) return;

    try {
      // Remove from local state immediately
      const itemToRemove = wishlist.find(item => item.id === id);
      setWishlist(prev => prev.filter(w => w.id !== id));

      // Update Firebase
      const currentWishlist = await FirebaseService.getUserWishlist(currentUser.uid);
      const tourIds = (currentWishlist?.tourIds || []).filter((tourId: string) => tourId !== id);
      const destinationIds = (currentWishlist?.destinationIds || []).filter((destId: string) => destId !== id);

      await FirebaseService.updateUserWishlist(currentUser.uid, tourIds, destinationIds);

      toast({
        title: "Removed from wishlist",
        description: `${itemToRemove?.title || 'Item'} has been removed from your wishlist.`
      });
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      // Revert local state on error
      loadWishlist();
      toast({
        title: "Error",
        description: "Failed to remove item from wishlist. Please try again.",
        variant: "destructive"
      });
    }
  };

  const isInWishlist = (id: string) => {
    return wishlist.some(item => item.id === id);
  };

  return (
    <WishlistContext.Provider value={{ wishlist, addToWishlist, removeFromWishlist, isInWishlist, loading }}>
      {children}
    </WishlistContext.Provider>
  );
};

export const useWishlist = () => {
  const context = useContext(WishlistContext);
  if (!context) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};

import { useEffect, useCallback, useRef } from 'react';

interface PerformanceOptions {
  enableThrottling?: boolean;
  throttleDelay?: number;
  enableDebouncing?: boolean;
  debounceDelay?: number;
  enableRAF?: boolean;
}

/**
 * Custom hook for performance optimizations including throttling, debouncing, and RAF
 */
export const usePerformanceOptimization = (options: PerformanceOptions = {}) => {
  const {
    enableThrottling = true,
    throttleDelay = 16, // ~60fps
    enableDebouncing = false,
    debounceDelay = 300,
    enableRAF = true
  } = options;

  const rafId = useRef<number | null>(null);
  const lastExecTime = useRef<number>(0);
  const timeoutId = useRef<NodeJS.Timeout | null>(null);

  // Throttle function for high-frequency events like scroll
  const throttle = useCallback((func: Function, delay: number = throttleDelay) => {
    if (!enableThrottling) return func;

    return (...args: any[]) => {
      const currentTime = Date.now();
      
      if (currentTime - lastExecTime.current > delay) {
        func(...args);
        lastExecTime.current = currentTime;
      } else {
        if (timeoutId.current) clearTimeout(timeoutId.current);
        timeoutId.current = setTimeout(() => {
          func(...args);
          lastExecTime.current = Date.now();
        }, delay - (currentTime - lastExecTime.current));
      }
    };
  }, [enableThrottling, throttleDelay]);

  // Debounce function for events that should only fire after a delay
  const debounce = useCallback((func: Function, delay: number = debounceDelay) => {
    if (!enableDebouncing) return func;

    return (...args: any[]) => {
      if (timeoutId.current) clearTimeout(timeoutId.current);
      timeoutId.current = setTimeout(() => func(...args), delay);
    };
  }, [enableDebouncing, debounceDelay]);

  // RequestAnimationFrame wrapper for smooth animations
  const raf = useCallback((func: Function) => {
    if (!enableRAF) return func();

    if (rafId.current) cancelAnimationFrame(rafId.current);
    rafId.current = requestAnimationFrame(() => func());
  }, [enableRAF]);

  // Cleanup function
  useEffect(() => {
    return () => {
      if (rafId.current) cancelAnimationFrame(rafId.current);
      if (timeoutId.current) clearTimeout(timeoutId.current);
    };
  }, []);

  return {
    throttle,
    debounce,
    raf
  };
};

/**
 * Hook to detect if user prefers reduced motion
 */
export const useReducedMotion = () => {
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
    : false;

  return prefersReducedMotion;
};

/**
 * Hook to detect mobile devices for performance optimizations
 */
export const useMobileDetection = () => {
  const isMobile = typeof window !== 'undefined' 
    ? window.innerWidth < 768
    : false;

  const isTouch = typeof window !== 'undefined'
    ? 'ontouchstart' in window || navigator.maxTouchPoints > 0
    : false;

  return {
    isMobile,
    isTouch,
    isDesktop: !isMobile
  };
};

/**
 * Hook for optimizing scroll performance
 */
export const useScrollOptimization = () => {
  const { throttle } = usePerformanceOptimization({ throttleDelay: 16 });
  const { isMobile } = useMobileDetection();
  const prefersReducedMotion = useReducedMotion();

  const optimizeScrollHandler = useCallback((handler: (event: Event) => void) => {
    if (prefersReducedMotion) {
      // Return a no-op function if user prefers reduced motion
      return () => {};
    }

    const optimizedHandler = throttle(handler, isMobile ? 32 : 16); // Slower on mobile
    return optimizedHandler;
  }, [throttle, isMobile, prefersReducedMotion]);

  return {
    optimizeScrollHandler,
    shouldReduceAnimations: prefersReducedMotion || isMobile
  };
};


import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { BookingService } from '@/services/bookingService';
import { useToast } from '@/hooks/use-toast';

export const useBooking = () => {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(false);
  const { currentUser } = useAuth();
  const { toast } = useToast();

  const createBooking = async (bookingData: any) => {
    if (!currentUser) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to make a booking.",
        variant: "destructive",
      });
      return null;
    }

    setLoading(true);
    try {
      const result = await BookingService.createBooking(
        bookingData,
        currentUser.uid,
        currentUser.email || ''
      );

      toast({
        title: "Booking Created",
        description: "Your booking has been successfully created!",
      });

      return result;
    } catch (error) {
      console.error('Error creating booking:', error);
      toast({
        title: "Booking Failed",
        description: "There was an error creating your booking. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  const getUserBookings = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      const userBookings = await BookingService.getUserBookings(currentUser.uid);
      setBookings(userBookings);
    } catch (error) {
      console.error('Error getting user bookings:', error);
      toast({
        title: "Error",
        description: "Failed to load your bookings.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const cancelBooking = async (bookingId: string, reason: string) => {
    setLoading(true);
    try {
      await BookingService.cancelBooking(bookingId, reason);
      toast({
        title: "Booking Cancelled",
        description: "Your booking has been cancelled successfully.",
      });
      await getUserBookings(); // Refresh bookings
    } catch (error) {
      console.error('Error cancelling booking:', error);
      toast({
        title: "Cancellation Failed",
        description: "There was an error cancelling your booking.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser) {
      getUserBookings();
    }
  }, [currentUser]);

  return {
    bookings,
    loading,
    createBooking,
    getUserBookings,
    cancelBooking
  };
};


import React, { useState, useEffect } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import PageLoader from '@/components/ui/PageLoader';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Star, Shield, Search, ThumbsUp, MessageCircle, Quote, Award, Users, TrendingUp } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Review } from '@/types/firebase';
import { useToast } from '@/hooks/use-toast';

const Reviews = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();

  const [stats, setStats] = useState({
    totalReviews: 0,
    averageRating: 0,
    ratingBreakdown: {
      5: 0,
      4: 0,
      3: 0,
      2: 0,
      1: 0
    }
  });

  useEffect(() => {
    const loadReviews = async () => {
      try {
        setLoading(true);
        const reviewsData = await FirebaseService.getAllReviews();
        const typedReviews = reviewsData as Review[];
        setReviews(typedReviews);
        
        // Calculate stats
        const totalReviews = typedReviews.length;
        const totalRating = typedReviews.reduce((sum, review) => sum + review.rating, 0);
        const averageRating = totalReviews > 0 ? totalRating / totalReviews : 0;
        
        const ratingBreakdown = {
          5: typedReviews.filter(r => r.rating === 5).length,
          4: typedReviews.filter(r => r.rating === 4).length,
          3: typedReviews.filter(r => r.rating === 3).length,
          2: typedReviews.filter(r => r.rating === 2).length,
          1: typedReviews.filter(r => r.rating === 1).length,
        };
        
        setStats({
          totalReviews,
          averageRating: Math.round(averageRating * 10) / 10,
          ratingBreakdown
        });
      } catch (error) {
        console.error('Error loading reviews:', error);
        toast({
          title: "Error loading reviews",
          description: "There was an error loading the reviews.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadReviews();
  }, [toast]);

  const filteredAndSortedReviews = React.useMemo(() => {
    let filtered = reviews;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(review => 
        review.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.userName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.tourName?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply rating filter
    if (filter !== 'all') {
      const rating = parseInt(filter);
      filtered = filtered.filter(review => review.rating === rating);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return (b.createdAt?.toMillis?.() || 0) - (a.createdAt?.toMillis?.() || 0);
        case 'oldest':
          return (a.createdAt?.toMillis?.() || 0) - (b.createdAt?.toMillis?.() || 0);
        case 'highest':
          return b.rating - a.rating;
        case 'lowest':
          return a.rating - b.rating;
        case 'helpful':
          return (b.helpful || 0) - (a.helpful || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [reviews, filter, sortBy, searchTerm]);

  const RatingStars = ({ rating, size = 'w-4 h-4' }: { rating: number; size?: string }) => (
    <div className="flex gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${size} transition-all duration-300 ${
            star <= rating
              ? 'fill-[#D4C2A4] text-[#D4C2A4] drop-shadow-sm'
              : 'text-[#F2EEE6]/20 hover:text-[#D4C2A4]/50'
          }`}
        />
      ))}
    </div>
  );

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Unknown date';
    try {
      return timestamp.toDate().toLocaleDateString();
    } catch {
      return 'Unknown date';
    }
  };

  const handleHelpfulClick = async (reviewId: string) => {
    try {
      // In a real implementation, this would update the helpful count in Firebase
      console.log('Marking review as helpful:', reviewId);
      toast({
        title: "Thank you!",
        description: "Your feedback has been recorded."
      });
    } catch (error) {
      console.error('Error updating helpful count:', error);
    }
  };

  if (loading) {
    return (
      <PageLoader
        title="Loading Reviews..."
        subtitle="Gathering traveler experiences and testimonials..."
      />
    );
  }

  return (
    <div className="min-h-screen bg-[#16191D]">
      <Header />
      <main className="">
        {/* Luxury Hero Section */}
        <div className="relative min-h-[70vh] sm:min-h-[80vh] lg:min-h-screen overflow-hidden bg-cover bg-center bg-no-repeat"
         style={{
              backgroundImage: 'url(https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/image%20(5).png)',
            }}
        >
          {/* Sophisticated Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#16191D]/95 via-[#16191D]/85 to-[#16191D]/90" />
          <div className="absolute inset-0 bg-gradient-to-t from-[#16191D] via-transparent to-transparent" />

          {/* Elegant Content Container */}
          <div className="relative z-10 flex items-center justify-center min-h-[70vh] sm:min-h-[80vh] lg:min-h-screen px-4 pt-16 sm:pt-20">
            <div className="text-center max-w-6xl mx-auto">
              {/* Premium Badge */}
              <div className="mb-6 sm:mb-8 inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 rounded-full bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/20">
                <Award className="w-4 h-4 sm:w-5 sm:h-5 mr-2 sm:mr-3 text-[#D4C2A4]" />
                <span className="text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm font-medium tracking-wider uppercase">
                  Guest Testimonials
                </span>
              </div>

              {/* Luxury Typography */}
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6] mb-6 sm:mb-8 leading-tight tracking-wide">
                What Our
                <span className="block text-[#D4C2A4] font-normal italic">Distinguished</span>
                <span className="block">Guests Say</span>
              </h1>

              {/* Elegant Subtitle */}
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl font-['Open_Sans'] text-[#F2EEE6]/80 leading-relaxed max-w-3xl mx-auto font-light tracking-wide px-4">
                Authentic experiences from discerning travelers who have journeyed with us through Africa's most extraordinary wilderness
              </p>

              {/* Decorative Element */}
              <div className="mt-8 sm:mt-12 flex justify-center">
                <div className="w-16 sm:w-24 h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Luxury Stats Overview */}
        <div className="bg-[#16191D] py-12 sm:py-16 lg:py-24 relative">
          {/* Subtle Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px)`,
              backgroundSize: '50px 50px'
            }}></div>
          </div>

          <div className="container mx-auto px-4 relative z-10">
            {/* Premium Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12 sm:mb-16 lg:mb-20">
              {/* Total Reviews Card */}
              <div className="group">
                <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-2xl p-6 sm:p-8 text-center hover:bg-[#D4C2A4]/10 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-[#D4C2A4]/10">
                  <div className="mb-3 sm:mb-4">
                    <Users className="w-6 h-6 sm:w-8 sm:h-8 text-[#D4C2A4] mx-auto" />
                  </div>
                  <div className="text-3xl sm:text-4xl lg:text-5xl font-['Cormorant_Garamond'] font-light text-[#D4C2A4] mb-2 sm:mb-3">
                    {stats.totalReviews.toLocaleString()}
                  </div>
                  <div className="text-[#F2EEE6]/70 font-['Open_Sans'] text-xs sm:text-sm tracking-wider uppercase">
                    Verified Reviews
                  </div>
                </div>
              </div>

              {/* Average Rating Card */}
              <div className="group">
                <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-2xl p-6 sm:p-8 text-center hover:bg-[#D4C2A4]/10 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-[#D4C2A4]/10">
                  <div className="mb-3 sm:mb-4">
                    <Award className="w-6 h-6 sm:w-8 sm:h-8 text-[#D4C2A4] mx-auto" />
                  </div>
                  <div className="flex items-center justify-center mb-2 sm:mb-3">
                    <span className="text-3xl sm:text-4xl lg:text-5xl font-['Cormorant_Garamond'] font-light text-[#D4C2A4] mr-2 sm:mr-3">
                      {stats.averageRating}
                    </span>
                    <RatingStars rating={Math.round(stats.averageRating)} size="w-5 h-5 sm:w-6 sm:h-6" />
                  </div>
                  <div className="text-[#F2EEE6]/70 font-['Open_Sans'] text-xs sm:text-sm tracking-wider uppercase">
                    Average Rating
                  </div>
                </div>
              </div>

              {/* Recommendation Rate Card */}
              <div className="group sm:col-span-2 lg:col-span-1">
                <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-2xl p-6 sm:p-8 text-center hover:bg-[#D4C2A4]/10 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-[#D4C2A4]/10">
                  <div className="mb-3 sm:mb-4">
                    <TrendingUp className="w-6 h-6 sm:w-8 sm:h-8 text-[#D4C2A4] mx-auto" />
                  </div>
                  <div className="text-3xl sm:text-4xl lg:text-5xl font-['Cormorant_Garamond'] font-light text-[#D4C2A4] mb-2 sm:mb-3">
                    {stats.totalReviews > 0 ? Math.round((stats.ratingBreakdown[4] + stats.ratingBreakdown[5]) / stats.totalReviews * 100) : 0}%
                  </div>
                  <div className="text-[#F2EEE6]/70 font-['Open_Sans'] text-xs sm:text-sm tracking-wider uppercase">
                    Would Recommend
                  </div>
                </div>
              </div>
            </div>

            {/* Elegant Rating Breakdown */}
            <div className="max-w-3xl mx-auto">
              <h3 className="text-2xl sm:text-3xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6] mb-8 sm:mb-12 text-center">
                Rating Distribution
              </h3>
              <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-2xl p-6 sm:p-8 space-y-4 sm:space-y-6">
                {[5, 4, 3, 2, 1].map(rating => (
                  <div key={rating} className="flex items-center group">
                    <span className="w-6 sm:w-8 text-[#F2EEE6] font-['Open_Sans'] font-medium text-sm sm:text-base">{rating}</span>
                    <Star className="w-4 h-4 sm:w-5 sm:h-5 text-[#D4C2A4] mr-3 sm:mr-4 fill-current" />
                    <div className="flex-1 bg-[#F2EEE6]/10 rounded-full h-2 sm:h-3 mr-4 sm:mr-6 overflow-hidden">
                      <div
                        className="bg-gradient-to-r from-[#D4C2A4] to-[#D4C2A4]/80 h-2 sm:h-3 rounded-full transition-all duration-1000 ease-out"
                        style={{
                          width: stats.totalReviews > 0
                            ? `${(stats.ratingBreakdown[rating as keyof typeof stats.ratingBreakdown] / stats.totalReviews) * 100}%`
                            : '0%'
                        }}
                      />
                    </div>
                    <span className="w-12 sm:w-16 text-[#F2EEE6]/70 font-['Open_Sans'] text-xs sm:text-sm text-right">
                      {stats.ratingBreakdown[rating as keyof typeof stats.ratingBreakdown]}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Luxury Filters and Reviews Section */}
        <div className="bg-[#16191D] py-12 sm:py-16 lg:py-20">
          <div className="container mx-auto px-4">
            {/* Elegant Section Header */}
            <div className="text-center mb-12 sm:mb-16">
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6] mb-4">
                Guest Experiences
              </h2>
              <div className="w-16 sm:w-24 h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent mx-auto"></div>
            </div>

            {/* Premium Search and Filters */}
            <div className="mb-8 sm:mb-12 flex flex-col gap-4 sm:gap-6 max-w-4xl mx-auto">
              {/* Luxury Search Bar */}
              <div className="relative">
                <Search className="absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-[#D4C2A4]/60 w-4 h-4 sm:w-5 sm:h-5" />
                <Input
                  placeholder="Search testimonials..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 sm:pl-12 pr-4 py-3 sm:py-4 bg-[#D4C2A4]/5 border border-[#D4C2A4]/20 rounded-xl text-[#F2EEE6] placeholder:text-[#F2EEE6]/50 focus:bg-[#D4C2A4]/10 focus:border-[#D4C2A4]/40 transition-all duration-300 font-['Open_Sans'] backdrop-blur-md text-sm sm:text-base"
                />
              </div>

              {/* Premium Filter Selects */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Select value={filter} onValueChange={setFilter}>
                  <SelectTrigger className="py-3 sm:py-4 bg-[#D4C2A4]/5 border border-[#D4C2A4]/20 rounded-xl text-[#F2EEE6] focus:bg-[#D4C2A4]/10 focus:border-[#D4C2A4]/40 transition-all duration-300 font-['Open_Sans'] backdrop-blur-md text-sm sm:text-base">
                    <SelectValue placeholder="Filter by rating" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#16191D]/95 border border-[#D4C2A4]/20 backdrop-blur-md">
                    <SelectItem value="all" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">All Ratings</SelectItem>
                    <SelectItem value="5" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">5 Stars</SelectItem>
                    <SelectItem value="4" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">4 Stars</SelectItem>
                    <SelectItem value="3" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">3 Stars</SelectItem>
                    <SelectItem value="2" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">2 Stars</SelectItem>
                    <SelectItem value="1" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">1 Star</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="py-3 sm:py-4 bg-[#D4C2A4]/5 border border-[#D4C2A4]/20 rounded-xl text-[#F2EEE6] focus:bg-[#D4C2A4]/10 focus:border-[#D4C2A4]/40 transition-all duration-300 font-['Open_Sans'] backdrop-blur-md text-sm sm:text-base">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#16191D]/95 border border-[#D4C2A4]/20 backdrop-blur-md">
                    <SelectItem value="newest" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">Newest First</SelectItem>
                    <SelectItem value="oldest" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">Oldest First</SelectItem>
                    <SelectItem value="highest" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">Highest Rating</SelectItem>
                    <SelectItem value="lowest" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">Lowest Rating</SelectItem>
                    <SelectItem value="helpful" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10 focus:bg-[#D4C2A4]/10">Most Helpful</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Luxury Reviews List */}
            <div className="space-y-6 sm:space-y-8 max-w-5xl mx-auto">
              {filteredAndSortedReviews.length === 0 ? (
                <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-2xl p-8 sm:p-12 lg:p-16 text-center">
                  <div className="text-[#D4C2A4]/60 mb-4 sm:mb-6">
                    <Star className="w-12 h-12 sm:w-16 sm:h-16 mx-auto" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6] mb-3 sm:mb-4">
                    No testimonials found
                  </h3>
                  <p className="text-[#F2EEE6]/70 font-['Open_Sans'] text-sm sm:text-base">
                    {searchTerm || filter !== 'all'
                      ? 'Try adjusting your search criteria or filters.'
                      : 'Be the first to share your extraordinary safari experience!'}
                  </p>
                </div>
              ) : (
                filteredAndSortedReviews.map((review) => (
                  <div key={review.id} className="group">
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-2xl p-6 sm:p-8 hover:bg-[#D4C2A4]/8 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-[#D4C2A4]/10">
                      {/* Review Header */}
                      <div className="flex items-start justify-between mb-4 sm:mb-6">
                        <div className="flex items-start gap-3 sm:gap-6 flex-1">
                          <Avatar className="h-12 w-12 sm:h-16 sm:w-16 ring-2 ring-[#D4C2A4]/30 flex-shrink-0">
                            {review.userAvatar ? (
                              <img src={review.userAvatar} alt={review.userName || 'User'} className="object-cover" />
                            ) : (
                              <div className="w-full h-full bg-[#D4C2A4]/20 flex items-center justify-center text-[#D4C2A4] font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium">
                                {(review.userName || 'U').charAt(0).toUpperCase()}
                              </div>
                            )}
                          </Avatar>
                          <div className="min-w-0 flex-1">
                            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                              <h4 className="font-['Cormorant_Garamond'] text-lg sm:text-xl font-medium text-[#F2EEE6] truncate">
                                {review.userName || 'Distinguished Guest'}
                              </h4>
                              {review.verified && (
                                <div className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full bg-[#D4C2A4]/10 border border-[#D4C2A4]/30 self-start">
                                  <Shield className="w-3 h-3 mr-1 text-[#D4C2A4]" />
                                  <span className="text-[#D4C2A4] font-['Open_Sans'] text-xs font-medium tracking-wider uppercase">
                                    Verified
                                  </span>
                                </div>
                              )}
                            </div>
                            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 text-xs sm:text-sm text-[#F2EEE6]/70">
                              <RatingStars rating={review.rating} size="w-3 h-3 sm:w-4 sm:h-4" />
                              <span className="font-['Open_Sans']">{formatDate(review.createdAt)}</span>
                              <span className="hidden sm:inline">•</span>
                              <span className="font-['Open_Sans'] italic truncate">{review.tourName || 'Safari Experience'}</span>
                            </div>
                          </div>
                        </div>

                        {/* Quote Icon */}
                        <Quote className="w-6 h-6 sm:w-8 sm:h-8 text-[#D4C2A4]/30 transform rotate-180 flex-shrink-0 ml-2" />
                      </div>

                      {/* Review Content */}
                      <div className="mb-4 sm:mb-6">
                        <h3 className="font-['Cormorant_Garamond'] text-xl sm:text-2xl font-medium text-[#F2EEE6] mb-3 sm:mb-4 leading-relaxed">
                          {review.title}
                        </h3>
                        <p className="text-[#F2EEE6]/80 font-['Open_Sans'] leading-relaxed text-sm sm:text-base lg:text-lg">
                          {review.content}
                        </p>
                      </div>

                      {/* Review Images */}
                      {review.images && review.images.length > 0 && (
                        <div className="flex gap-2 sm:gap-3 mb-4 sm:mb-6 overflow-x-auto pb-2">
                          {review.images.map((image, index) => (
                            <img
                              key={index}
                              src={image}
                              alt={`Experience ${index + 1}`}
                              className="w-24 h-24 sm:w-32 sm:h-32 object-cover rounded-xl border border-[#D4C2A4]/20 hover:scale-105 transition-transform duration-300 flex-shrink-0"
                            />
                          ))}
                        </div>
                      )}

                      {/* Review Actions */}
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-6 pt-4 sm:pt-6 border-t border-[#D4C2A4]/20">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleHelpfulClick(review.id)}
                          className="text-[#F2EEE6]/70 hover:text-[#D4C2A4] hover:bg-[#D4C2A4]/10 font-['Open_Sans'] transition-all duration-300 text-xs sm:text-sm"
                        >
                          <ThumbsUp className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                          Helpful ({review.helpful || 0})
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-[#F2EEE6]/70 hover:text-[#D4C2A4] hover:bg-[#D4C2A4]/10 font-['Open_Sans'] transition-all duration-300 text-xs sm:text-sm"
                        >
                          <MessageCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                          Reply
                        </Button>
                      </div>

                      {/* Management Response */}
                      {review.response && (
                        <div className="mt-4 sm:mt-6 p-4 sm:p-6 bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/30 rounded-xl">
                          <div className="font-['Cormorant_Garamond'] text-base sm:text-lg font-medium text-[#D4C2A4] mb-2">
                            {review.response.author}
                          </div>
                          <p className="text-[#F2EEE6]/80 font-['Open_Sans'] leading-relaxed mb-3 text-sm sm:text-base">
                            {review.response.content}
                          </p>
                          <div className="text-xs text-[#F2EEE6]/50 font-['Open_Sans']">
                            {review.response.date}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Elegant Load More Section */}
            {filteredAndSortedReviews.length > 0 && (
              <div className="text-center mt-12 sm:mt-16">
                <Button
                  variant="outline"
                  className="px-8 sm:px-12 py-3 sm:py-4 bg-[#D4C2A4]/5 border border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/50 font-['Open_Sans'] font-medium tracking-wider uppercase transition-all duration-300 rounded-xl backdrop-blur-md text-sm sm:text-base"
                >
                  Discover More Stories
                </Button>
              </div>
            )}
          </div>
        </div>
      </main>
      <Footer isDarkBackground={true} />
    </div>
  );
};

export default Reviews;


import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Calendar, TrendingUp } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface WildlifeSighting {
  animal: string;
  location: string;
  probability: number;
  lastSeen: string;
  trend: 'up' | 'down' | 'stable';
}

const WildlifeTracker = () => {
  const { t } = useLanguage();
  const [sightings, setSightings] = useState<WildlifeSighting[]>([]);

  useEffect(() => {
    // Simulate real-time wildlife data
    const mockSightings: WildlifeSighting[] = [
      { animal: 'Lions', location: 'Serengeti Central', probability: 85, lastSeen: '2 hours ago', trend: 'up' },
      { animal: 'Elephants', location: 'Tarangire', probability: 92, lastSeen: '30 minutes ago', trend: 'stable' },
      { animal: 'Leopards', location: 'Ngorongoro', probability: 45, lastSeen: '1 day ago', trend: 'down' },
      { animal: 'Rhinos', location: 'Ngorongoro Crater', probability: 68, lastSeen: '4 hours ago', trend: 'up' },
      { animal: 'Cheetahs', location: 'Serengeti Plains', probability: 72, lastSeen: '6 hours ago', trend: 'stable' },
    ];
    setSightings(mockSightings);
  }, []);

  const getProbabilityColor = (probability: number) => {
    if (probability >= 80) return 'bg-green-600';
    if (probability >= 60) return 'bg-yellow-600';
    return 'bg-red-600';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return '📈';
      case 'down': return '📉';
      default: return '➡️';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
          {t('wildlife.spotted')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sightings.map((sighting, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h4 className="font-semibold">{sighting.animal}</h4>
                  <span className="text-lg">{getTrendIcon(sighting.trend)}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600 space-x-3">
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 mr-1" />
                    {sighting.location}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    {sighting.lastSeen}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <Badge className={`${getProbabilityColor(sighting.probability)} text-white`}>
                  {sighting.probability}%
                </Badge>
                <p className="text-xs text-gray-500 mt-1">Probability</p>
              </div>
            </div>
          ))}
        </div>
        <Button className="w-full mt-4" variant="outline">
          View Full Wildlife Map
        </Button>
      </CardContent>
    </Card>
  );
};

export default WildlifeTracker;

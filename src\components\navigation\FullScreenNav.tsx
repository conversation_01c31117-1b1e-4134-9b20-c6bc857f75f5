import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { IoClose } from 'react-icons/io5';
import { FiPhone, FiFacebook, FiInstagram, FiLogIn, FiUser } from 'react-icons/fi';
import { FaWhatsapp } from 'react-icons/fa';
import { useAuth } from '@/contexts/AuthContext';

interface FullScreenNavProps {
  isOpen: boolean;
  onClose: () => void;
}

const FullScreenNav: React.FC<FullScreenNavProps> = ({ isOpen, onClose }) => {
  const { currentUser, userProfile, logout } = useAuth();

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);
  return (
    <div
      className={`fixed inset-0 w-screen h-screen bg-[#16191D] z-[1000] flex transition-all duration-500 ease-in-out ${
        isOpen
          ? 'opacity-100 visible'
          : 'opacity-0 invisible'
      }`}
    >
      {/* Close Icon - Top Left */}
      <button
        onClick={onClose}
        className="absolute top-6 left-6 md:top-10 md:left-10 text-white text-3xl md:text-5xl hover:text-[#D4C2A4] transition-colors duration-300 cursor-pointer z-10"
        aria-label="Close menu"
      >
        <IoClose />
      </button>

      {/* Main Navigation Menu - Left Side */}
      <div className="flex-1 flex flex-col justify-center pl-6 md:pl-20 lg:pl-32 pr-6 md:pr-0">
        {/* Menu Title */}
        <div className="mb-6 md:mb-8 lg:mb-12">
          <h2 className="text-[#A9A9A9] font-['Open Sans'] text-xs md:text-sm lg:text-base tracking-[0.2em] uppercase">
            MENU
          </h2>
        </div>

        {/* Navigation Links */}
        <nav className="space-y-2 md:space-y-4 lg:space-y-6">
          <Link
            to="/"
            onClick={onClose}
            className="block text-[#F2EEE6] opacity-40 hover:text-[#D4C2A4] hover:opacity-100 font-['Cormorant_Garamond'] font-regular text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl transition-all duration-300 cursor-pointer leading-tight hover:translate-x-2 transform"
          >
            HOME
          </Link>
          <Link
            to="/tours"
            onClick={onClose}
            className="block text-[#F2EEE6] opacity-40 hover:text-[#D4C2A4] hover:opacity-100 font-['Cormorant_Garamond'] font-regular text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl transition-all duration-300 cursor-pointer leading-tight hover:translate-x-2 transform"
          >
            SAFARIS
          </Link>
          <Link
            to="/gallery"
            onClick={onClose}
            className="block text-[#F2EEE6] opacity-40 hover:text-[#D4C2A4] hover:opacity-100 font-['Cormorant_Garamond'] font-regular text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl transition-all duration-300 cursor-pointer leading-tight hover:translate-x-2 transform"
          >
            GALLERY
          </Link>
          <Link
            to="/about"
            onClick={onClose}
            className="block text-[#F2EEE6] opacity-40 hover:text-[#D4C2A4] hover:opacity-100 font-['Cormorant_Garamond'] font-regular text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl transition-all duration-300 cursor-pointer leading-tight hover:translate-x-2 transform"
          >
            ABOUT US
          </Link>
          <Link
            to="/contact"
            onClick={onClose}
            className="block text-[#F2EEE6] opacity-40 hover:text-[#D4C2A4] hover:opacity-100 font-['Cormorant_Garamond'] font-regular text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl transition-all duration-300 cursor-pointer leading-tight hover:translate-x-2 transform"
          >
            CALL EXPERT
          </Link>
        </nav>
      </div>

      {/* Central Background Logo */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-0">
        <img
          src="/photos/menuLogo.svg"
          alt="Warriors of Africa Safari Logo"
          className="opacity-100 w-48 h-48 sm:w-56 sm:h-56 md:w-72 md:h-72 lg:w-80 lg:h-80 xl:w-96 xl:h-96 object-contain"
        />
      </div>

      {/* Social & Contact Icons - Top Right */}
      <div className="absolute top-6 right-6 md:top-10 md:right-10 flex flex-col space-y-4 md:space-y-6">
        <a
          href="tel:+25566121379"
          className="text-[#A9A9A9] hover:text-[#D4C2A4] opacity-30 hover:opacity-100 transition-all duration-300"
          aria-label="Phone"
        >
          <FiPhone size={20} />
        </a>
        <a
          href="https://facebook.com"
          target="_blank"
          rel="noopener noreferrer"
          className="text-[#A9A9A9] hover:text-[#D4C2A4] opacity-30 hover:opacity-100 transition-all duration-300"
          aria-label="Facebook"
        >
          <FiFacebook size={20} />
        </a>
        <a
          href="https://instagram.com/warriors_ofafricasafari"
          target="_blank"
          rel="noopener noreferrer"
          className="text-[#A9A9A9] hover:text-[#D4C2A4] opacity-30 hover:opacity-100 transition-all duration-300"
          aria-label="Instagram"
        >
          <FiInstagram size={20} />
        </a>
        <a
          href="https://wa.me/255769042314"
          target="_blank"
          rel="noopener noreferrer"
          className="text-[#A9A9A9] hover:text-[#D4C2A4] opacity-30 hover:opacity-100 transition-all duration-300"
          aria-label="WhatsApp"
        >
          <FaWhatsapp size={20} />
        </a>

        {/* Login/User Icon with Dropdown */}
        {currentUser ? (
          <div className="relative group">
            <div className="text-[#A9A9A9] hover:text-[#D4C2A4] opacity-30 hover:opacity-100 transition-all duration-300 cursor-pointer">
              <FiUser size={20} />
            </div>

            {/* Stunning Dropdown Menu */}
            <div className="absolute right-0 top-full mt-2 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-50">
              <div className="bg-[#16191D]/95 backdrop-blur-md border border-[#A9A9A9]/20 rounded-sm shadow-2xl overflow-hidden">
                {/* Glass effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none"></div>

                <div className="relative">
                  {/* Dashboard Link */}
                  <Link
                    to="/user-dashboard"
                    onClick={onClose}
                    className="block px-4 py-3 text-[#F2EEE6] hover:text-[#D4C2A4] hover:bg-[#D4C2A4]/10 transition-all duration-300 font-['Open Sans'] text-sm tracking-wide border-b border-[#A9A9A9]/10"
                  >
                    Dashboard
                  </Link>

                  {/* Admin Link (only for admins) */}
                  {userProfile?.role === 'admin' && (
                    <Link
                      to="/admin"
                      onClick={onClose}
                      className="block px-4 py-3 text-[#F2EEE6] hover:text-[#D4C2A4] hover:bg-[#D4C2A4]/10 transition-all duration-300 font-['Open Sans'] text-sm tracking-wide border-b border-[#A9A9A9]/10"
                    >
                      Admin
                    </Link>
                  )}

                  {/* Logout Button */}
                  <button
                    onClick={async () => {
                      await logout();
                      onClose();
                    }}
                    className="block w-full text-left px-4 py-3 text-[#F2EEE6] hover:text-[#D4C2A4] hover:bg-[#D4C2A4]/10 transition-all duration-300 font-['Open Sans'] text-sm tracking-wide"
                  >
                    Logout
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <Link
            to="/login"
            onClick={onClose}
            className="text-[#A9A9A9] hover:text-[#D4C2A4] opacity-30 hover:opacity-100 transition-all duration-300"
            aria-label="Login"
          >
            <FiLogIn size={20} />
          </Link>
        )}
      </div>

      {/* Contact Information - Bottom Right */}
      <div className="absolute bottom-6 right-6 md:bottom-10 md:right-10 text-right">
        <div className="text-[#A9A9A9] font-['Open Sans'] font-regular space-y-1 text-xs md:text-sm lg:text-base">
          <div>Arusha, Tanzania</div>
          <div>+25566121379</div>
          <div className="break-all md:break-normal"><EMAIL></div>
        </div>
      </div>
    </div>
  );
};

export default FullScreenNav;

// Dynamic import for web-vitals to make it optional
let webVitalsModule: any = null;

async function loadWebVitals() {
  if (!webVitalsModule) {
    try {
      webVitalsModule = await import('web-vitals');
    } catch (error) {
      console.warn('web-vitals package not found. Install it with: npm install web-vitals');
      return null;
    }
  }
  return webVitalsModule;
}

// Types for Web Vitals metrics
interface WebVitalsMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
  navigationType: string;
}

// Thresholds for Core Web Vitals (based on Google's recommendations)
const THRESHOLDS = {
  LCP: { good: 2500, poor: 4000 },
  INP: { good: 200, poor: 500 }, // INP replaces FID
  CLS: { good: 0.1, poor: 0.25 },
  FCP: { good: 1800, poor: 3000 },
  TTFB: { good: 800, poor: 1800 }
};

// Get rating based on thresholds
function getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const threshold = THRESHOLDS[name as keyof typeof THRESHOLDS];
  if (!threshold) return 'good';
  
  if (value <= threshold.good) return 'good';
  if (value <= threshold.poor) return 'needs-improvement';
  return 'poor';
}

// Send metrics to analytics (replace with your analytics service)
function sendToAnalytics(metric: WebVitalsMetric) {
  // Example: Send to Google Analytics 4
  if (typeof gtag !== 'undefined') {
    gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      custom_map: {
        metric_rating: metric.rating,
        metric_delta: metric.delta,
        navigation_type: metric.navigationType
      }
    });
  }

  // Example: Send to custom analytics endpoint
  if (process.env.NODE_ENV === 'production') {
    fetch('/api/analytics/web-vitals', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        metric: metric.name,
        value: metric.value,
        rating: metric.rating,
        delta: metric.delta,
        id: metric.id,
        navigationType: metric.navigationType,
        url: window.location.href,
        timestamp: Date.now(),
        userAgent: navigator.userAgent
      }),
    }).catch(console.error);
  }

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Web Vitals:', {
      name: metric.name,
      value: metric.value,
      rating: metric.rating,
      delta: metric.delta
    });
  }
}

// Initialize Web Vitals monitoring
export async function initWebVitals() {
  const webVitals = await loadWebVitals();
  if (!webVitals) {
    // Fallback: Use basic performance monitoring
    initBasicPerformanceMonitoring();
    return;
  }

  const { onLCP, onINP, onCLS, onFCP, onTTFB } = webVitals;

  // Core Web Vitals
  onLCP((metric: any) => {
    sendToAnalytics({
      ...metric,
      rating: getRating('LCP', metric.value)
    });
  });

  // Use onINP instead of deprecated onFID
  if (onINP) {
    onINP((metric: any) => {
      sendToAnalytics({
        ...metric,
        rating: getRating('INP', metric.value)
      });
    });
  }

  onCLS((metric: any) => {
    sendToAnalytics({
      ...metric,
      rating: getRating('CLS', metric.value)
    });
  });

  // Additional metrics
  onFCP((metric: any) => {
    sendToAnalytics({
      ...metric,
      rating: getRating('FCP', metric.value)
    });
  });

  onTTFB((metric: any) => {
    sendToAnalytics({
      ...metric,
      rating: getRating('TTFB', metric.value)
    });
  });
}

// Get current Web Vitals metrics (for dashboard/debugging)
export async function getCurrentWebVitals() {
  const metrics = {
    LCP: null as number | null,
    FID: null as number | null,
    CLS: null as number | null,
    FCP: null as number | null,
    TTFB: null as number | null
  };

  const webVitals = await loadWebVitals();
  if (!webVitals) return metrics;

  const { getLCP, getFID, getCLS, getFCP, getTTFB } = webVitals;

  try {
    await Promise.all([
      getLCP((metric: any) => { metrics.LCP = metric.value; }),
      getFID((metric: any) => { metrics.FID = metric.value; }),
      getCLS((metric: any) => { metrics.CLS = metric.value; }),
      getFCP((metric: any) => { metrics.FCP = metric.value; }),
      getTTFB((metric: any) => { metrics.TTFB = metric.value; })
    ]);
  } catch (error) {
    console.error('Error getting Web Vitals:', error);
  }

  return metrics;
}

// Performance observer for additional metrics
export function initPerformanceObserver() {
  if ('PerformanceObserver' in window) {
    // Monitor long tasks (> 50ms)
    try {
      const longTaskObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 50) {
            console.warn('Long task detected:', {
              duration: entry.duration,
              startTime: entry.startTime,
              name: entry.name
            });
            
            // Send to analytics if needed
            if (process.env.NODE_ENV === 'production') {
              sendToAnalytics({
                name: 'long-task',
                value: entry.duration,
                rating: entry.duration > 100 ? 'poor' : 'needs-improvement',
                delta: 0,
                id: `long-task-${Date.now()}`,
                navigationType: 'navigate'
              });
            }
          }
        });
      });
      
      longTaskObserver.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      console.warn('Long task observer not supported');
    }

    // Monitor layout shifts
    try {
      const layoutShiftObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry: any) => {
          if (entry.hadRecentInput) return; // Ignore user-initiated shifts
          
          if (entry.value > 0.1) {
            console.warn('Layout shift detected:', {
              value: entry.value,
              startTime: entry.startTime,
              sources: entry.sources
            });
          }
        });
      });
      
      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (error) {
      console.warn('Layout shift observer not supported');
    }
  }
}

// Performance monitoring hook for React components
export function usePerformanceMonitoring(componentName: string) {
  const startTime = performance.now();
  
  return {
    markComplete: () => {
      const duration = performance.now() - startTime;
      if (duration > 16) { // More than one frame (60fps)
        console.log(`Component ${componentName} render took ${duration.toFixed(2)}ms`);
      }
    }
  };
}

// Basic performance monitoring fallback when web-vitals is not available
function initBasicPerformanceMonitoring() {
  if (typeof window === 'undefined' || !('performance' in window)) return;

  // Monitor page load time
  const handleLoad = () => {
    const loadTime = performance.now();
    if (process.env.NODE_ENV === 'development') {
      console.log(`Page load time: ${loadTime.toFixed(2)}ms`);
    }

    // Send basic metrics
    sendToAnalytics({
      name: 'page-load',
      value: loadTime,
      rating: loadTime < 2000 ? 'good' : loadTime < 4000 ? 'needs-improvement' : 'poor',
      delta: 0,
      id: `page-load-${Date.now()}`,
      navigationType: 'navigate'
    });
  };
  
  window.addEventListener('load', handleLoad, { once: true });

  // Monitor navigation timing
  if ('getEntriesByType' in performance) {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          const ttfb = navEntry.responseStart - navEntry.requestStart;

          sendToAnalytics({
            name: 'TTFB',
            value: ttfb,
            rating: getRating('TTFB', ttfb),
            delta: 0,
            id: `ttfb-${Date.now()}`,
            navigationType: 'navigate'
          });
        }
      });
    });

    try {
      observer.observe({ entryTypes: ['navigation'] });
    } catch (error) {
      console.warn('Navigation timing observer not supported');
    }
  }
}

declare global {
  function gtag(...args: any[]): void;
}


import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  MessageCircle,
  Send,
  X,
  Minimize2,
  Maximize2,
  Bot,
  User,
  MapPin,
  Calendar,
  Plane,
  Star,
  Phone,
  Mail,
  Clock,
  Info
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { geminiService } from '@/services/gemini';
import { useIsMobile } from '@/hooks/use-mobile';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  suggestions?: string[];
  actions?: Array<{
    label: string;
    action: string;
    data?: any;
  }>;
}

interface ChatBotProps {
  isOpen: boolean;
  onToggle: () => void;
}

const ChatBot: React.FC<ChatBotProps> = ({ isOpen, onToggle }) => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [conversationHistory, setConversationHistory] = useState<Array<{role: string, content: string}>>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: '1',
        text: "🦁 Hello! I'm your AI-powered Warrior Safari guide! I have extensive knowledge about Tanzania's wildlife, destinations, tours, and can help you plan your perfect safari adventure. Ask me anything about our safaris, wildlife, destinations, or travel tips!",
        sender: 'bot',
        timestamp: new Date(),
        suggestions: [
          "Show me popular tours",
          "Best time to visit Tanzania",
          "Wildlife in Serengeti",
          "Safari pricing",
          "Cultural experiences"
        ]
      };
      setMessages([welcomeMessage]);
    }
  }, [isOpen, messages.length]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputText;
    setInputText('');
    setIsLoading(true);

    try {
      // Add user message to conversation history
      const newHistory = [...conversationHistory, { role: 'user', content: currentInput }];

      const response = await geminiService.generateResponse(currentInput, conversationHistory);
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response.text,
        sender: 'bot',
        timestamp: new Date(),
        suggestions: response.suggestions,
        actions: response.actions
      };

      setMessages(prev => [...prev, botMessage]);

      // Update conversation history with bot response
      setConversationHistory([...newHistory, { role: 'assistant', content: response.text }]);
    } catch (error) {
      console.error('Error processing message:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "I apologize, but I'm having trouble processing your request right now. Please try again or contact our support <NAME_EMAIL>.",
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };



  const handleSuggestionClick = (suggestion: string) => {
    setInputText(suggestion);
  };

  const handleActionClick = (action: { label: string; action: string; data?: any }) => {
    switch (action.action) {
      case 'navigate':
        navigate(action.data);
        onToggle();
        break;
      case 'email':
        window.location.href = `mailto:${action.data}`;
        break;
      case 'search':
        navigate(`/tours?search=${action.data}`);
        onToggle();
        break;
      default:
        // Action not implemented
    }
  };

  if (!isOpen) {
    return null; // Don't render anything when closed - ChatBotWidget handles the floating button
  }

  return (
    <Card className={`fixed z-[9999] shadow-2xl transition-all duration-300 bg-white border border-gray-200 flex flex-col
      ${/* Mobile: Responsive width and centering */ ''}
      ${isMobile
        ? 'w-[calc(100vw-1rem)] max-w-[20rem] left-1/2 transform -translate-x-1/2'
        : 'w-80 right-4 lg:right-6'
      }
      ${/* Mobile: Better bottom positioning */ ''}
      ${isMobile
        ? 'bottom-20 sm:bottom-16'
        : 'bottom-4 lg:bottom-6'
      }
      ${/* Height adjustments for better experience */ ''}
      ${isMinimized
        ? 'h-12 sm:h-14'
        : isMobile
          ? 'h-[70vh] max-h-[28rem]'
          : 'h-[32rem] max-h-[32rem]'
      }
    `}>
      <CardHeader className="p-3 bg-gradient-to-r from-deep-brown to-medium-brown text-warm-cream rounded-t-lg flex-shrink-0">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            <Bot className="h-4 w-4 flex-shrink-0" />
            <span className="text-sm font-medium truncate">
              Warrior Safari Assistant
            </span>
          </div>
          <div className="flex items-center space-x-1 flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-8 w-8 p-0 text-white hover:bg-white/20 touch-target flex items-center justify-center rounded"
            >
              {isMinimized ?
                <Maximize2 className="h-4 w-4" /> :
                <Minimize2 className="h-4 w-4" />
              }
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggle}
              className="h-8 w-8 p-0 text-white hover:bg-white/20 touch-target flex items-center justify-center rounded"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      {!isMinimized && (
        <CardContent className="p-0 flex flex-col flex-1 overflow-hidden chatbot-content-container">
          <div className="flex-1 overflow-hidden chatbot-messages-area">
            <ScrollArea className="h-full chatbot-scroll-area">
              <div className="p-3 space-y-3">
              {messages.map((message) => (
                <div key={message.id} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-[85%] rounded-lg p-3 chatbot-message chatbot-message-container ${
                    message.sender === 'user'
                      ? 'bg-deep-rust text-dark-brown'
                      : 'bg-light-warm-gray text-dark-brown'
                  }`}>
                    <div className="flex items-start space-x-2">
                      {message.sender === 'bot' && <Bot className="h-4 w-4 mt-0.5 flex-shrink-0" />}
                      {message.sender === 'user' && <User className="h-4 w-4 mt-0.5 flex-shrink-0" />}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm whitespace-pre-line break-words leading-relaxed">
                          {message.text}
                        </p>

                        {message.actions && (
                          <div className="mt-2 space-y-1">
                            {message.actions.map((action, index) => (
                              <Button
                                key={index}
                                variant="outline"
                                size="sm"
                                onClick={() => handleActionClick(action)}
                                className="mr-1 mb-1 text-xs h-9 sm:h-8 md:h-7 px-3 sm:px-2 md:px-3 touch-target chatbot-button"
                              >
                                {action.label}
                              </Button>
                            ))}
                          </div>
                        )}

                        {message.suggestions && (
                          <div className="mt-2 flex flex-wrap gap-1 sm:gap-1">
                            {message.suggestions.map((suggestion, index) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="cursor-pointer text-xs hover:bg-gray-200 px-2 py-1.5 sm:px-2 sm:py-1 touch-target min-h-[32px] sm:min-h-[28px] flex items-center"
                                onClick={() => handleSuggestionClick(suggestion)}
                              >
                                {suggestion}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 rounded-lg p-2 sm:p-2 md:p-3 max-w-[90%] sm:max-w-[85%] md:max-w-[80%]">
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <Bot className="h-3 w-3 sm:h-4 sm:w-4" />
                      <div className="flex space-x-1">
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              </div>
              <div ref={messagesEndRef} />
            </ScrollArea>
          </div>

          <div className="p-3 border-t bg-white flex-shrink-0">
            <div className="flex space-x-2">
              <Input
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Ask me anything about safaris..."
                className="flex-1 text-sm h-10 no-zoom rounded-lg border-gray-300 focus:border-deep-rust focus:ring-deep-rust"
                disabled={isLoading}
              />
              <Button
                onClick={handleSendMessage}
                disabled={isLoading || !inputText.trim()}
                size="sm"
                className="bg-deep-rust hover:bg-golden-orange text-dark-brown h-10 w-10 touch-target flex items-center justify-center rounded-lg flex-shrink-0"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default ChatBot;

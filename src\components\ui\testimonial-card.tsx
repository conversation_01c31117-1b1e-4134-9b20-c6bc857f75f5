import { cn } from "@/lib/utils"
import { Avatar, AvatarImage } from "@/components/ui/avatar"

export interface TestimonialAuthor {
  name: string
  handle: string
  avatar: string
}

export interface TestimonialCardProps {
  author: TestimonialAuthor
  text: string
  href?: string
  className?: string
}

export function TestimonialCard({ 
  author,
  text,
  href,
  className
}: TestimonialCardProps) {
  const Card = href ? 'a' : 'div'
  
  return (
    <Card
      {...(href ? { href, target: "_blank", rel: "noopener noreferrer" } : {})}
      className={cn(
        "flex flex-col rounded-lg border-t",
        "bg-gradient-to-b from-muted/50 to-muted/10",
        "p-4 text-start sm:p-6",
        "hover:from-muted/60 hover:to-muted/20",
        "min-h-[200px] h-auto", // Ensure consistent minimum height
        "transition-colors duration-300",
        "overflow-hidden", // Prevent content overflow
        className
      )}
    >
      <div className="flex items-center gap-3 mb-4">
        <Avatar className="h-12 w-12 flex-shrink-0">
          <AvatarImage src={author.avatar} alt={author.name} />
        </Avatar>
        <div className="flex flex-col items-start min-w-0 flex-1">
          <h3 className="text-md font-semibold leading-tight truncate w-full">
            {author.name}
          </h3>
          <p className="text-sm text-muted-foreground truncate w-full">
            {author.handle}
          </p>
        </div>
      </div>
      <p className="text-sm sm:text-base text-muted-foreground leading-relaxed flex-1">
        {text}
      </p>
    </Card>
  )
}
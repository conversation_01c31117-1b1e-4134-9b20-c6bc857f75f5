
import React, { useState } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import PageLoader from '@/components/ui/PageLoader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Calendar, Clock, Search, User, BookOpen } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useBlogPosts } from '@/hooks/useBlog';

const Blog = () => {
  const { posts, loading, error } = useBlogPosts();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  // Filter posts based on search and category
  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Get unique categories
  const categories = Array.from(new Set(posts.map(post => post.category)));

  if (loading) {
    return (
      <PageLoader
        title="Curating Safari Chronicles..."
        subtitle="Unveiling extraordinary tales from the African wilderness"
      />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#16191D]">
        <Header />
        <main className="">
          <div className="relative overflow-hidden bg-[#16191D] pt-16 sm:pt-20 py-12 sm:py-16 md:py-20 lg:py-24">
            {/* Elegant Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                                 radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
                backgroundSize: '40px 40px'
              }} />
            </div>
            <div className="relative z-10 container mx-auto px-4 text-center">
              <h1 className="font-['Cormorant_Garamond'] text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-semibold mb-4 sm:mb-6 tracking-wide text-[#F2EEE6]">
                Stories Unavailable
              </h1>
              <p className="font-['Open_Sans'] text-sm sm:text-base md:text-lg lg:text-xl text-[#F2EEE6]/80 max-w-xl sm:max-w-2xl mx-auto leading-relaxed px-2">
                We're experiencing technical difficulties. Please try again.
              </p>
            </div>
          </div>
          <div className="container mx-auto px-4 py-12 sm:py-16 md:py-20 text-center">
            <p className="font-['Open_Sans'] text-[#D4C2A4] mb-6 sm:mb-8 text-sm sm:text-base md:text-lg px-2">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              className="bg-[#D4C2A4] hover:bg-[#D4C2A4]/80 text-[#16191D] font-['Open_Sans'] px-6 sm:px-8 py-2 sm:py-3 rounded-xl sm:rounded-2xl transition-all duration-300 hover:shadow-lg text-sm sm:text-base"
            >
              Try Again
            </Button>
          </div>
        </main>
        <Footer isDarkBackground={true} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#16191D] font-['Open_Sans'] text-[#F2EEE6] antialiased">
      <Header />
      <main className="">
        {/* Luxury Hero Section - Mobile Optimized */}
        <div className="relative overflow-hidden bg-[#16191D] pt-20 sm:pt-24 h-[70vh] sm:h-[80vh] md:min-h-screen">
          {/* Elegant Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                               radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
              backgroundSize: '40px 40px'
            }} />
          </div>

          <div className="relative z-10 container mx-auto px-4 sm:px-6 text-center py-12 sm:py-16 md:py-20 lg:py-24">
            {/* Premium Badge - Mobile Responsive */}
            <div className="inline-flex items-center gap-2 sm:gap-3 mb-6 sm:mb-8 md:mb-12 px-4 sm:px-6 py-2 sm:py-3 rounded-full bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/20">
              <BookOpen className="w-3 h-3 sm:w-4 sm:h-4 text-[#D4C2A4]" />
              <span className="text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm tracking-wider uppercase">
                Safari Chronicles
              </span>
            </div>

            {/* Luxury Title - Mobile First */}
            <h1 className="font-['Cormorant_Garamond'] text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-light leading-none mb-4 sm:mb-6 md:mb-8 tracking-wide text-[#F2EEE6]">
              Stories &
              <span className="block text-[#D4C2A4] italic -mt-1 sm:-mt-2">Insights</span>
            </h1>

            {/* Sophisticated Description - Mobile Optimized */}
            <p className="font-['Open_Sans'] text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl leading-relaxed text-[#F2EEE6]/90 max-w-xs xs:max-w-sm sm:max-w-2xl md:max-w-4xl mx-auto mb-8 sm:mb-12 md:mb-16 px-2 sm:px-0">
              Discover extraordinary tales, expert insights, and captivating stories from the heart of Tanzania's wilderness.
              <span className="hidden sm:inline"> Journey through our collection of safari adventures, wildlife encounters, and travel wisdom.</span>
            </p>

            {/* Action Buttons - Mobile Responsive */}
            <div className="flex flex-col xs:flex-row gap-3 xs:gap-4 justify-center items-center mb-8 sm:mb-12">
              <button
                onClick={() => document.getElementById('blog-content')?.scrollIntoView({ behavior: 'smooth' })}
                className="w-full xs:w-auto px-6 py-3 bg-[#D4C2A4] text-[#16191D] font-['Open_Sans'] font-semibold text-sm tracking-wide uppercase rounded-sm transition-all duration-500 hover:bg-[#F2EEE6] transform hover:-translate-y-1"
              >
                Explore Stories
              </button>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('');
                  document.getElementById('blog-content')?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="w-full xs:w-auto px-6 py-3 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-sm tracking-wide uppercase rounded-sm backdrop-blur-sm transition-all duration-500 hover:bg-[#D4C2A4]/10"
              >
                Latest Posts
              </button>
            </div>

            {/* Elegant Divider */}
            <div className="flex items-center justify-center gap-2 sm:gap-4 mb-8 sm:mb-12">
              <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent w-16 sm:w-24"></div>
              <div className="w-4 h-4 sm:w-6 sm:h-6 bg-[#D4C2A4] rounded-full"></div>
              <div className="h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent w-16 sm:w-24"></div>
            </div>
          </div>
        </div>

        {/* Luxury Content Section - Mobile Optimized */}
        <div id="blog-content" className="relative bg-[#16191D] py-8 sm:py-12 md:py-16 lg:py-20">
          <div className="container mx-auto px-4 sm:px-6">
            {/* Elegant Search and Filter Section - Mobile First */}
            <div className="mb-8 sm:mb-12 md:mb-16">
              {/* Section Header - Mobile Responsive */}
              <div className="text-center mb-6 sm:mb-8 md:mb-10">
                <h2 className="font-['Cormorant_Garamond'] text-2xl xs:text-3xl sm:text-4xl md:text-5xl font-light text-[#F2EEE6] mb-3 sm:mb-4">
                  Discover Stories
                </h2>
                <p className="font-['Open_Sans'] text-sm sm:text-base md:text-lg text-[#F2EEE6]/70 max-w-xs sm:max-w-2xl mx-auto px-2 sm:px-0">
                  Search through our collection of safari adventures and insights
                </p>
              </div>

              {/* Glass Morphism Search Container - Mobile Optimized */}
              <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8 max-w-4xl mx-auto">
                {/* Search Input - Mobile First */}
                <div className="relative mb-4 sm:mb-6">
                  <Search className="absolute left-3 sm:left-4 top-1/2 h-4 w-4 sm:h-5 sm:w-5 -translate-y-1/2 text-[#D4C2A4]/60" />
                  <Input
                    type="text"
                    placeholder="Search safari stories, tips, and insights..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 sm:pl-12 pr-4 py-3 sm:py-4 bg-[#16191D]/50 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#F2EEE6]/50 rounded-lg sm:rounded-xl focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20 transition-all duration-300 text-sm sm:text-base"
                  />
                </div>

                {/* Category Filters - Mobile Responsive */}
                <div className="flex flex-wrap gap-2 sm:gap-3 justify-center">
                  <Button
                    variant={selectedCategory === '' ? 'default' : 'outline'}
                    onClick={() => setSelectedCategory('')}
                    className={`px-3 sm:px-4 py-2 sm:py-2.5 text-xs sm:text-sm font-['Open_Sans'] rounded-lg transition-all duration-300 ${
                      selectedCategory === ''
                        ? 'bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/80'
                        : 'bg-transparent border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]'
                    }`}
                  >
                    All Stories
                  </Button>
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? 'default' : 'outline'}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-3 sm:px-4 py-2 sm:py-2.5 text-xs sm:text-sm font-['Open_Sans'] rounded-lg transition-all duration-300 ${
                        selectedCategory === category
                          ? 'bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/80'
                          : 'bg-transparent border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]'
                      }`}
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Luxury Blog Posts Grid - Mobile Optimized */}
            {filteredPosts.length === 0 ? (
              <div className="text-center py-12 sm:py-16 md:py-20">
                {/* Empty State - Mobile Responsive */}
                <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl p-6 sm:p-8 md:p-12 max-w-md sm:max-w-lg mx-auto">
                  <BookOpen className="w-12 h-12 sm:w-16 sm:h-16 text-[#D4C2A4]/60 mx-auto mb-4 sm:mb-6" />
                  <p className="font-['Open_Sans'] text-[#F2EEE6]/80 text-sm sm:text-base md:text-lg mb-4 sm:mb-6 px-2">
                    {posts.length === 0 ? 'No safari stories available yet.' : 'No stories match your search criteria.'}
                  </p>
                  {searchTerm && (
                    <Button
                      onClick={() => setSearchTerm('')}
                      className="bg-[#D4C2A4] hover:bg-[#D4C2A4]/80 text-[#16191D] font-['Open_Sans'] px-4 sm:px-6 py-2 sm:py-3 rounded-lg transition-all duration-300 text-sm sm:text-base"
                    >
                      Clear Search
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 md:gap-10">
                {filteredPosts.map((post) => (
                  <div key={post.id} className="group">
                    {/* Luxury Card Container - Mobile First */}
                    <div className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl overflow-hidden transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/40 hover:shadow-2xl hover:shadow-[#D4C2A4]/10 transform hover:-translate-y-2">

                      {/* Featured Image - Mobile Optimized */}
                      {post.featuredImage && (
                        <div className="aspect-video overflow-hidden relative">
                          <img
                            src={post.featuredImage.includes('http')
                              ? post.featuredImage
                              : `https://images.unsplash.com/${post.featuredImage}?auto=format&fit=crop&w=600&h=400`
                            }
                            alt={post.title}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                          />
                          {/* Elegant Overlay */}
                          <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                          {/* Category Badge - Mobile Responsive */}
                          <div className="absolute top-3 sm:top-4 left-3 sm:left-4">
                            <span className="px-2 sm:px-3 py-1 sm:py-1.5 bg-[#D4C2A4]/90 backdrop-blur-sm text-[#16191D] font-['Open_Sans'] text-xs sm:text-sm font-medium rounded-full">
                              {post.category}
                            </span>
                          </div>

                          {/* Read Time - Mobile Responsive */}
                          <div className="absolute top-3 sm:top-4 right-3 sm:right-4">
                            <div className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1 sm:py-1.5 bg-[#16191D]/70 backdrop-blur-sm rounded-full">
                              <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-[#D4C2A4]" />
                              <span className="text-[#F2EEE6] font-['Open_Sans'] text-xs sm:text-sm">
                                {Math.ceil(post.content.length / 200)} min
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Content Section - Mobile Optimized */}
                      <div className="p-4 sm:p-6 md:p-8">
                        {/* Title - Mobile First */}
                        <h3 className="font-['Cormorant_Garamond'] text-lg xs:text-xl sm:text-2xl md:text-3xl font-medium text-[#F2EEE6] mb-3 sm:mb-4 leading-tight group-hover:text-[#D4C2A4] transition-colors duration-300">
                          <Link to={`/blog/${post.slug}`} className="hover:underline">
                            {post.title}
                          </Link>
                        </h3>

                        {/* Excerpt - Mobile Responsive */}
                        <p className="font-['Open_Sans'] text-sm sm:text-base text-[#F2EEE6]/80 mb-4 sm:mb-6 leading-relaxed line-clamp-3">
                          {post.excerpt}
                        </p>

                        {/* Meta Information - Mobile Optimized */}
                        <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between gap-2 xs:gap-4 mb-4 sm:mb-6">
                          <div className="flex items-center gap-2">
                            <User className="h-3 w-3 sm:h-4 sm:w-4 text-[#D4C2A4]" />
                            <span className="font-['Open_Sans'] text-xs sm:text-sm text-[#F2EEE6]/70">
                              {post.authorId || 'Safari Expert'}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-[#D4C2A4]" />
                            <span className="font-['Open_Sans'] text-xs sm:text-sm text-[#F2EEE6]/70">
                              {(() => {
                                const date = post.publishedAt || post.createdAt;
                                if (date && typeof date.toDate === 'function') {
                                  return date.toDate().toLocaleDateString();
                                }
                                if (date instanceof Date) {
                                  return date.toLocaleDateString();
                                }
                                return new Date().toLocaleDateString();
                              })()}
                            </span>
                          </div>
                        </div>

                        {/* CTA Button - Mobile Optimized */}
                        <Link to={`/blog/${post.slug}`}>
                          <Button className="w-full bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4] hover:text-[#16191D] font-['Open_Sans'] py-2 sm:py-3 rounded-lg transition-all duration-300 text-sm sm:text-base group-hover:border-[#D4C2A4]">
                            Read Full Story
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Luxury Footer Transition - Mobile Optimized */}
        <div className="relative bg-[#16191D] py-8 sm:py-12 lg:py-16">
          <div className="container mx-auto px-4 sm:px-6 text-center">
            {/* Elegant Divider - Mobile Responsive */}
            <div className="flex items-center justify-center mb-6 sm:mb-8 lg:mb-12">
              <div className="w-16 sm:w-24 lg:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
              <div className="mx-2 sm:mx-4 w-2 h-2 sm:w-3 sm:h-3 bg-[#D4C2A4] rounded-full"></div>
              <div className="w-16 sm:w-24 lg:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
            </div>

            {/* Luxury CTA - Mobile First */}
            <h2 className="font-['Cormorant_Garamond'] text-2xl xs:text-3xl sm:text-3xl md:text-4xl lg:text-5xl font-light text-[#F2EEE6] mb-4 sm:mb-6">
              Share Your
              <span className="block text-[#D4C2A4] italic">Safari Story</span>
            </h2>

            <p className="font-['Open_Sans'] text-sm xs:text-base sm:text-lg text-[#F2EEE6]/80 mb-6 sm:mb-8 lg:mb-10 max-w-xs xs:max-w-sm sm:max-w-2xl mx-auto px-2 sm:px-0">
              Have an incredible safari experience to share?
              <span className="hidden sm:inline"> We'd love to feature your story and inspire other adventurers.</span>
            </p>

            <div className="flex flex-col xs:flex-row gap-3 sm:gap-4 lg:gap-6 justify-center">
              <button className="w-full xs:w-auto px-6 sm:px-8 lg:px-10 py-3 sm:py-4 bg-[#D4C2A4] text-[#16191D] font-['Open_Sans'] font-semibold text-xs sm:text-sm tracking-wide uppercase rounded-sm transition-all duration-500 hover:bg-[#F2EEE6] hover:shadow-2xl hover:shadow-[#D4C2A4]/20 transform hover:-translate-y-1">
                Submit Story
              </button>
              <button className="w-full xs:w-auto px-6 sm:px-8 lg:px-10 py-3 sm:py-4 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-xs sm:text-sm tracking-wide uppercase rounded-sm backdrop-blur-sm transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transform hover:-translate-y-1">
                Contact Us
              </button>
            </div>
          </div>
        </div>
      </main>
      <Footer isDarkBackground={true} />
    </div>
  );
};

export default Blog;

import React, { useState, useEffect, useRef, memo } from 'react';
import { Link } from 'react-router-dom';

const HeroSection = memo(() => {
  const [isMobile, setIsMobile] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleCanPlay = () => {
      // Ensure video plays smoothly by preloading
      video.play().catch(console.error);
    };

    video.addEventListener('canplay', handleCanPlay);

    return () => {
      video.removeEventListener('canplay', handleCanPlay);
    };
  }, []);

  return (
    <section className="hero-section">
      {/* Background Video - Only load on desktop for performance */}
      {!isMobile ? (
        <video
          ref={videoRef}
          className="hero-video"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          poster="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fhero.jpg?alt=media&token=30d86270-9862-4537-95bc-d70ef0080603"
          style={{
            opacity: 1, // Always show video/poster
            transition: 'opacity 0.5s ease-in-out'
          }}
        >
          <source src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2FGenerated%20File%20June%2024%2C%202025%20-%208_57AM%20(1)%20(1).mp4?alt=media&token=fbba50d7-119c-4f0b-836f-34c6eeaecd0d" type="video/mp4" />
          {/* Fallback for browsers that don't support video */}
          Your browser does not support the video tag.
        </video>
      ) : (
        // Mobile fallback - static background image for better performance
        <video
          ref={videoRef}
          className="hero-video"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          poster="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fhero.jpg?alt=media&token=30d86270-9862-4537-95bc-d70ef0080603"
          style={{
            opacity: 1, // Always show video/poster
            transition: 'opacity 0.5s ease-in-out'
          }}
        >
          <source src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2FGenerated%20File%20June%2024%2C%202025%20-%208_57AM%20(1)%20(1).mp4?alt=media&token=fbba50d7-119c-4f0b-836f-34c6eeaecd0d" type="video/mp4" />
          {/* Fallback for browsers that don't support video */}
          Your browser does not support the video tag.
        </video>
      )}

      {/* Video Overlay */}
      <div className="hero-overlay"></div>

      <div className="hero-content">
        {/* Logo and Company Name Combined */}
        <div className="flex flex-col items-center mb-8">
          <img
            src="/photos/heroLogo.svg"
            alt="Warriors of Africa Safari Logo"
            className="hero-logo mb-4"
          />
          <div className="hero-company-name">
            WARRIORS OF <br />
            AFRICA SAFARI
          </div>
        </div>

        {/* Main Headline */}
        <h1 className="hero-headline">
          Welcome to the<br />
          Land of <em>Endless</em><br />
          Safari
        </h1>

        {/* Call-to-Action Button */}
        <Link to="/tour-builder" className="hero-cta-button">
          JOIN THE SAFARI
        </Link>
      </div>
    </section>
  );
});

HeroSection.displayName = 'HeroSection';

export default HeroSection;

import { UserProfile } from '@/types/firebase';

export interface ProfileCompletionResult {
  percentage: number;
  completedFields: string[];
  missingFields: string[];
  suggestions: string[];
}

export const calculateProfileCompletion = (userProfile: UserProfile | null): ProfileCompletionResult => {
  if (!userProfile) {
    return {
      percentage: 0,
      completedFields: [],
      missingFields: ['All profile fields'],
      suggestions: ['Please complete your profile to get personalized recommendations']
    };
  }

  const completedFields: string[] = [];
  const missingFields: string[] = [];
  const suggestions: string[] = [];

  // Basic Information (30% weight)
  if (userProfile.displayName?.trim()) {
    completedFields.push('Display Name');
  } else {
    missingFields.push('Display Name');
    suggestions.push('Add your full name');
  }

  if (userProfile.phone?.trim()) {
    completedFields.push('Phone Number');
  } else {
    missingFields.push('Phone Number');
    suggestions.push('Add your phone number for booking confirmations');
  }

  if (userProfile.country?.trim()) {
    completedFields.push('Country');
  } else {
    missingFields.push('Country');
    suggestions.push('Add your country for visa and travel requirements');
  }

  // Preferences (50% weight)
  if (userProfile.preferences?.accommodation) {
    completedFields.push('Accommodation Preference');
  } else {
    missingFields.push('Accommodation Preference');
    suggestions.push('Choose your preferred accommodation level');
  }

  if (userProfile.preferences?.activities && userProfile.preferences.activities.length > 0) {
    completedFields.push('Activity Interests');
  } else {
    missingFields.push('Activity Interests');
    suggestions.push('Select activities you\'re interested in');
  }

  if (userProfile.preferences?.fitnessLevel) {
    completedFields.push('Fitness Level');
  } else {
    missingFields.push('Fitness Level');
    suggestions.push('Set your fitness level for appropriate tour recommendations');
  }

  // Special Interests (10% weight)
  // Photography and birding interests are always "set" since they're boolean
  completedFields.push('Photography Interest');
  completedFields.push('Birding Interest');

  // Dietary Restrictions (10% weight) - Optional but considered complete if user has reviewed
  completedFields.push('Dietary Preferences');

  // Calculate percentage based on weighted importance
  const weights = {
    'Display Name': 10,
    'Phone Number': 10,
    'Country': 10,
    'Accommodation Preference': 20,
    'Activity Interests': 20,
    'Fitness Level': 10,
    'Photography Interest': 5,
    'Birding Interest': 5,
    'Dietary Preferences': 10
  };

  const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
  const completedWeight = completedFields.reduce((sum, field) => {
    return sum + (weights[field as keyof typeof weights] || 0);
  }, 0);

  const percentage = Math.round((completedWeight / totalWeight) * 100);

  return {
    percentage,
    completedFields,
    missingFields,
    suggestions: suggestions.slice(0, 3) // Limit to top 3 suggestions
  };
};

export const getProfileCompletionBadge = (percentage: number) => {
  if (percentage >= 90) {
    return {
      label: 'Complete',
      color: 'bg-green-100 text-green-800',
      icon: '✅'
    };
  } else if (percentage >= 70) {
    return {
      label: 'Almost Complete',
      color: 'bg-yellow-100 text-yellow-800',
      icon: '⚡'
    };
  } else if (percentage >= 40) {
    return {
      label: 'In Progress',
      color: 'bg-blue-100 text-blue-800',
      icon: '🔄'
    };
  } else {
    return {
      label: 'Getting Started',
      color: 'bg-gray-100 text-gray-800',
      icon: '🚀'
    };
  }
};

export const shouldShowProfilePrompt = (userProfile: UserProfile | null): boolean => {
  const completion = calculateProfileCompletion(userProfile);
  return completion.percentage < 70;
};

export const getNextProfileStep = (userProfile: UserProfile | null): string | null => {
  const completion = calculateProfileCompletion(userProfile);
  
  if (completion.missingFields.includes('Display Name')) {
    return 'Add your full name';
  }
  if (completion.missingFields.includes('Accommodation Preference')) {
    return 'Choose your accommodation preference';
  }
  if (completion.missingFields.includes('Activity Interests')) {
    return 'Select your activity interests';
  }
  if (completion.missingFields.includes('Phone Number')) {
    return 'Add your phone number';
  }
  if (completion.missingFields.includes('Country')) {
    return 'Add your country';
  }
  if (completion.missingFields.includes('Fitness Level')) {
    return 'Set your fitness level';
  }
  
  return null;
};

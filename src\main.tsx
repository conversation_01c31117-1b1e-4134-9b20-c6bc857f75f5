import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { initWebVitals, initPerformanceObserver } from './utils/webVitals'
import { registerSW } from './utils/serviceWorker'
import { initializeThirdPartyOptimizations } from './utils/thirdPartyOptimization'

// Initialize performance monitoring and optimizations
if (typeof window !== 'undefined') {
  // Initialize third-party optimizations
  initializeThirdPartyOptimizations();

  // Initialize performance monitoring
  initWebVitals();
  initPerformanceObserver();

  // Register service worker for caching and offline functionality
  registerSW({
    onSuccess: () => {
      console.log('App is ready for offline use');
    },
    onUpdate: () => {
      console.log('New content available, please refresh');
    },
    onOfflineReady: () => {
      console.log('App is ready to work offline');
    }
  });
}

createRoot(document.getElementById("root")!).render(<App />);

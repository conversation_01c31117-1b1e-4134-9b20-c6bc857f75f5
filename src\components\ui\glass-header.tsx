import * as React from "react"
import { cn } from "@/lib/utils"

interface GlassHeaderProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode
  variant?: "ultra-glass" | "frosted" | "crystal"
  intensity?: "light" | "medium" | "heavy" | "extreme"
}

interface GlassFooterProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode
  variant?: "ultra-glass" | "frosted" | "crystal"
  intensity?: "light" | "medium" | "heavy" | "extreme"
}

const GlassHeader = React.forwardRef<HTMLElement, GlassHeaderProps>(
  ({ className, children, variant = "ultra-glass", intensity = "heavy", ...props }, ref) => {
    const variantClasses = {
      "ultra-glass": "bg-transparent",
      "frosted": "bg-transparent",
      "crystal": "bg-transparent"
    }

    const intensityClasses = {
      light: "backdrop-blur-sm",
      medium: "backdrop-blur-md",
      heavy: "backdrop-blur-lg",
      extreme: "backdrop-blur-xl"
    }

    return (
      <header
        ref={ref}
        className={cn(
          "fixed top-0 left-0 right-0 z-50 transition-all duration-500",
          // Real glass - completely transparent base
          variantClasses[variant],
          intensityClasses[intensity],
          // Real glass shadow
          "shadow-[0_4px_16px_rgba(0,0,0,0.05)]",
          className
        )}
        style={{
          background: 'rgba(255, 255, 255, 0.02)',
          backdropFilter: 'blur(20px) saturate(180%)',
          WebkitBackdropFilter: 'blur(20px) saturate(180%)',
        }}
        {...props}
      >
        {/* Real glass layer - minimal and transparent */}
        <div className="absolute inset-0 -z-10">
          {/* Single glass layer for real glass effect */}
          <div
            className="absolute inset-0"
            style={{
              background: 'linear-gradient(135deg, rgba(255,255,255,0.01) 0%, rgba(255,255,255,0.03) 50%, rgba(255,255,255,0.01) 100%)',
              backdropFilter: 'url("#real-glass-filter") blur(16px)',
              WebkitBackdropFilter: 'url("#real-glass-filter") blur(16px)'
            }}
          />


        </div>

        {/* Content */}
        <div className="relative z-10">
          {children}
        </div>

        {/* Advanced glass filter SVG */}
        <UltraGlassFilter />
      </header>
    )
  }
)

GlassHeader.displayName = "GlassHeader"

const GlassFooter = React.forwardRef<HTMLElement, GlassFooterProps>(
  ({ className, children, variant = "ultra-glass", intensity = "heavy", ...props }, ref) => {
    const variantClasses = {
      "ultra-glass": "bg-transparent",
      "frosted": "bg-transparent",
      "crystal": "bg-transparent"
    }

    const intensityClasses = {
      light: "backdrop-blur-sm",
      medium: "backdrop-blur-md",
      heavy: "backdrop-blur-lg",
      extreme: "backdrop-blur-xl"
    }

    return (
      <footer
        ref={ref}
        className={cn(
          "relative overflow-hidden transition-all duration-500",
          // Ultra transparent water-like glass
          variantClasses[variant],
          // Minimal blur for crystal clarity
          "backdrop-blur-sm",
          // Subtle shadow like water surface
          "shadow-[0_-2px_8px_rgba(0,0,0,0.02)]",
          className
        )}
        style={{
          // Luxury deep charcoal background with transparency
          background: 'rgba(22, 25, 29, 0.95)',
          backdropFilter: 'blur(8px) saturate(120%) brightness(110%)',
          WebkitBackdropFilter: 'blur(8px) saturate(120%) brightness(110%)',
          // Luxury gold border accent
          borderTop: '1px solid rgba(212, 194, 164, 0.3)',
        }}
        {...props}
      >
        {/* Luxury background layer */}
        <div className="absolute inset-0 -z-10">
          {/* Luxury gradient effect */}
          <div
            className="absolute inset-0"
            style={{
              background: 'linear-gradient(180deg, rgba(212,194,164,0.05) 0%, rgba(212,194,164,0.02) 50%, rgba(212,194,164,0.05) 100%)',
              backdropFilter: 'url("#water-glass-filter") blur(4px)',
              WebkitBackdropFilter: 'url("#water-glass-filter") blur(4px)'
            }}
          />
        </div>

        {/* Content */}
        <div className="relative z-10">
          {children}
        </div>

        {/* Water-like glass filter SVG */}
        <WaterGlassFilter />
      </footer>
    )
  }
)

GlassFooter.displayName = "GlassFooter"

function UltraGlassFilter() {
  return (
    <svg className="hidden">
      <defs>
        {/* Real Glass Filter - Subtle and realistic */}
        <filter
          id="real-glass-filter"
          x="0%"
          y="0%"
          width="100%"
          height="100%"
          colorInterpolationFilters="sRGB"
        >
          {/* Very subtle noise for real glass texture */}
          <feTurbulence
            type="fractalNoise"
            baseFrequency="0.005 0.005"
            numOctaves="1"
            seed="1"
            result="subtleNoise"
          />

          {/* Minimal blur for glass texture */}
          <feGaussianBlur in="subtleNoise" stdDeviation="0.5" result="blurredNoise" />

          {/* Very subtle displacement - like real glass */}
          <feDisplacementMap
            in="SourceGraphic"
            in2="blurredNoise"
            scale="2"
            xChannelSelector="R"
            yChannelSelector="G"
            result="glassDisplaced"
          />

          {/* Minimal processing to maintain transparency */}
          <feGaussianBlur in="glassDisplaced" stdDeviation="0.2" result="finalGlass" />

          {/* Output with minimal modification */}
          <feComposite in="finalGlass" in2="SourceGraphic" operator="over" />
        </filter>
      </defs>
    </svg>
  )
}

function WaterGlassFilter() {
  return (
    <svg className="hidden">
      <defs>
        {/* Water Glass Filter - Ultra clean and transparent like water */}
        <filter
          id="water-glass-filter"
          x="0%"
          y="0%"
          width="100%"
          height="100%"
          colorInterpolationFilters="sRGB"
        >
          {/* Ultra-subtle water ripple effect */}
          <feTurbulence
            type="fractalNoise"
            baseFrequency="0.002 0.001"
            numOctaves="1"
            seed="2"
            result="waterRipple"
          />

          {/* Minimal blur for water clarity */}
          <feGaussianBlur in="waterRipple" stdDeviation="0.2" result="softRipple" />

          {/* Extremely subtle displacement - like water surface */}
          <feDisplacementMap
            in="SourceGraphic"
            in2="softRipple"
            scale="0.5"
            xChannelSelector="R"
            yChannelSelector="G"
            result="waterDisplaced"
          />

          {/* Color matrix for crystal clarity */}
          <feColorMatrix
            in="waterDisplaced"
            type="matrix"
            values="1.01 0 0 0 0.002
                    0 1.01 0 0 0.002
                    0 0 1.02 0 0.002
                    0 0 0 0.98 0"
            result="crystalClear"
          />

          {/* Final water-like composite */}
          <feComposite in="crystalClear" in2="SourceGraphic" operator="over" />
        </filter>
      </defs>
    </svg>
  )
}

export { GlassHeader, GlassFooter }

import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/contexts/AuthContext';
import { 
  calculateProfileCompletion, 
  getProfileCompletionBadge, 
  shouldShowProfilePrompt,
  getNextProfileStep 
} from '@/utils/profileCompletion';
import { User, ArrowRight, X } from 'lucide-react';

interface ProfileCompletionBannerProps {
  onDismiss?: () => void;
  showDismiss?: boolean;
  compact?: boolean;
}

const ProfileCompletionBanner: React.FC<ProfileCompletionBannerProps> = ({ 
  onDismiss, 
  showDismiss = false,
  compact = false 
}) => {
  const { userProfile } = useAuth();
  
  // Don't show if profile is sufficiently complete
  if (!shouldShowProfilePrompt(userProfile)) {
    return null;
  }

  const completion = calculateProfileCompletion(userProfile);
  const badge = getProfileCompletionBadge(completion.percentage);
  const nextStep = getNextProfileStep(userProfile);

  if (compact) {
    return (
      <Card className="border-[#D4C2A4]/20 bg-[#1a1e23]/50 backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <User className="h-5 w-5 text-[#D4C2A4]" />
              <div>
                <p className="text-sm font-medium text-[#F2EEE6] font-['Open_Sans']">
                  Profile {completion.percentage}% complete
                </p>
                {nextStep && (
                  <p className="text-xs text-[#A9A9A9] font-['Open_Sans']">{nextStep}</p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Link to="/user-dashboard">
                <Button size="sm" variant="outline" className="text-[#D4C2A4] border-[#D4C2A4]/30 bg-transparent hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transition-all duration-300 font-['Open_Sans']">
                  Complete
                </Button>
              </Link>
              {showDismiss && onDismiss && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={onDismiss}
                  className="h-8 w-8 p-0 text-[#D4C2A4] hover:text-[#F2EEE6] hover:bg-[#D4C2A4]/10 transition-all duration-300"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-[#D4C2A4]/20 bg-gradient-to-r from-[#1a1e23]/50 to-[#16191D]/50 backdrop-blur-sm">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-3">
              <User className="h-6 w-6 text-[#D4C2A4]" />
              <div>
                <h3 className="font-semibold text-[#F2EEE6] font-['Cormorant_Garamond']">Complete Your Profile</h3>
                <p className="text-sm text-[#A9A9A9] font-['Open_Sans']">
                  Get personalized safari recommendations based on your preferences
                </p>
              </div>
              <Badge className={`${badge.color} border-0 bg-[#D4C2A4]/20 text-[#D4C2A4]`}>
                <span className="mr-1">{badge.icon}</span>
                {badge.label}
              </Badge>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm font-['Open_Sans']">
                <span className="text-[#A9A9A9]">Profile completion</span>
                <span className="font-medium text-[#D4C2A4]">{completion.percentage}%</span>
              </div>
              <Progress
                value={completion.percentage}
                className="h-2 bg-[#16191D]/50"
              />

              {completion.suggestions.length > 0 && (
                <div className="space-y-1">
                  <p className="text-sm font-medium text-[#D4C2A4] font-['Open_Sans']">Next steps:</p>
                  <ul className="text-sm text-[#A9A9A9] space-y-1 font-['Open_Sans']">
                    {completion.suggestions.map((suggestion, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <span className="w-1 h-1 bg-[#D4C2A4] rounded-full"></span>
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <div className="flex items-center gap-3 mt-4">
              <Link to="/user-dashboard">
                <Button className="bg-[#D4C2A4] hover:bg-[#D4C2A4]/90 text-[#16191D] transition-all duration-300 font-['Open_Sans'] font-medium">
                  Complete Profile
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              {showDismiss && onDismiss && (
                <Button
                  variant="ghost"
                  onClick={onDismiss}
                  className="text-[#D4C2A4] hover:text-[#F2EEE6] hover:bg-[#D4C2A4]/10 transition-all duration-300 font-['Open_Sans']"
                >
                  Maybe Later
                </Button>
              )}
            </div>
          </div>

          {showDismiss && onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="text-[#D4C2A4] hover:text-[#F2EEE6] hover:bg-[#D4C2A4]/10 ml-4 transition-all duration-300"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileCompletionBanner;

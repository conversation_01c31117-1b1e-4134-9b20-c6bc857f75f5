/* MonochromeGallery Luxury Enhancements */

/* Premium Typography Effects */
.luxury-typography {
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.02em;
}

/* Enhanced luxury glass container */
.luxury-glass-container {
  background: linear-gradient(135deg, 
    rgba(212, 194, 164, 0.12) 0%,
    rgba(212, 194, 164, 0.08) 50%,
    rgba(212, 194, 164, 0.15) 100%);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(212, 194, 164, 0.3);
  box-shadow: 
    0 25px 50px rgba(22, 25, 29, 0.4),
    0 0 0 1px rgba(212, 194, 164, 0.1),
    inset 0 1px 0 rgba(212, 194, 164, 0.2),
    inset 0 -1px 0 rgba(212, 194, 164, 0.1);
  position: relative;
  overflow: hidden;
}

.luxury-glass-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.6), 
    transparent);
  animation: luxuryShimmer 3s ease-in-out infinite;
}

.luxury-glass-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.1), 
    transparent);
  animation: luxurySlide 4s ease-in-out infinite;
}

/* Luxury Button Enhancements */
.luxury-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.luxury-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.2), 
    transparent);
  transition: left 0.5s ease;
}

.luxury-button:hover::before {
  left: 100%;
}

.luxury-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 
    0 10px 25px rgba(212, 194, 164, 0.3),
    0 0 20px rgba(212, 194, 164, 0.2);
}

/* Touch Target Enhancement */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Premium Shimmer Animation */
@keyframes luxuryShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Luxury Slide Animation */
@keyframes luxurySlide {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* Elegant Border Animations */
@keyframes luxuryBorderGlow {
  0%, 100% {
    border-color: rgba(212, 194, 164, 0.4);
    box-shadow: 0 0 10px rgba(212, 194, 164, 0.2);
  }
  50% {
    border-color: rgba(212, 194, 164, 0.8);
    box-shadow: 0 0 25px rgba(212, 194, 164, 0.4);
  }
}

.luxury-border-glow {
  animation: luxuryBorderGlow 3s ease-in-out infinite;
}

/* Responsive Design for Mobile Devices */
@media (max-width: 768px) {
  .luxury-glass-container {
    backdrop-filter: blur(20px) saturate(150%);
    -webkit-backdrop-filter: blur(20px) saturate(150%);
    margin: 1rem;
    border-radius: 1rem;
  }
  
  .luxury-button:hover {
    transform: translateY(-2px) scale(1.01);
  }
}

@media (max-width: 640px) {
  .luxury-glass-container {
    backdrop-filter: blur(15px) saturate(130%);
    -webkit-backdrop-filter: blur(15px) saturate(130%);
    padding: 1.5rem !important;
    margin: 0.75rem;
  }
  
  .luxury-button {
    min-height: 48px;
    font-size: 0.875rem;
  }
  
  .luxury-border-glow {
    animation-duration: 4s;
  }
}

@media (max-width: 480px) {
  .luxury-glass-container {
    backdrop-filter: blur(12px) saturate(120%);
    -webkit-backdrop-filter: blur(12px) saturate(120%);
    padding: 1.25rem !important;
    margin: 0.5rem;
    border-radius: 0.75rem;
  }
  
  .luxury-button:hover {
    transform: translateY(-1px);
  }
}

/* Extra Small Mobile Devices */
@media (max-width: 375px) {
  .luxury-glass-container {
    margin: 0.25rem;
    padding: 1rem !important;
  }
  
  .luxury-button {
    font-size: 0.75rem;
    padding: 0.75rem 1rem !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .luxury-glass-container {
    background: rgba(212, 194, 164, 0.2);
    border-color: rgba(212, 194, 164, 0.6);
  }
  
  .luxury-button {
    background: #D4C2A4;
    border: 2px solid #16191D;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .luxury-border-glow,
  .luxury-glass-container::before,
  .luxury-glass-container::after {
    animation: none;
  }
  
  .luxury-button::before {
    transition: none;
  }
  
  .luxury-button:hover {
    transform: none;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .luxury-button:hover {
    transform: scale(0.98);
  }
  
  .luxury-glass-container {
    -webkit-tap-highlight-color: rgba(212, 194, 164, 0.1);
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .luxury-glass-container {
    background: linear-gradient(135deg,
      rgba(212, 194, 164, 0.15) 0%,
      rgba(212, 194, 164, 0.10) 50%,
      rgba(212, 194, 164, 0.18) 100%);
  }
}

/* Custom Scrollbar Styling */
.luxury-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.luxury-scrollbar::-webkit-scrollbar-track {
  background: rgba(22, 25, 29, 0.1);
  border-radius: 4px;
}

.luxury-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #D4C2A4, #C4B294);
  border-radius: 4px;
  border: 1px solid rgba(22, 25, 29, 0.1);
}

.luxury-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #E4D2B4, #D4C2A4);
  box-shadow: 0 0 10px rgba(212, 194, 164, 0.3);
}

/* Gallery Grid Enhancements */
.gallery-grid-item {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(212, 194, 164, 0.05) 0%, rgba(212, 194, 164, 0.02) 100%);
}

.gallery-grid-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(212, 194, 164, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.gallery-grid-item:hover::before {
  opacity: 1;
}

/* Mobile-specific enhancements */
@media (max-width: 640px) {
  .gallery-grid-item {
    aspect-ratio: 4/3;
    min-height: 80px;
    height: auto;
  }
}

@media (max-width: 480px) {
  .gallery-grid-item {
    aspect-ratio: 3/2;
    min-height: 70px;
    height: auto;
  }
}

@media (max-width: 375px) {
  .gallery-grid-item {
    aspect-ratio: 3/2;
    min-height: 60px;
    height: auto;
  }
}

/* Gallery Container Centering */
.gallery-container-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.gallery-container {
  margin: 0 auto;
  display: block;
}

/* Landscape mobile orientation */
@media (max-height: 500px) and (orientation: landscape) {
  .luxury-glass-container {
    margin: 0.5rem;
    padding: 1rem !important;
  }
}

/* Very small screens (iPhone SE, etc.) */
@media (max-width: 320px) {
  .luxury-glass-container {
    margin: 0.25rem auto;
    padding: 0.75rem !important;
    border-radius: 0.5rem;
    max-width: 95%;
  }

  .luxury-button {
    font-size: 0.625rem;
    padding: 0.5rem 0.75rem !important;
    min-height: 40px;
  }

  .gallery-grid-item {
    min-height: 45px;
  }
}

/* Extra responsive improvements for centering */
@media (max-width: 768px) {
  .gallery-container-wrapper {
    padding: 0 0.5rem;
  }
}

@media (max-width: 480px) {
  .gallery-container-wrapper {
    padding: 0 0.25rem;
  }
}

@media (max-width: 320px) {
  .gallery-container-wrapper {
    padding: 0 0.125rem;
  }
}

/* Large screens and desktops */
@media (min-width: 1440px) {
  .luxury-glass-container {
    backdrop-filter: blur(30px) saturate(200%);
    -webkit-backdrop-filter: blur(30px) saturate(200%);
  }

  .luxury-button {
    font-size: 1.125rem;
    padding: 1rem 2rem;
  }
}

/* Ultra-wide screens */
@media (min-width: 1920px) {
  .luxury-glass-container {
    max-width: 1200px;
    margin: 2rem auto;
  }
}

/* Performance optimizations */
.gallery-grid-item,
.luxury-button,
.luxury-glass-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Smooth animations for better UX */
* {
  -webkit-tap-highlight-color: transparent;
}

.gallery-grid-item:active {
  transform: scale(0.98);
}

@media (hover: hover) {
  .gallery-grid-item:hover {
    transform: scale(1.02);
  }
}

/* Perfect centering for all screen sizes */
.gallery-centered-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0 auto;
}

/* Responsive grid improvements */
@media (max-width: 640px) {
  .gallery-grid-item {
    border-radius: 0.375rem;
  }
}

@media (max-width: 480px) {
  .gallery-grid-item {
    border-radius: 0.25rem;
  }
}

@media (max-width: 375px) {
  .gallery-grid-item {
    border-radius: 0.125rem;
  }
}

/* Extra small screens optimization */
@media (max-width: 320px) {
  .gallery-responsive-grid {
    gap: 0.25rem !important;
    padding: 0.5rem !important;
  }

  .gallery-grid-item {
    min-height: 45px;
    border-radius: 0.25rem;
  }
}

/* Responsive Grid Layout */
.gallery-responsive-grid {
  display: grid;
  width: 100%;
  height: 100%;
}

/* Mobile Grid Layout (2 columns, 3 rows) */
@media (max-width: 640px) {
  .gallery-responsive-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    grid-auto-flow: row;
    align-items: stretch;
    justify-items: stretch;
  }

  .gallery-grid-item {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .gallery-grid-item > div {
    flex: 1;
  }
}

/* Desktop Grid Layout (3 columns, 2 rows) */
@media (min-width: 641px) {
  .gallery-responsive-grid {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    grid-auto-flow: row;
  }

  .gallery-grid-item {
    width: 100%;
    height: 100%;
    aspect-ratio: auto;
  }
}

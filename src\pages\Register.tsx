
import React from 'react';
import { Link } from 'react-router-dom';
import { FiHome } from 'react-icons/fi';
import RegisterForm from '@/components/auth/RegisterForm';


const Register = () => {
  return (
    <div className="min-h-screen bg-[#16191D] relative overflow-hidden">
      {/* Luxury Background Layers */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#16191D] via-[#1a1e23] to-[#16191D]"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-[#D4C2A4]/8 via-[#D4C2A4]/3 to-transparent"></div>

      {/* Premium Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-32 h-32 md:w-64 md:h-64 bg-gradient-to-r from-[#D4C2A4]/10 to-[#D4C2A4]/5 rounded-full opacity-30 blur-3xl animate-pulse"></div>
        <div className="absolute top-40 left-20 w-48 h-48 md:w-96 md:h-96 bg-gradient-to-r from-[#D4C2A4]/8 to-[#D4C2A4]/3 rounded-full opacity-20 blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 right-1/2 w-40 h-40 md:w-80 md:h-80 bg-gradient-to-r from-[#D4C2A4]/6 to-[#D4C2A4]/2 rounded-full opacity-25 blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Luxury Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-[#D4C2A4]/20 rounded-full luxury-particle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* Premium Decorative Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/3 right-10 w-2 h-2 border border-[#D4C2A4]/30 rotate-45 animate-pulse"></div>
        <div className="absolute top-2/3 left-10 w-3 h-3 border border-[#D4C2A4]/20 rotate-12 animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/3 right-1/4 w-1 h-1 bg-[#D4C2A4]/40 rounded-full animate-pulse delay-2000"></div>
        <div className="absolute top-1/2 left-1/3 w-1 h-1 bg-[#D4C2A4]/30 rounded-full animate-pulse delay-1500"></div>
      </div>

      {/* Premium Back to Home Icon */}
      <div className="absolute top-4 left-4 md:top-8 md:left-8 z-20">
        <Link
          to="/"
          className="group relative text-[#A9A9A9] hover:text-[#D4C2A4] transition-all duration-500 p-3 md:p-4 rounded-full hover:bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 hover:shadow-lg hover:shadow-[#D4C2A4]/20"
          aria-label="Back to Home"
        >
          <FiHome size={20} className="md:w-6 md:h-6 transition-transform duration-300 group-hover:scale-110" />
          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#D4C2A4]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </Link>
      </div>

      {/* Premium Main Content Container */}
      <main className="relative z-10">
        <div className="min-h-screen flex items-center justify-center py-8 md:py-12 px-4">
          <div className="w-full max-w-md mx-auto">
            {/* VIP Join Section */}
            <div className="text-center mb-8 md:mb-12">
              <div className="relative inline-block mb-4 md:mb-6">
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-['Cormorant_Garamond'] font-light text-[#F2EEE6] tracking-wide luxury-glow-text luxury-typography">
                  Join Us
                </h1>
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent luxury-border-glow"></div>
              </div>
              <p className="text-[#A9A9A9] font-['Open_Sans'] text-sm md:text-base tracking-wider uppercase opacity-80 luxury-typography">
                Begin your extraordinary safari journey
              </p>
              <div className="mt-4 flex justify-center space-x-2">
                <div className="w-2 h-2 bg-[#D4C2A4]/30 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-[#D4C2A4]/50 rounded-full animate-pulse delay-300"></div>
                <div className="w-2 h-2 bg-[#D4C2A4]/30 rounded-full animate-pulse delay-600"></div>
              </div>
            </div>

            <RegisterForm />

            {/* Premium Footer Quote */}
            <div className="text-center mt-8 md:mt-12">
              <p className="text-[#A9A9A9]/60 font-['Cormorant_Garamond'] text-lg md:text-xl italic tracking-wide">
                "Every great adventure begins with a single step"
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Register;

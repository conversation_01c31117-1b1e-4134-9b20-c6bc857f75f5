
import { useState, useEffect } from 'react';
import { FirebaseService } from '@/services/firebase';
import { BlogPost } from '@/types/firebase';

export const useBlogPosts = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadBlogPosts = async () => {
      try {
        setLoading(true);
        const data = await FirebaseService.getBlogPosts();
        const blogPosts = data.map((post: any) => ({
          id: post.id,
          title: post.title || 'Untitled Post',
          content: post.content || '',
          excerpt: post.excerpt || '',
          slug: post.slug || post.id,
          author: post.author || 'Admin',
          publishDate: post.publishDate || new Date(),
          tags: Array.isArray(post.tags) ? post.tags : [],
          category: post.category || 'Safari',
          featuredImage: post.featuredImage || '',
          status: post.status || 'published',
          readTime: post.readTime || 5,
          createdAt: post.createdAt,
          updatedAt: post.updatedAt
        })) as BlogPost[];
        
        // Filter only published posts for frontend
        const publishedPosts = blogPosts.filter(post => post.status === 'published');
        setPosts(publishedPosts);
      } catch (err) {
        setError('Failed to load blog posts');
        console.error('Error loading blog posts:', err);
      } finally {
        setLoading(false);
      }
    };

    loadBlogPosts();
  }, []);

  return { posts, loading, error };
};

export const useBlogPost = (slug: string) => {
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadBlogPost = async () => {
      try {
        setLoading(true);
        const data = await FirebaseService.getBlogPosts();
        const foundPost = data.find((p: any) => p.slug === slug || p.id === slug);
        
        if (foundPost) {
          const blogPost = {
            id: foundPost.id,
            title: foundPost.title || 'Untitled Post',
            content: foundPost.content || '',
            excerpt: foundPost.excerpt || '',
            slug: foundPost.slug || foundPost.id,
            author: foundPost.author || 'Admin',
            publishDate: foundPost.publishDate || new Date(),
            tags: Array.isArray(foundPost.tags) ? foundPost.tags : [],
            category: foundPost.category || 'Safari',
            featuredImage: foundPost.featuredImage || '',
            status: foundPost.status || 'published',
            readTime: foundPost.readTime || 5,
            createdAt: foundPost.createdAt,
            updatedAt: foundPost.updatedAt
          } as BlogPost;
          
          setPost(blogPost);
        } else {
          setError('Blog post not found');
        }
      } catch (err) {
        setError('Failed to load blog post');
        console.error('Error loading blog post:', err);
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      loadBlogPost();
    }
  }, [slug]);

  return { post, loading, error };
};

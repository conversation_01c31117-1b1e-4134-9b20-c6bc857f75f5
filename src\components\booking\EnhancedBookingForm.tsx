
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { CalendarIcon, Plus, Minus, User, CreditCard, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';
import { BookingService } from '@/services/bookingService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

interface BookingData {
  tourId: string;
  startDate: Date | null;
  groupSize: number;
  childrenCount: number;
  travelers: Array<{
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: Date | null;
    passportNumber: string;
    nationality: string;
  }>;
  accommodation: string;
  addOns: string[];
  specialRequests: string;
  totalPrice: number;
}

interface EnhancedBookingFormProps {
  tourId: string;
  currentStep: number;
  bookingData: BookingData;
  onDataChange: (field: keyof BookingData, value: any) => void;
  onNext: () => void;
  onPrev: () => void;
  customTour?: any;
}

const EnhancedBookingForm: React.FC<EnhancedBookingFormProps> = ({
  tourId,
  currentStep,
  bookingData,
  onDataChange,
  onNext,
  onPrev,
  customTour
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { currentUser } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const validateCurrentStep = () => {
    switch (currentStep) {
      case 1:
        if (!bookingData.startDate) {
          toast({
            title: "Start Date Required",
            description: "Please select a start date to continue.",
            variant: "destructive"
          });
          return false;
        }
        break;
      case 2:
        // Add traveler validation if needed
        break;
      case 3:
        // Add customization validation if needed
        break;
    }
    return true;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      onNext();
    }
  };

  const accommodationOptions = [
    { value: 'budget', label: 'Budget Camping', price: 0 },
    { value: 'midrange', label: 'Mid-range Lodge', price: 150 },
    { value: 'luxury', label: 'Luxury Safari Lodge', price: 400 },
  ];

  const addOnOptions = [
    { value: 'photography', label: 'Photography Guide', price: 75 },
    { value: 'cultural', label: 'Cultural Village Visit', price: 50 },
    { value: 'balloon', label: 'Hot Air Balloon', price: 550 },
    { value: 'night-drive', label: 'Night Game Drive', price: 100 },
  ];

  const updateTravelerInfo = (index: number, field: string, value: any) => {
    const updatedTravelers = [...bookingData.travelers];
    updatedTravelers[index] = { ...updatedTravelers[index], [field]: value };
    onDataChange('travelers', updatedTravelers);
  };

  const addTraveler = () => {
    const newTraveler = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: null,
      passportNumber: '',
      nationality: '',
    };
    onDataChange('travelers', [...bookingData.travelers, newTraveler]);
  };

  const removeTraveler = (index: number) => {
    const updatedTravelers = bookingData.travelers.filter((_, i) => i !== index);
    onDataChange('travelers', updatedTravelers);
  };

  const handleSubmit = async () => {
    if (!currentUser) {
      toast({
        title: "Authentication Required",
        description: "Please log in to complete your booking.",
        variant: "destructive"
      });
      return;
    }

    // Validate required fields
    if (!bookingData.startDate) {
      toast({
        title: "Start Date Required",
        description: "Please select a start date for your booking.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare booking data according to Firebase structure
      const bookingPayload = {
        tourId: tourId,
        startDate: bookingData.startDate ? format(bookingData.startDate, 'yyyy-MM-dd') : '',
        groupSize: bookingData.groupSize,
        childrenCount: bookingData.childrenCount,
        travelers: bookingData.travelers,
        accommodation: bookingData.accommodation,
        addOns: bookingData.addOns,
        specialRequests: bookingData.specialRequests,
        totalPrice: bookingData.totalPrice
      };

      // Create booking using BookingService
      const result = await BookingService.createBooking(
        bookingPayload,
        currentUser.uid,
        currentUser.email || ''
      );

      toast({
        title: "Booking Confirmed!",
        description: "Your booking has been successfully submitted. You will receive a confirmation email shortly."
      });

      // Navigate to user dashboard or booking confirmation page
      navigate('/user-dashboard');

    } catch (error) {
      console.error('Error creating booking:', error);
      toast({
        title: "Booking Failed",
        description: "There was an error processing your booking. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="backdrop-blur-xl bg-white/5 border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl shadow-2xl overflow-hidden">
            {/* Header - Mobile Optimized */}
            <div className="bg-gradient-to-r from-[#D4C2A4]/10 to-[#B8A082]/10 border-b border-[#D4C2A4]/20 p-4 sm:p-6">
              <div className="flex items-center gap-2 sm:gap-3">
                <CalendarIcon className="w-5 h-5 sm:w-6 sm:h-6 text-[#D4C2A4]" />
                <h3 className="font-cormorant text-xl sm:text-2xl font-semibold text-[#F2EEE6]">
                  Select Dates & Group Size
                </h3>
              </div>
            </div>

            {/* Content - Mobile Optimized */}
            <div className="p-4 sm:p-6 md:p-8 space-y-6 sm:space-y-8">
              {/* Start Date - Mobile Optimized */}
              <div className="space-y-2 sm:space-y-3">
                <label className="font-open-sans text-[#F2EEE6] font-medium flex items-center gap-2 text-sm sm:text-base">
                  Start Date
                  <span className="text-red-400">*</span>
                </label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal h-11 sm:h-12 bg-white/5 border-[#D4C2A4]/30 hover:bg-white/10 hover:border-[#D4C2A4]/50 text-[#F2EEE6] touch-target"
                    >
                      <CalendarIcon className="mr-2 sm:mr-3 h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4]" />
                      <span className="font-open-sans text-sm sm:text-base">
                        {bookingData.startDate ? format(bookingData.startDate, 'PPP') : 'Pick a date'}
                      </span>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0 bg-[#16191D] border-[#D4C2A4]/30">
                    <Calendar
                      mode="single"
                      selected={bookingData.startDate || undefined}
                      onSelect={(date) => onDataChange('startDate', date)}
                      initialFocus
                      className="text-[#F2EEE6]"
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Group Size - Mobile Optimized */}
              <div className="space-y-3 sm:space-y-4">
                <label className="font-open-sans text-[#F2EEE6] font-medium text-sm sm:text-base">Group Size (Adults)</label>
                <div className="flex items-center justify-center space-x-4 sm:space-x-6">
                  <Button
                    type="button"
                    variant="outline"
                    size="lg"
                    onClick={() => onDataChange('groupSize', Math.max(1, bookingData.groupSize - 1))}
                    disabled={bookingData.groupSize <= 1}
                    className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white/5 border-[#D4C2A4]/30 hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4]/50 disabled:opacity-30 touch-target"
                  >
                    <Minus className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4]" />
                  </Button>
                  <div className="bg-gradient-to-br from-[#D4C2A4]/20 to-[#B8A082]/20 border border-[#D4C2A4]/30 rounded-lg sm:rounded-xl px-4 sm:px-8 py-3 sm:py-4 min-w-[80px] sm:min-w-[100px] text-center">
                    <span className="font-cormorant text-2xl sm:text-3xl font-bold text-[#F2EEE6]">{bookingData.groupSize}</span>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="lg"
                    onClick={() => onDataChange('groupSize', Math.min(12, bookingData.groupSize + 1))}
                    disabled={bookingData.groupSize >= 12}
                    className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white/5 border-[#D4C2A4]/30 hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4]/50 disabled:opacity-30 touch-target"
                  >
                    <Plus className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4]" />
                  </Button>
                </div>
                <p className="font-open-sans text-xs sm:text-sm text-[#A9A9A9] text-center">Adults (18+ years)</p>
              </div>

              {/* Children Count - Mobile Optimized */}
              <div className="space-y-3 sm:space-y-4">
                <label className="font-open-sans text-[#F2EEE6] font-medium text-sm sm:text-base">Children</label>
                <div className="flex items-center justify-center space-x-4 sm:space-x-6">
                  <Button
                    type="button"
                    variant="outline"
                    size="lg"
                    onClick={() => onDataChange('childrenCount', Math.max(0, bookingData.childrenCount - 1))}
                    disabled={bookingData.childrenCount <= 0}
                    className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white/5 border-[#D4C2A4]/30 hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4]/50 disabled:opacity-30 touch-target"
                  >
                    <Minus className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4]" />
                  </Button>
                  <div className="bg-gradient-to-br from-[#D4C2A4]/20 to-[#B8A082]/20 border border-[#D4C2A4]/30 rounded-lg sm:rounded-xl px-4 sm:px-8 py-3 sm:py-4 min-w-[80px] sm:min-w-[100px] text-center">
                    <span className="font-cormorant text-2xl sm:text-3xl font-bold text-[#F2EEE6]">{bookingData.childrenCount}</span>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="lg"
                    onClick={() => onDataChange('childrenCount', Math.min(8, bookingData.childrenCount + 1))}
                    disabled={bookingData.childrenCount >= 8}
                    className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white/5 border-[#D4C2A4]/30 hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4]/50 disabled:opacity-30 touch-target"
                  >
                    <Plus className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4]" />
                  </Button>
                </div>
                <p className="font-open-sans text-xs sm:text-sm text-[#A9A9A9] text-center">Children (3-17 years) - 50% discount</p>
              </div>

              {/* Custom Tour Details */}
              {customTour && (
                <div className="bg-gradient-to-br from-[#D4C2A4]/10 to-transparent p-6 rounded-xl border border-[#D4C2A4]/20">
                  <h4 className="font-cormorant text-lg font-semibold text-[#D4C2A4] mb-3">Custom Tour Details</h4>
                  <div className="space-y-2">
                    <p className="font-open-sans text-[#F2EEE6]">Duration: <span className="text-[#D4C2A4]">{customTour.duration}</span></p>
                    <p className="font-open-sans text-[#F2EEE6]">Price: <span className="text-[#D4C2A4]">${customTour.price}</span></p>
                  </div>
                </div>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="backdrop-blur-xl bg-white/5 border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl shadow-2xl overflow-hidden">
            {/* Header - Mobile Optimized */}
            <div className="bg-gradient-to-r from-[#D4C2A4]/10 to-[#B8A082]/10 border-b border-[#D4C2A4]/20 p-4 sm:p-6">
              <div className="flex items-center gap-2 sm:gap-3">
                <User className="w-5 h-5 sm:w-6 sm:h-6 text-[#D4C2A4]" />
                <h3 className="font-cormorant text-xl sm:text-2xl font-semibold text-[#F2EEE6]">
                  Traveler Information
                </h3>
              </div>
            </div>

            {/* Content - Mobile Optimized */}
            <div className="p-4 sm:p-6 md:p-8 space-y-6 sm:space-y-8">
              {Array.from({ length: bookingData.groupSize }).map((_, index) => (
                <div key={index} className="bg-gradient-to-br from-white/5 to-transparent border border-[#D4C2A4]/20 rounded-lg sm:rounded-xl p-4 sm:p-6">
                  <div className="flex justify-between items-center mb-4 sm:mb-6">
                    <h4 className="font-cormorant text-lg sm:text-xl font-semibold text-[#F2EEE6] flex items-center gap-2">
                      <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-gradient-to-br from-[#D4C2A4] to-[#B8A082] flex items-center justify-center text-[#16191D] font-bold text-xs sm:text-sm">
                        {index + 1}
                      </div>
                      Traveler {index + 1}
                    </h4>
                    {index > 0 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeTraveler(index)}
                        className="bg-red-500/10 border-red-500/30 text-red-400 hover:bg-red-500/20 hover:border-red-500/50 text-xs sm:text-sm h-8 sm:h-9 px-2 sm:px-3 touch-target"
                      >
                        Remove
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                    <div className="space-y-2">
                      <label className="font-open-sans text-[#F2EEE6] font-medium text-xs sm:text-sm">First Name</label>
                      <Input
                        value={bookingData.travelers[index]?.firstName || ''}
                        onChange={(e) => updateTravelerInfo(index, 'firstName', e.target.value)}
                        placeholder="Enter first name"
                        className="bg-white/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#A9A9A9] focus:border-[#D4C2A4]/50 focus:ring-[#D4C2A4]/20 h-11 sm:h-12 text-sm sm:text-base no-zoom"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="font-open-sans text-[#F2EEE6] font-medium text-xs sm:text-sm">Last Name</label>
                      <Input
                        value={bookingData.travelers[index]?.lastName || ''}
                        onChange={(e) => updateTravelerInfo(index, 'lastName', e.target.value)}
                        placeholder="Enter last name"
                        className="bg-white/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#A9A9A9] focus:border-[#D4C2A4]/50 focus:ring-[#D4C2A4]/20 h-11 sm:h-12 text-sm sm:text-base no-zoom"
                      />
                    </div>
                    <div className="space-y-2 sm:col-span-2">
                      <label className="font-open-sans text-[#F2EEE6] font-medium text-xs sm:text-sm">Email</label>
                      <Input
                        type="email"
                        value={bookingData.travelers[index]?.email || ''}
                        onChange={(e) => updateTravelerInfo(index, 'email', e.target.value)}
                        placeholder="Enter email"
                        className="bg-white/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#A9A9A9] focus:border-[#D4C2A4]/50 focus:ring-[#D4C2A4]/20 h-11 sm:h-12 text-sm sm:text-base no-zoom"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="font-open-sans text-[#F2EEE6] font-medium text-sm">Phone</label>
                      <Input
                        value={bookingData.travelers[index]?.phone || ''}
                        onChange={(e) => updateTravelerInfo(index, 'phone', e.target.value)}
                        placeholder="Enter phone number"
                        className="bg-white/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#A9A9A9] focus:border-[#D4C2A4]/50 focus:ring-[#D4C2A4]/20 h-12"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="font-open-sans text-[#F2EEE6] font-medium text-sm">Passport Number</label>
                      <Input
                        value={bookingData.travelers[index]?.passportNumber || ''}
                        onChange={(e) => updateTravelerInfo(index, 'passportNumber', e.target.value)}
                        placeholder="Enter passport number"
                        className="bg-white/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#A9A9A9] focus:border-[#D4C2A4]/50 focus:ring-[#D4C2A4]/20 h-12"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="font-open-sans text-[#F2EEE6] font-medium text-sm">Nationality</label>
                      <Input
                        value={bookingData.travelers[index]?.nationality || ''}
                        onChange={(e) => updateTravelerInfo(index, 'nationality', e.target.value)}
                        placeholder="Enter nationality"
                        className="bg-white/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#A9A9A9] focus:border-[#D4C2A4]/50 focus:ring-[#D4C2A4]/20 h-12"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="backdrop-blur-xl bg-white/5 border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl shadow-2xl overflow-hidden">
            {/* Header - Mobile Optimized */}
            <div className="bg-gradient-to-r from-[#D4C2A4]/10 to-[#B8A082]/10 border-b border-[#D4C2A4]/20 p-4 sm:p-6">
              <div className="flex items-center gap-2 sm:gap-3">
                <CreditCard className="w-5 h-5 sm:w-6 sm:h-6 text-[#D4C2A4]" />
                <h3 className="font-cormorant text-xl sm:text-2xl font-semibold text-[#F2EEE6]">
                  Customize Your Experience
                </h3>
              </div>
            </div>

            {/* Content - Mobile Optimized */}
            <div className="p-4 sm:p-6 md:p-8 space-y-6 sm:space-y-8">
              {/* Accommodation Selection */}
              <div className="space-y-4">
                <label className="font-open-sans text-[#F2EEE6] font-medium text-lg">Accommodation</label>
                <Select value={bookingData.accommodation} onValueChange={(value) => onDataChange('accommodation', value)}>
                  <SelectTrigger className="h-12 bg-white/5 border-[#D4C2A4]/30 text-[#F2EEE6] focus:border-[#D4C2A4]/50 focus:ring-[#D4C2A4]/20">
                    <SelectValue placeholder="Select accommodation" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#16191D] border-[#D4C2A4]/30">
                    {accommodationOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value} className="text-[#F2EEE6] focus:bg-[#D4C2A4]/20">
                        <div className="flex justify-between items-center w-full">
                          <span>{option.label}</span>
                          {option.price > 0 && (
                            <span className="text-[#D4C2A4] ml-2">(+${option.price}/night)</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Add-ons */}
              <div className="space-y-4">
                <label className="font-open-sans text-[#F2EEE6] font-medium text-lg">Premium Add-ons</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {addOnOptions.map((addon) => (
                    <div key={addon.value} className="bg-gradient-to-br from-white/5 to-transparent border border-[#D4C2A4]/20 rounded-xl p-4 hover:border-[#D4C2A4]/40 transition-all duration-300">
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id={addon.value}
                          checked={bookingData.addOns.includes(addon.value)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              onDataChange('addOns', [...bookingData.addOns, addon.value]);
                            } else {
                              onDataChange('addOns', bookingData.addOns.filter(a => a !== addon.value));
                            }
                          }}
                          className="mt-1 border-[#D4C2A4]/50 data-[state=checked]:bg-[#D4C2A4] data-[state=checked]:border-[#D4C2A4]"
                        />
                        <div className="flex-1">
                          <label htmlFor={addon.value} className="font-open-sans text-[#F2EEE6] font-medium cursor-pointer">
                            {addon.label}
                          </label>
                          <p className="font-cormorant text-[#D4C2A4] font-semibold text-lg">+${addon.price}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Special Requests */}
              <div className="space-y-4">
                <label htmlFor="specialRequests" className="font-open-sans text-[#F2EEE6] font-medium text-lg">Special Requests</label>
                <Textarea
                  id="specialRequests"
                  value={bookingData.specialRequests}
                  onChange={(e) => onDataChange('specialRequests', e.target.value)}
                  placeholder="Any special dietary requirements, accessibility needs, or other requests..."
                  rows={4}
                  className="bg-white/5 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#A9A9A9] focus:border-[#D4C2A4]/50 focus:ring-[#D4C2A4]/20 resize-none"
                />
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="backdrop-blur-xl bg-white/5 border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl shadow-2xl overflow-hidden">
            {/* Header - Mobile Optimized */}
            <div className="bg-gradient-to-r from-[#D4C2A4]/10 to-[#B8A082]/10 border-b border-[#D4C2A4]/20 p-4 sm:p-6">
              <div className="flex items-center gap-2 sm:gap-3">
                <CreditCard className="w-5 h-5 sm:w-6 sm:h-6 text-[#D4C2A4]" />
                <h3 className="font-cormorant text-xl sm:text-2xl font-semibold text-[#F2EEE6]">
                  Payment & Confirmation
                </h3>
              </div>
            </div>

            {/* Content - Mobile Optimized */}
            <div className="p-4 sm:p-6 md:p-8 space-y-6 sm:space-y-8">
              {/* Booking Summary - Mobile Optimized */}
              <div className="bg-gradient-to-br from-green-500/10 to-transparent border border-green-500/20 rounded-lg sm:rounded-xl p-4 sm:p-6">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                  <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-green-400" />
                  <h4 className="font-cormorant text-lg sm:text-xl font-semibold text-green-400">Booking Summary</h4>
                </div>
                <div className="space-y-2 sm:space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="font-open-sans text-xs sm:text-sm text-[#A9A9A9]">Tour:</span>
                    <span className="font-open-sans text-xs sm:text-sm text-[#F2EEE6] font-medium text-right">
                      {customTour ? customTour.title || 'Custom Safari' : 'Safari Adventure'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-open-sans text-xs sm:text-sm text-[#A9A9A9]">Date:</span>
                    <span className="font-open-sans text-xs sm:text-sm text-[#F2EEE6] font-medium text-right">
                      {bookingData.startDate ? format(bookingData.startDate, 'PPP') : 'Not selected'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-open-sans text-xs sm:text-sm text-[#A9A9A9]">Group Size:</span>
                    <span className="font-open-sans text-xs sm:text-sm text-[#F2EEE6] font-medium">
                      {bookingData.groupSize} travelers
                    </span>
                  </div>
                  <div className="border-t border-green-500/20 pt-2 sm:pt-3 mt-2 sm:mt-3">
                    <div className="flex justify-between items-center">
                      <span className="font-cormorant text-base sm:text-lg font-semibold text-green-400">Total Price:</span>
                      <span className="font-cormorant text-xl sm:text-2xl font-bold text-green-400">
                        ${bookingData.totalPrice.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Complete Booking Button - Mobile Optimized */}
              <div className="text-center">
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="w-full h-12 sm:h-14 bg-gradient-to-r from-[#D4C2A4] to-[#B8A082] hover:from-[#B8A082] hover:to-[#D4C2A4] text-[#16191D] font-cormorant text-base sm:text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl touch-target"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2 sm:gap-3">
                      <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-[#16191D]/30 border-t-[#16191D] rounded-full animate-spin"></div>
                      Processing...
                    </div>
                  ) : (
                    'Complete Booking'
                  )}
                </Button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6 sm:space-y-8">
      {renderStep()}

      {/* Luxury Navigation - Mobile Optimized */}
      <div className="flex justify-between items-center pt-4 sm:pt-6 gap-3 sm:gap-4">
        <Button
          onClick={onPrev}
          disabled={currentStep === 1}
          variant="outline"
          className="h-11 sm:h-12 px-4 sm:px-8 bg-white/5 border-[#D4C2A4]/30 text-[#F2EEE6] hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4]/50 disabled:opacity-30 disabled:cursor-not-allowed font-open-sans text-sm sm:text-base touch-target"
        >
          Previous
        </Button>

        {currentStep < 4 && (
          <Button
            onClick={handleNext}
            className="h-11 sm:h-12 px-4 sm:px-8 bg-gradient-to-r from-[#D4C2A4] to-[#B8A082] hover:from-[#B8A082] hover:to-[#D4C2A4] text-[#16191D] font-cormorant font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-sm sm:text-base touch-target"
          >
            Next Step
          </Button>
        )}
      </div>
    </div>
  );
};

export default EnhancedBookingForm;

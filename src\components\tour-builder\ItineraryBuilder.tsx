
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { GripVertical, MapPin, Calendar } from 'lucide-react';

interface ItineraryDay {
  day: number;
  destination: string;
  activities: string[];
  accommodation: string;
}

interface ItineraryBuilderProps {
  destinations: string[];
  activities: string[];
  duration: number;
  itinerary: ItineraryDay[];
  onItineraryChange: (itinerary: ItineraryDay[]) => void;
}

const ItineraryBuilder: React.FC<ItineraryBuilderProps> = ({
  destinations,
  activities,
  duration,
  itinerary,
  onItineraryChange
}) => {
  const [localItinerary, setLocalItinerary] = useState<ItineraryDay[]>([]);

  // Initialize itinerary when component mounts or props change
  useEffect(() => {
    if (itinerary.length === 0) {
      const newItinerary: ItineraryDay[] = [];
      for (let i = 1; i <= duration; i++) {
        newItinerary.push({
          day: i,
          destination: destinations[0] || '',
          activities: [],
          accommodation: ''
        });
      }
      setLocalItinerary(newItinerary);
      onItineraryChange(newItinerary);
    } else {
      setLocalItinerary(itinerary);
    }
  }, [duration, destinations, itinerary.length]);

  const updateDay = (dayIndex: number, field: keyof ItineraryDay, value: any) => {
    const updated = [...localItinerary];
    updated[dayIndex] = { ...updated[dayIndex], [field]: value };
    setLocalItinerary(updated);
    onItineraryChange(updated);
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(localItinerary);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update day numbers
    const updatedItems = items.map((item, index) => ({
      ...item,
      day: index + 1
    }));

    setLocalItinerary(updatedItems);
    onItineraryChange(updatedItems);
  };

  const destinationOptions = [
    { id: 'serengeti', name: 'Serengeti National Park' },
    { id: 'ngorongoro', name: 'Ngorongoro Crater' },
    { id: 'tarangire', name: 'Tarangire National Park' },
    { id: 'manyara', name: 'Lake Manyara' }
  ];

  const activityOptions = [
    { id: 'game-drives', name: 'Game Drives' },
    { id: 'photography-tour', name: 'Photography Workshop' },
    { id: 'cultural-visit', name: 'Cultural Village Visit' },
    { id: 'walking-safari', name: 'Walking Safari' },
    { id: 'balloon-safari', name: 'Hot Air Balloon Safari' },
    { id: 'conservation-tour', name: 'Conservation Experience' }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-4">Build Your Day-by-Day Itinerary</h2>
        <p className="text-gray-600">
          Drag and drop to reorder days, and customize each day's activities and destinations
        </p>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="itinerary">
          {(provided) => (
            <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
              {localItinerary.map((day, index) => (
                <Draggable key={`day-${day.day}`} draggableId={`day-${day.day}`} index={index}>
                  {(provided, snapshot) => (
                    <Card 
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={`${snapshot.isDragging ? 'shadow-lg' : ''}`}
                    >
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center space-x-3">
                          <div {...provided.dragHandleProps} className="cursor-grab">
                            <GripVertical className="h-5 w-5 text-gray-400" />
                          </div>
                          <Calendar className="h-5 w-5 text-orange-600" />
                          <span>Day {day.day}</span>
                        </CardTitle>
                      </CardHeader>
                      
                      <CardContent className="space-y-4">
                        {/* Destination Selection */}
                        <div>
                          <label className="block text-sm font-medium mb-2">Destination</label>
                          <Select 
                            value={day.destination} 
                            onValueChange={(value) => updateDay(index, 'destination', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select destination" />
                            </SelectTrigger>
                            <SelectContent>
                              {destinationOptions
                                .filter(dest => destinations.includes(dest.id))
                                .map((dest) => (
                                <SelectItem key={dest.id} value={dest.id}>
                                  <div className="flex items-center">
                                    <MapPin className="h-4 w-4 mr-2" />
                                    {dest.name}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Activities Selection */}
                        <div>
                          <label className="block text-sm font-medium mb-2">Activities</label>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {activityOptions
                              .filter(activity => activities.includes(activity.id))
                              .map((activity) => (
                              <Button
                                key={activity.id}
                                variant={day.activities.includes(activity.id) ? "default" : "outline"}
                                size="sm"
                                onClick={() => {
                                  const currentActivities = day.activities;
                                  const newActivities = currentActivities.includes(activity.id)
                                    ? currentActivities.filter(id => id !== activity.id)
                                    : [...currentActivities, activity.id];
                                  updateDay(index, 'activities', newActivities);
                                }}
                                className="text-xs"
                              >
                                {activity.name}
                              </Button>
                            ))}
                          </div>
                          
                          {day.activities.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-1">
                              {day.activities.map((activityId) => {
                                const activity = activityOptions.find(a => a.id === activityId);
                                return activity ? (
                                  <Badge key={activityId} variant="secondary" className="text-xs">
                                    {activity.name}
                                  </Badge>
                                ) : null;
                              })}
                            </div>
                          )}
                        </div>

                        {/* Display selected destination */}
                        {day.destination && (
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <div className="flex items-center text-sm text-gray-600">
                              <MapPin className="h-4 w-4 mr-2" />
                              {destinationOptions.find(d => d.id === day.destination)?.name}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Itinerary Summary */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-800">Itinerary Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {localItinerary.map((day) => (
              <div key={day.day} className="flex items-center justify-between text-sm">
                <span className="font-medium">Day {day.day}:</span>
                <span className="text-gray-600">
                  {destinationOptions.find(d => d.id === day.destination)?.name || 'No destination'}
                  {day.activities.length > 0 && ` (${day.activities.length} activities)`}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ItineraryBuilder;


import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: {
				DEFAULT: '1rem',
				sm: '1.5rem',
				lg: '2rem',
				xl: '2rem',
				'2xl': '2rem',
			},
			screens: {
				'sm': '640px',
				'md': '768px',
				'lg': '1024px',
				'xl': '1280px',
				'2xl': '1400px'
			}
		},
		screens: {
			'xs': '475px',
			'sm': '640px',
			'md': '768px',
			'lg': '1024px',
			'xl': '1280px',
			'2xl': '1536px',
		},
		extend: {
			 fontFamily: {
        bubblegum: ['Bubblegum Sans', 'cursive'],
        cormorant: ['Cormorant Garamond', 'serif'],
        'open-sans': ['Open Sans', 'sans-serif'],
        'playfair': ['Playfair Display', 'serif'],
        'montserrat': ['Montserrat', 'sans-serif'],
        sans: ['Noto Sans', 'sans-serif'],
        cinzel: ['Cinzel', 'serif'],
      },
			colors: {
				 'dark': '#16191D',
        'intro-bg': '#F2EEE6',
        'slide1-bg': '#D4C2A4',
        'slide2-bg': '#E8DCC6',
        'slide3-bg': '#F2EEE6',
        'slide4-bg': '#D4C2A4',
        'slide5-bg': '#E8DCC6',
        'slide6-bg': '#F2EEE6',
        'footer-bg': '#16191D',
        'slide1-bg-t': 'rgba(212, 194, 164, 0.9)',
        'slide2-bg-t': 'rgba(232, 220, 198, 0.9)',
        'slide3-bg-t': 'rgba(242, 238, 230, 0.9)',
        'slide4-bg-t': 'rgba(212, 194, 164, 0.9)',
        'slide5-bg-t': 'rgba(232, 220, 198, 0.9)',
        'slide6-bg-t': 'rgba(242, 238, 230, 0.9)',
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				// Luxury Color Palette
				'deep-rust': 'hsl(var(--deep-rust))', // #D4C2A4 - Luxury gold primary
				'warm-orange': 'hsl(var(--warm-orange))', // Luxury warm gold variant
				'golden-orange': 'hsl(var(--golden-orange))', // #D4C2A4 - Luxury gold accent
				'warm-cream': 'hsl(var(--warm-cream))', // #F2EEE6 - Luxury cream background
				'light-warm-gray': 'hsl(var(--light-warm-gray))', // Luxury light card backgrounds
				'deep-brown': 'hsl(var(--deep-brown))', // #16191D - Luxury deep charcoal
				'dark-brown': 'hsl(var(--dark-brown))', // Luxury primary text
				'medium-brown': 'hsl(var(--medium-brown))', // Luxury secondary text
				'bright-orange': 'hsl(var(--bright-orange))', // Luxury bright gold highlights
				'golden-yellow': 'hsl(var(--golden-yellow))', // Luxury golden yellow accents
				'warm-red': 'hsl(var(--warm-red))', // Luxury deep red emphasis

				// Legacy safari colors for backward compatibility
				safari: {
					sunset: 'hsl(var(--deep-rust))', // Map to deep-rust
					'golden-sun': 'hsl(var(--golden-yellow))', // Map to golden-yellow
					'deep-brown': 'hsl(var(--deep-brown))', // Keep same
					'warm-amber': 'hsl(var(--warm-orange))', // Map to warm-orange
					'burnt-sienna': 'hsl(var(--medium-brown))', // Map to medium-brown
					'dusty-rose': 'hsl(var(--light-warm-gray))', // Map to light-warm-gray
					'savanna-gold': 'hsl(var(--golden-orange))', // Map to golden-orange
					'coffee-brown': 'hsl(var(--medium-brown))', // Map to medium-brown
					'bright-orange': 'hsl(var(--bright-orange))', // Keep same
					'golden-yellow': 'hsl(var(--golden-yellow))', // Keep same
					'deep-rust': 'hsl(var(--deep-rust))', // Keep same
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			fontSize: {
				'2xs': ['0.625rem', { lineHeight: '0.75rem' }],
				'5vw': '5vw',
				'6vw': '6vw',
				'8vw': '8vw',
        '11vw': '11vw',
        '12vw': '12vw',
        '18vw': '18vw',
        '25vw': '25vw',
			},
			letterSpacing: {
				'tightest-vw': '-2.3vw',
				'tighter-vw': '-0.8vw',
			},
			lineHeight: {
				'tighter-vw': '0.9',
			},
			spacing: {
				'safe-top': 'env(safe-area-inset-top)',
				'safe-bottom': 'env(safe-area-inset-bottom)',
				'safe-left': 'env(safe-area-inset-left)',
				'safe-right': 'env(safe-area-inset-right)',
				'4vw': '4vw',
				'5vw': '5vw',
        '6vw': '6vw',
        '8vw': '8vw',
        '10vw': '10vw',
			},
			minHeight: {
				'screen-safe': 'calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom))',
			},
			maxWidth: {
				'8xl': '88rem',
				'9xl': '96rem',
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					'0%': {
						opacity: '0',
						transform: 'translateY(10px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'slide-in-right': {
					'0%': {
						transform: 'translateX(100%)'
					},
					'100%': {
						transform: 'translateX(0)'
					}
				},
				'slide-in-left': {
					'0%': {
						transform: 'translateX(-100%)'
					},
					'100%': {
						transform: 'translateX(0)'
					}
				},
				'slide-in-up': {
					'0%': {
						transform: 'translateY(100%)',
						opacity: '0'
					},
					'100%': {
						transform: 'translateY(0)',
						opacity: '1'
					}
				},
				'scale-in': {
					'0%': {
						transform: 'scale(0.9)',
						opacity: '0'
					},
					'100%': {
						transform: 'scale(1)',
						opacity: '1'
					}
				},
				'bounce-in': {
					'0%': {
						transform: 'scale(0.3)',
						opacity: '0'
					},
					'50%': {
						transform: 'scale(1.05)'
					},
					'70%': {
						transform: 'scale(0.9)'
					},
					'100%': {
						transform: 'scale(1)',
						opacity: '1'
					}
				},
				'marquee': {
					'0%': { transform: 'translateX(0)' },
					'100%': { transform: 'translateX(calc(-100%))' }
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.5s ease-out',
				'slide-in-right': 'slide-in-right 0.3s ease-out',
				'slide-in-left': 'slide-in-left 0.3s ease-out',
				'slide-in-up': 'slide-in-up 0.4s ease-out',
				'scale-in': 'scale-in 0.3s ease-out',
				'bounce-in': 'bounce-in 0.6s ease-out',
				'marquee': 'marquee var(--duration) linear infinite',
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;

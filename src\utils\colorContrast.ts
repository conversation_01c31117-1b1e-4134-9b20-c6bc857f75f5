// Color contrast utilities for WCAG AA compliance

// Convert HSL to RGB
function hslToRgb(h: number, s: number, l: number): [number, number, number] {
  h /= 360;
  s /= 100;
  l /= 100;

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h * 6) % 2 - 1));
  const m = l - c / 2;

  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 1/6) {
    r = c; g = x; b = 0;
  } else if (1/6 <= h && h < 1/3) {
    r = x; g = c; b = 0;
  } else if (1/3 <= h && h < 1/2) {
    r = 0; g = c; b = x;
  } else if (1/2 <= h && h < 2/3) {
    r = 0; g = x; b = c;
  } else if (2/3 <= h && h < 5/6) {
    r = x; g = 0; b = c;
  } else if (5/6 <= h && h < 1) {
    r = c; g = 0; b = x;
  }

  return [
    Math.round((r + m) * 255),
    Math.round((g + m) * 255),
    Math.round((b + m) * 255)
  ];
}

// Calculate relative luminance
function getLuminance(r: number, g: number, b: number): number {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

// Calculate contrast ratio between two colors
export function getContrastRatio(color1: [number, number, number], color2: [number, number, number]): number {
  const lum1 = getLuminance(...color1);
  const lum2 = getLuminance(...color2);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
}

// Check if contrast meets WCAG standards
export function meetsWCAGAA(contrastRatio: number, isLargeText: boolean = false): boolean {
  return contrastRatio >= (isLargeText ? 3 : 4.5);
}

export function meetsWCAGAAA(contrastRatio: number, isLargeText: boolean = false): boolean {
  return contrastRatio >= (isLargeText ? 4.5 : 7);
}

// Parse CSS HSL color to RGB
export function parseHSLToRGB(hslString: string): [number, number, number] | null {
  const match = hslString.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
  if (!match) return null;
  
  const [, h, s, l] = match.map(Number);
  return hslToRgb(h, s, l);
}

// Get color from CSS custom property
export function getCSSCustomProperty(property: string): string {
  if (typeof window === 'undefined') return '';
  return getComputedStyle(document.documentElement).getPropertyValue(property).trim();
}

// Validate color contrast for our theme
export function validateThemeContrast() {
  const results: Array<{
    name: string;
    foreground: string;
    background: string;
    ratio: number;
    passesAA: boolean;
    passesAAA: boolean;
  }> = [];

  // Define color combinations to test
  const colorTests = [
    {
      name: 'Primary text on background',
      foreground: '--foreground',
      background: '--background'
    },
    {
      name: 'Primary button text',
      foreground: '--primary-foreground',
      background: '--primary'
    },
    {
      name: 'Secondary button text',
      foreground: '--secondary-foreground',
      background: '--secondary'
    },
    {
      name: 'Muted text',
      foreground: '--muted-foreground',
      background: '--background'
    },
    {
      name: 'Card text',
      foreground: '--card-foreground',
      background: '--card'
    },
    {
      name: 'Destructive button text',
      foreground: '--destructive-foreground',
      background: '--destructive'
    }
  ];

  colorTests.forEach(test => {
    const fgHSL = getCSSCustomProperty(test.foreground);
    const bgHSL = getCSSCustomProperty(test.background);
    
    const fgRGB = parseHSLToRGB(`hsl(${fgHSL})`);
    const bgRGB = parseHSLToRGB(`hsl(${bgHSL})`);
    
    if (fgRGB && bgRGB) {
      const ratio = getContrastRatio(fgRGB, bgRGB);
      results.push({
        name: test.name,
        foreground: `hsl(${fgHSL})`,
        background: `hsl(${bgHSL})`,
        ratio: Math.round(ratio * 100) / 100,
        passesAA: meetsWCAGAA(ratio),
        passesAAA: meetsWCAGAAA(ratio)
      });
    }
  });

  return results;
}

// Generate accessible color variations
export function generateAccessibleColor(
  baseColor: [number, number, number],
  targetBackground: [number, number, number],
  targetRatio: number = 4.5
): [number, number, number] {
  const baseLum = getLuminance(...baseColor);
  const bgLum = getLuminance(...targetBackground);
  
  // Determine if we need to make the color lighter or darker
  const needsLighter = baseLum < bgLum;
  
  let [r, g, b] = baseColor;
  let currentRatio = getContrastRatio([r, g, b], targetBackground);
  
  // Adjust brightness until we meet the target ratio
  const step = needsLighter ? 5 : -5;
  const limit = needsLighter ? 255 : 0;
  
  while (currentRatio < targetRatio && (needsLighter ? r < limit : r > limit)) {
    r = Math.max(0, Math.min(255, r + step));
    g = Math.max(0, Math.min(255, g + step));
    b = Math.max(0, Math.min(255, b + step));
    
    currentRatio = getContrastRatio([r, g, b], targetBackground);
  }
  
  return [r, g, b];
}

// Convert RGB to HSL
export function rgbToHsl(r: number, g: number, b: number): [number, number, number] {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s = 0, l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return [Math.round(h * 360), Math.round(s * 100), Math.round(l * 100)];
}

// Generate CSS custom property for accessible color
export function generateAccessibleCSSColor(
  baseHSL: string,
  targetBackgroundHSL: string,
  targetRatio: number = 4.5
): string {
  const baseRGB = parseHSLToRGB(`hsl(${baseHSL})`);
  const bgRGB = parseHSLToRGB(`hsl(${targetBackgroundHSL})`);
  
  if (!baseRGB || !bgRGB) return baseHSL;
  
  const accessibleRGB = generateAccessibleColor(baseRGB, bgRGB, targetRatio);
  const [h, s, l] = rgbToHsl(...accessibleRGB);
  
  return `${h} ${s}% ${l}%`;
}

// Accessibility color recommendations for our theme
export const accessibleColors = {
  // Improved colors that meet WCAG AA standards
  lightTheme: {
    // Primary text should be very dark for good contrast
    foreground: '25 60% 8%', // Very dark brown
    // Muted text should still be readable
    mutedForeground: '25 50% 25%', // Medium dark brown
    // Primary button should have good contrast
    primary: '16 65% 42%', // Slightly darker rust
    // Secondary colors adjusted for better contrast
    secondary: '28 75% 47%', // Darker warm orange
    destructive: '14 85% 42%', // Darker warm red
  },
  darkTheme: {
    // Light text on dark background
    foreground: '48 85% 96%', // Warm cream
    // Muted text should be lighter in dark theme
    mutedForeground: '48 60% 80%', // Light warm text
    // Primary button can be lighter in dark theme
    primary: '16 65% 57%', // Lighter rust
    // Secondary colors adjusted for dark theme
    secondary: '28 75% 62%', // Lighter warm orange
    destructive: '14 85% 57%', // Lighter warm red
  }
};

// Apply accessible colors to CSS custom properties
export function applyAccessibleColors(theme: 'light' | 'dark' = 'light') {
  if (typeof document === 'undefined') return;
  
  const colors = accessibleColors[theme];
  const root = document.documentElement;
  
  Object.entries(colors).forEach(([property, value]) => {
    root.style.setProperty(`--${property}`, value);
  });
}

// Check if user prefers high contrast
export function prefersHighContrast(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-contrast: high)').matches;
}

// Apply high contrast mode
export function applyHighContrastMode() {
  if (typeof document === 'undefined') return;
  
  const root = document.documentElement;
  
  // High contrast colors
  const highContrastColors = {
    foreground: '0 0% 0%', // Pure black
    background: '0 0% 100%', // Pure white
    primary: '0 0% 0%', // Black buttons
    primaryForeground: '0 0% 100%', // White text on black
    border: '0 0% 0%', // Black borders
    mutedForeground: '0 0% 20%', // Dark gray
  };
  
  Object.entries(highContrastColors).forEach(([property, value]) => {
    root.style.setProperty(`--${property}`, value);
  });
  
  // Add high contrast class
  document.body.classList.add('high-contrast');
}

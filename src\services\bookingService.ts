
import { FirebaseService } from './firebase';
import { Booking, Tour, UserProfile } from '@/types/firebase';
import { Timestamp } from 'firebase/firestore';
import { EmailService, BookingEmailData } from './emailService';

export class BookingService {
  // Create a new booking
  static async createBooking(bookingData: any, userId: string, userEmail: string) {
    try {
      console.log('Creating booking:', bookingData);
      
      // Get tour details if it's not a custom tour
      let tourTitle = 'Custom Safari Tour';
      let tourPrice = bookingData.totalPrice;
      
      if (!bookingData.tourId?.startsWith('custom-')) {
        const tour = await FirebaseService.getTour(bookingData.tourId);
        if (tour) {
          tourTitle = tour.title;
          tourPrice = tour.price;
        }
      }

      // Calculate pricing
      const accommodationPrices = {
        budget: 0,
        midrange: 150,
        luxury: 400,
      };
      
      const addOnPrices = {
        photography: 75,
        cultural: 50,
        balloon: 550,
        'night-drive': 100,
      };

      const basePrice = tourPrice;
      const accommodationPrice = accommodationPrices[bookingData.accommodation as keyof typeof accommodationPrices] || 0;
      const addOnsPrice = bookingData.addOns.reduce((total: number, addon: string) =>
        total + (addOnPrices[addon as keyof typeof addOnPrices] || 0), 0
      );

      // Calculate pricing for adults and children separately
      const adultPrice = (basePrice + accommodationPrice) * bookingData.groupSize;
      const childrenPrice = (basePrice + accommodationPrice) * (bookingData.childrenCount || 0) * 0.5; // 50% discount for children
      const totalPrice = adultPrice + childrenPrice + addOnsPrice;
      const depositAmount = Math.round(totalPrice * 0.3); // 30% deposit

      const booking: Omit<Booking, 'id'> = {
        userId,
        tourId: bookingData.tourId,
        tourTitle,
        customerInfo: {
          firstName: bookingData.travelers[0]?.firstName || '',
          lastName: bookingData.travelers[0]?.lastName || '',
          email: userEmail,
          phone: bookingData.travelers[0]?.phone || '',
          country: bookingData.travelers[0]?.nationality || '',
          passportNumber: bookingData.travelers[0]?.passportNumber || '',
          emergencyContact: {
            name: '',
            phone: '',
            relationship: ''
          }
        },
        bookingDetails: {
          startDate: typeof bookingData.startDate === 'string' ? bookingData.startDate : (bookingData.startDate?.toISOString().split('T')[0] || ''),
          endDate: this.calculateEndDate(typeof bookingData.startDate === 'string' ? new Date(bookingData.startDate) : bookingData.startDate, tourTitle),
          participants: bookingData.groupSize,
          childrenCount: bookingData.childrenCount || 0,
          accommodationType: bookingData.accommodation,
          specialRequests: bookingData.specialRequests || '',
          dietaryRestrictions: []
        },
        pricing: {
          basePrice: tourPrice,
          participantCount: bookingData.groupSize,
          subtotal: totalPrice,
          taxes: 0,
          totalAmount: totalPrice,
          currency: 'USD'
        },
        paymentInfo: {
          paymentMethod: 'pending',
          paymentStatus: 'pending' as const,
          amountPaid: 0
        },
        status: 'pending' as const,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      const result = await FirebaseService.createBooking(booking);

      // Create initial payment transaction
      await this.createPaymentTransaction(result.id, totalPrice, depositAmount, 'deposit');

      // Send confirmation notification
      await this.sendBookingConfirmationNotification(userId, result.id, tourTitle);

      // Send email notification to admin
      await this.sendBookingEmailNotification(booking, result.id, tourTitle, userEmail);

      return result;
    } catch (error) {
      console.error('Error creating booking:', error);
      throw error;
    }
  }

  // Calculate end date based on tour duration
  private static calculateEndDate(startDate: Date | string | null, tourTitle: string): string {
    if (!startDate || startDate === '') return '';

    // Extract days from tour title or default to 7 days
    const daysMatch = tourTitle.match(/(\d+)\s*days?/i);
    const duration = daysMatch ? parseInt(daysMatch[1]) : 7;

    const date = typeof startDate === 'string' ? new Date(startDate) : startDate;

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid start date provided:', startDate);
      return '';
    }

    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + duration - 1);

    return endDate.toISOString().split('T')[0];
  }

  // Create payment transaction
  private static async createPaymentTransaction(bookingId: string, totalAmount: number, amount: number, type: 'deposit' | 'balance') {
    try {
      const transaction = {
        bookingId,
        userId: '', // Will be set by the booking system
        amount,
        currency: 'USD',
        type,
        status: 'pending' as const,
        paymentMethod: 'credit_card',
        transactionId: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        description: `${type === 'deposit' ? 'Deposit' : 'Balance'} payment for booking ${bookingId}`
      };

      return await FirebaseService.createPaymentTransaction(transaction);
    } catch (error) {
      console.error('Error creating payment transaction:', error);
      throw error;
    }
  }

  // Send booking confirmation notification
  private static async sendBookingConfirmationNotification(userId: string, bookingId: string, tourTitle: string) {
    try {
      const notification = {
        userId,
        title: 'Booking Confirmation',
        message: `Your booking for "${tourTitle}" has been received and is being processed.`,
        type: 'booking' as const,
        read: false,
        priority: 'medium' as const,
        actionUrl: `/dashboard`,
        data: { bookingId },
        createdAt: Timestamp.now()
      };

      return await FirebaseService.createNotification(notification);
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  // Send booking email notification to admin
  private static async sendBookingEmailNotification(
    booking: any,
    bookingId: string,
    tourTitle: string,
    userEmail: string
  ) {
    try {
      // Get customer name from travelers or use email
      const customerName = booking.bookingDetails.travelers?.[0]?.name || userEmail.split('@')[0];

      const emailData: BookingEmailData = {
        tourTitle,
        customerName,
        customerEmail: userEmail,
        customerPhone: booking.bookingDetails.travelers?.[0]?.phone,
        startDate: booking.bookingDetails.startDate,
        groupSize: booking.bookingDetails.groupSize,
        childrenCount: booking.bookingDetails.childrenCount || 0,
        accommodation: booking.bookingDetails.accommodation,
        totalPrice: booking.paymentInfo.totalAmount,
        specialRequests: booking.bookingDetails.specialRequests,
        bookingId,
        travelers: booking.bookingDetails.travelers || [],
        addOns: booking.bookingDetails.addOns || []
      };

      await EmailService.sendBookingNotification(emailData);
      console.log('✅ Booking email notification sent successfully');
    } catch (error) {
      console.error('❌ Error sending booking email notification:', error);
      // Don't throw error to avoid breaking the booking process
    }
  }

  // Update booking status
  static async updateBookingStatus(bookingId: string, status: Booking['status']) {
    try {
      return await FirebaseService.updateBooking(bookingId, { status });
    } catch (error) {
      console.error('Error updating booking status:', error);
      throw error;
    }
  }

  // Process payment
  static async processPayment(bookingId: string, amount: number, paymentMethod: string) {
    try {
      // Simulate payment processing
      const isSuccessful = Math.random() > 0.1; // 90% success rate for demo
      
      if (isSuccessful) {
        // Get current booking to update payment info
        const currentBooking = await FirebaseService.getBooking(bookingId);
        if (currentBooking) {
          await FirebaseService.updateBooking(bookingId, {
            paymentInfo: {
              ...currentBooking.paymentInfo,
              paymentStatus: 'completed',
              amountPaid: amount
            },
            status: 'confirmed'
          });
        }

        // Create successful payment transaction
        await this.createPaymentTransaction(bookingId, amount, amount, 'balance');
        
        return { success: true, transactionId: `txn_${Date.now()}` };
      } else {
        throw new Error('Payment failed');
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  }

  // Get user bookings
  static async getUserBookings(userId: string) {
    try {
      return await FirebaseService.getBookings(userId);
    } catch (error) {
      console.error('Error getting user bookings:', error);
      throw error;
    }
  }

  // Cancel booking
  static async cancelBooking(bookingId: string, reason: string) {
    try {
      // Get current booking to update with cancellation reason
      const currentBooking = await FirebaseService.getBooking(bookingId);
      if (currentBooking) {
        await FirebaseService.updateBooking(bookingId, {
          status: 'cancelled',
          bookingDetails: {
            ...currentBooking.bookingDetails,
            specialRequests: `${currentBooking.bookingDetails.specialRequests || ''}\nCancellation reason: ${reason}`.trim()
          }
        });

        // Create refund transaction if payment was made
        if (currentBooking.paymentInfo.amountPaid > 0) {
          await this.createPaymentTransaction(bookingId, currentBooking.paymentInfo.amountPaid, currentBooking.paymentInfo.amountPaid, 'balance');
        }
      }

      return true;
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw error;
    }
  }
}

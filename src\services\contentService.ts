
import { FirebaseService } from './firebase';

export class ContentService {
  // Blog posts
  static async getBlogPosts(published: boolean = true) {
    try {
      return await FirebaseService.getBlogPosts(published);
    } catch (error) {
      console.error('Error getting blog posts:', error);
      throw error;
    }
  }

  // Travel guides
  static async getTravelGuides(category?: string) {
    try {
      return await FirebaseService.getTravelGuides(category);
    } catch (error) {
      console.error('Error getting travel guides:', error);
      throw error;
    }
  }

  // Destinations
  static async getDestinations() {
    try {
      return await FirebaseService.getDestinations();
    } catch (error) {
      console.error('Error getting destinations:', error);
      throw error;
    }
  }

  // Reviews
  static async getReviews(tourId?: string) {
    try {
      return await FirebaseService.getReviews(tourId);
    } catch (error) {
      console.error('Error getting reviews:', error);
      throw error;
    }
  }

  // Wildlife sightings
  static async getWildlifeSightings(destinationId?: string) {
    try {
      return await FirebaseService.getWildlifeSightings(destinationId);
    } catch (error) {
      console.error('Error getting wildlife sightings:', error);
      throw error;
    }
  }

  // Weather data
  static async getWeatherData(destinationId: string) {
    try {
      return await FirebaseService.getWeatherData(destinationId);
    } catch (error) {
      console.error('Error getting weather data:', error);
      throw error;
    }
  }

  // Activities
  static async getActivities() {
    try {
      return await FirebaseService.getActivities();
    } catch (error) {
      console.error('Error getting activities:', error);
      throw error;
    }
  }

  // Accommodations
  static async getAccommodations() {
    try {
      return await FirebaseService.getAccommodations();
    } catch (error) {
      console.error('Error getting accommodations:', error);
      throw error;
    }
  }
}


import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Star, ThumbsUp, MessageCircle, Shield, Loader2, Quote, Award } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Review } from '@/types/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Timestamp } from 'firebase/firestore';

interface ReviewsSectionProps {
  tourId: string;
  tourName: string;
}

const ReviewsSection: React.FC<ReviewsSectionProps> = ({ tourId, tourName }) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const { currentUser } = useAuth();
  const { toast } = useToast();

  const [newReview, setNewReview] = useState({
    rating: 5,
    title: '',
    content: ''
  });

  useEffect(() => {
    const loadReviews = async () => {
      try {
        setLoading(true);
        const reviewsData = await FirebaseService.getReviews(tourId);
        setReviews(reviewsData as Review[]);
      } catch (error) {
        console.error('Error loading reviews:', error);
        toast({
          title: "Error loading reviews",
          description: "There was an error loading the reviews.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadReviews();
  }, [tourId, toast]);

  const handleSubmitReview = async () => {
    if (!currentUser) {
      toast({
        title: "Authentication required",
        description: "Please sign in to leave a review.",
        variant: "destructive"
      });
      return;
    }

    if (!newReview.title.trim() || !newReview.content.trim()) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    try {
      setSubmitting(true);
      
      const reviewData = {
        tourId: tourId,
        tourName: tourName,
        userId: currentUser.uid,
        userName: currentUser.displayName || currentUser.email || 'Anonymous User',
        userAvatar: currentUser.photoURL || '',
        rating: newReview.rating,
        title: newReview.title,
        content: newReview.content,
        images: [],
        verified: false,
        helpful: 0,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      await FirebaseService.createReview(reviewData);
      
      toast({
        title: "Review submitted",
        description: "Thank you for your review!"
      });

      // Reset form
      setNewReview({ rating: 5, title: '', content: '' });
      setShowReviewForm(false);
      
      // Reload reviews
      const updatedReviews = await FirebaseService.getReviews(tourId);
      setReviews(updatedReviews as Review[]);
      
    } catch (error) {
      toast({
        title: "Error submitting review",
        description: "There was an error submitting your review. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  const RatingStars = ({ rating, size = 'w-5 h-5', interactive = false, onRatingChange }: {
    rating: number;
    size?: string;
    interactive?: boolean;
    onRatingChange?: (rating: number) => void;
  }) => (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${size} transition-all duration-200 ${
            star <= rating
              ? 'fill-[#D4C2A4] text-[#D4C2A4]'
              : 'text-[#F2EEE6]/30'
          } ${
            interactive ? 'cursor-pointer hover:text-[#D4C2A4] hover:scale-110' : ''
          }`}
          onClick={interactive ? () => onRatingChange?.(star) : undefined}
        />
      ))}
    </div>
  );

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Unknown date';
    try {
      return timestamp.toDate().toLocaleDateString();
    } catch {
      return 'Unknown date';
    }
  };

  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0;

  if (loading) {
    return (
      <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-12 text-center">
        <Loader2 className="h-12 w-12 animate-spin mx-auto mb-6 text-[#D4C2A4]" />
        <p className="font-open-sans text-[#F2EEE6]/70 text-lg">Loading guest experiences...</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Luxury Reviews Header */}
      <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-6">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-3 h-3 bg-[#D4C2A4] rounded-full"></div>
              <h3 className="font-cormorant text-3xl font-light text-[#F2EEE6]">
                Guest Experiences ({reviews.length})
              </h3>
            </div>
            <div className="w-20 h-px bg-gradient-to-r from-[#D4C2A4] to-transparent"></div>
            {averageRating > 0 && (
              <div className="flex items-center gap-4">
                <RatingStars rating={Math.round(averageRating)} size="w-6 h-6" />
                <div className="space-y-1">
                  <div className="font-cormorant text-2xl text-[#D4C2A4]">{averageRating.toFixed(1)}</div>
                  <div className="font-open-sans text-sm text-[#F2EEE6]/70">Average Rating</div>
                </div>
              </div>
            )}
          </div>
          {currentUser && (
            <button
              onClick={() => setShowReviewForm(!showReviewForm)}
              className="bg-gradient-to-r from-[#D4C2A4] to-[#D4C2A4]/90 hover:from-[#D4C2A4]/90 hover:to-[#D4C2A4] text-[#16191D] font-open-sans font-semibold px-8 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-[#D4C2A4]/20"
            >
              Share Your Experience
            </button>
          )}
        </div>
      </div>

      {/* Luxury Review Form */}
      {showReviewForm && (
        <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30">
          <div className="flex items-center mb-6">
            <div className="w-3 h-3 bg-[#D4C2A4] rounded-full mr-4"></div>
            <h3 className="font-cormorant text-3xl font-light text-[#F2EEE6]">Share Your Safari Experience</h3>
          </div>
          <div className="w-20 h-px bg-gradient-to-r from-[#D4C2A4] to-transparent mb-8"></div>

          <div className="space-y-6">
            <div>
              <Label className="font-open-sans text-[#F2EEE6] text-lg mb-3 block">Your Rating</Label>
              <div className="bg-[#D4C2A4]/10 rounded-2xl p-4 border border-[#D4C2A4]/20">
                <RatingStars
                  rating={newReview.rating}
                  size="w-8 h-8"
                  interactive
                  onRatingChange={(rating) => setNewReview(prev => ({ ...prev, rating }))}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="reviewTitle" className="font-open-sans text-[#F2EEE6] text-lg mb-3 block">Experience Title</Label>
              <Input
                id="reviewTitle"
                value={newReview.title}
                onChange={(e) => setNewReview(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Summarize your extraordinary experience..."
                maxLength={100}
                className="bg-[#D4C2A4]/10 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#F2EEE6]/50 rounded-2xl py-4 px-6 font-open-sans focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20"
              />
            </div>

            <div>
              <Label htmlFor="reviewContent" className="font-open-sans text-[#F2EEE6] text-lg mb-3 block">Your Story</Label>
              <Textarea
                id="reviewContent"
                value={newReview.content}
                onChange={(e) => setNewReview(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Share the details of your unforgettable safari journey..."
                rows={6}
                maxLength={1000}
                className="bg-[#D4C2A4]/10 border-[#D4C2A4]/30 text-[#F2EEE6] placeholder:text-[#F2EEE6]/50 rounded-2xl py-4 px-6 font-open-sans focus:border-[#D4C2A4] focus:ring-[#D4C2A4]/20 resize-none"
              />
              <p className="font-open-sans text-xs text-[#F2EEE6]/50 mt-2">
                {newReview.content.length}/1000 characters
              </p>
            </div>

            <div className="flex gap-4 pt-4">
              <button
                onClick={handleSubmitReview}
                disabled={submitting}
                className="bg-gradient-to-r from-[#D4C2A4] to-[#D4C2A4]/90 hover:from-[#D4C2A4]/90 hover:to-[#D4C2A4] text-[#16191D] font-open-sans font-semibold px-8 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-[#D4C2A4]/20 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {submitting ? (
                  <div className="flex items-center gap-3">
                    <Loader2 className="w-5 h-5 animate-spin" />
                    Publishing Experience...
                  </div>
                ) : (
                  'Publish Experience'
                )}
              </button>
              <button
                onClick={() => setShowReviewForm(false)}
                disabled={submitting}
                className="bg-transparent hover:bg-[#D4C2A4]/10 text-[#D4C2A4] border-2 border-[#D4C2A4]/50 hover:border-[#D4C2A4] font-open-sans font-medium px-8 py-3 rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Luxury Reviews List */}
      {reviews.length === 0 ? (
        <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-12 text-center">
          <div className="w-20 h-20 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <Star className="w-10 h-10 text-[#D4C2A4]" />
          </div>
          <h3 className="font-cormorant text-2xl font-light text-[#F2EEE6] mb-4">No Experiences Shared Yet</h3>
          <p className="font-open-sans text-[#F2EEE6]/70 text-lg">Be the first to share your extraordinary safari journey!</p>
        </div>
      ) : (
        <div className="space-y-6">
          {reviews.map((review) => (
            <div key={review.id} className="group">
              <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30 hover:shadow-2xl hover:shadow-[#D4C2A4]/10">
                {/* Quote Icon */}
                <div className="flex justify-end mb-4">
                  <Quote className="h-8 w-8 text-[#D4C2A4]/30 transform rotate-180" />
                </div>

                <div className="flex items-start gap-6 mb-6">
                  {/* Avatar */}
                  <div className="flex-shrink-0">
                    <Avatar className="h-16 w-16 ring-2 ring-[#D4C2A4]/30">
                      {review.userAvatar ? (
                        <img src={review.userAvatar} alt={review.userName || 'User'} className="object-cover" />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-[#D4C2A4] to-[#D4C2A4]/80 flex items-center justify-center text-[#16191D] font-cormorant text-xl font-semibold">
                          {(review.userName || 'U').charAt(0).toUpperCase()}
                        </div>
                      )}
                    </Avatar>
                  </div>

                  {/* Review Content */}
                  <div className="flex-1 space-y-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-3">
                          <h4 className="font-cormorant text-xl font-medium text-[#F2EEE6]">{review.userName || 'Anonymous Guest'}</h4>
                          {review.verified && (
                            <Badge className="bg-[#D4C2A4]/20 text-[#D4C2A4] border border-[#D4C2A4]/30 backdrop-blur-sm font-open-sans text-xs px-3 py-1 rounded-full">
                              <Shield className="w-3 h-3 mr-1" />
                              Verified Guest
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4">
                          <RatingStars rating={review.rating} size="w-5 h-5" />
                          <span className="font-open-sans text-sm text-[#F2EEE6]/60">{formatDate(review.createdAt)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h5 className="font-cormorant text-2xl font-light text-[#D4C2A4]">{review.title}</h5>
                      <p className="font-open-sans text-[#F2EEE6]/80 leading-relaxed text-lg">{review.content}</p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-6 pt-6 border-t border-[#D4C2A4]/20">
                  <button className="flex items-center gap-3 text-[#F2EEE6]/70 hover:text-[#D4C2A4] transition-colors duration-300 font-open-sans">
                    <ThumbsUp className="w-4 h-4" />
                    Helpful ({review.helpful || 0})
                  </button>
                  <button className="flex items-center gap-3 text-[#F2EEE6]/70 hover:text-[#D4C2A4] transition-colors duration-300 font-open-sans">
                    <MessageCircle className="w-4 h-4" />
                    Reply
                  </button>
                </div>

                {/* Response */}
                {review.response && (
                  <div className="mt-6 p-6 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-2xl">
                    <div className="flex items-center gap-3 mb-3">
                      <Award className="h-5 w-5 text-[#D4C2A4]" />
                      <div className="font-cormorant text-lg font-medium text-[#D4C2A4]">{review.response.author}</div>
                    </div>
                    <p className="font-open-sans text-[#F2EEE6]/80 leading-relaxed">{review.response.content}</p>
                    <div className="font-open-sans text-xs text-[#F2EEE6]/50 mt-3">{review.response.date}</div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReviewsSection;

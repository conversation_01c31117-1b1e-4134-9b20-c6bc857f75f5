
import { FirebaseService } from './firebase';
import { Tour } from '@/types/firebase';

export class TourService {
  // Get all tours
  static async getTours() {
    try {
      return await FirebaseService.getTours();
    } catch (error) {
      console.error('Error getting tours:', error);
      throw error;
    }
  }

  // Get single tour
  static async getTour(id: string) {
    try {
      return await FirebaseService.getTour(id);
    } catch (error) {
      console.error('Error getting tour:', error);
      throw error;
    }
  }

  // Create tour
  static async createTour(tourData: Omit<Tour, 'id'>) {
    try {
      return await FirebaseService.createTour(tourData);
    } catch (error) {
      console.error('Error creating tour:', error);
      throw error;
    }
  }

  // Update tour
  static async updateTour(id: string, updates: Partial<Tour>) {
    try {
      return await FirebaseService.updateTour(id, updates);
    } catch (error) {
      console.error('Error updating tour:', error);
      throw error;
    }
  }

  // Delete tour
  static async deleteTour(id: string) {
    try {
      return await FirebaseService.deleteTour(id);
    } catch (error) {
      console.error('Error deleting tour:', error);
      throw error;
    }
  }

  // Search tours
  static async searchTours(searchTerm: string, filters: any = {}) {
    try {
      const tours = await FirebaseService.getTours();
      
      let filtered = tours.filter((tour: any) => {
        const matchesSearch = tour.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             tour.description?.toLowerCase().includes(searchTerm.toLowerCase());
        
        if (!matchesSearch) return false;
        
        // Apply filters
        if (filters.category && filters.category !== 'all' && tour.category !== filters.category) {
          return false;
        }
        
        if (filters.accommodationLevel && filters.accommodationLevel !== 'all' && 
            tour.accommodationLevel !== filters.accommodationLevel) {
          return false;
        }
        
        if (filters.priceRange) {
          const price = tour.price || 0;
          if (price < filters.priceRange[0] || price > filters.priceRange[1]) {
            return false;
          }
        }
        
        return true;
      });
      
      return filtered;
    } catch (error) {
      console.error('Error searching tours:', error);
      throw error;
    }
  }

  // Get featured tours
  static async getFeaturedTours() {
    try {
      const tours = await FirebaseService.getTours();
      return tours.filter((tour: any) => tour.featured === true);
    } catch (error) {
      console.error('Error getting featured tours:', error);
      throw error;
    }
  }

  // Get tours by category
  static async getToursByCategory(category: string) {
    try {
      const tours = await FirebaseService.getTours();
      return tours.filter((tour: any) => tour.category === category);
    } catch (error) {
      console.error('Error getting tours by category:', error);
      throw error;
    }
  }

  // Get tours by destination
  static async getToursByDestination(destination: string) {
    try {
      const tours = await FirebaseService.getTours();
      return tours.filter((tour: any) => 
        tour.destinations?.includes(destination) || 
        tour.location?.includes(destination)
      );
    } catch (error) {
      console.error('Error getting tours by destination:', error);
      throw error;
    }
  }

  // Get tour availability
  static async getTourAvailability(tourId: string, date: string) {
    try {
      // This would typically check bookings and availability calendar
      const bookings = await FirebaseService.getBookings();
      const tourBookings = bookings.filter((booking: any) => 
        booking.tourId === tourId && 
        booking.date === date &&
        booking.status !== 'cancelled'
      );
      
      const tour = await FirebaseService.getTour(tourId);
      const maxCapacity = tour?.maxGroupSize || 12;
      const bookedSpots = tourBookings.reduce((sum: number, booking: any) => 
        sum + (booking.participants || 1), 0
      );
      
      return {
        available: bookedSpots < maxCapacity,
        spotsLeft: maxCapacity - bookedSpots,
        maxCapacity
      };
    } catch (error) {
      console.error('Error getting tour availability:', error);
      throw error;
    }
  }
}

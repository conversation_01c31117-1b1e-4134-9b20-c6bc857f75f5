import React from 'react';
import GoogleReviewsWidget from './GoogleReviewsWidget';
import TripAdvisorWidget from './TripAdvisorWidget';
import { reviewWidgetConfig } from '@/config/reviewWidgets';

const ReviewWidgetsPreview: React.FC = () => {
  return (
    <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 rounded-lg">
      <h3 className="text-white text-lg font-semibold mb-6 text-center">
        Review Widgets Preview
      </h3>
      
      <div className="max-w-3xl mx-auto">
        <h4 className="text-sm font-medium mb-4 text-center text-white/90">
          Trusted by Travelers Worldwide
        </h4>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <GoogleReviewsWidget 
            businessId={reviewWidgetConfig.google.businessId}
            rating={reviewWidgetConfig.google.rating}
            reviewCount={reviewWidgetConfig.google.reviewCount}
            className="flex-1 max-w-sm"
          />
          <TripAdvisorWidget 
            profileUrl={reviewWidgetConfig.tripadvisor.profileUrl}
            rating={reviewWidgetConfig.tripadvisor.rating}
            certificateYear={reviewWidgetConfig.tripadvisor.certificateYear}
            reviewCount={reviewWidgetConfig.tripadvisor.reviewCount}
            className="flex-1 max-w-sm"
          />
        </div>
      </div>
      
      <div className="mt-6 text-center">
        <p className="text-white/70 text-xs">
          These widgets appear in the footer with authentic Google and TripAdvisor branding
        </p>
      </div>
    </div>
  );
};

export default ReviewWidgetsPreview;

import { LiquidButton } from "@/components/ui/liquid-glass-button";

export default function LiquidGlassDemo() {
  return (
    <> 
      <div className="relative h-[200px] w-[800px] bg-gradient-to-br from-orange-100 to-red-100 rounded-lg flex items-center justify-center"> 
        <LiquidButton 
          className="absolute top-1/2 left-1/2 z-10 -translate-x-1/2 -translate-y-1/2 text-orange-600 hover:text-orange-700"
          size="xxl"
          variant="default"
        >
          Liquid Glass
        </LiquidButton> 
      </div>
    </>
  )
}

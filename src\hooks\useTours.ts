
import { useState, useEffect } from 'react';
import { FirebaseService } from '@/services/firebase';
import { Tour, SearchFilters } from '@/types/firebase';

export const useTours = () => {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadTours = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await FirebaseService.getTours();
      // Ensure we have proper Tour objects with safe defaults
      const tourData = data.map((tour: any) => ({
        id: tour.id,
        title: tour.title || 'Untitled Tour',
        description: tour.description || 'No description available',
        price: tour.price || 0,
        duration: tour.duration || 'Duration not specified',
        location: tour.location || '',
        destinations: Array.isArray(tour.destinations) ? tour.destinations : [],
        activities: Array.isArray(tour.activities) ? tour.activities : [],
        accommodations: Array.isArray(tour.accommodations) ? tour.accommodations : [],
        maxGroupSize: tour.maxGroupSize || 12,
        minGroupSize: tour.minGroupSize || 1,
        difficulty: tour.difficulty || 'easy',
        includes: Array.isArray(tour.includes) ? tour.includes : [],
        excludes: Array.isArray(tour.excludes) ? tour.excludes : [],
        images: Array.isArray(tour.images) ? tour.images : [],
        featured: tour.featured || false,
        status: tour.status || 'active',
        rating: tour.rating || 0,
        reviewCount: tour.reviewCount || 0,
        tourType: tour.tourType || 'standard',
        category: tour.category || 'Safari',
        accommodationLevel: tour.accommodationLevel || 'Standard',
        seasonality: tour.seasonality || {
          greenSeason: true,
          drySeason: true,
          bestMonths: []
        },
        itinerary: Array.isArray(tour.itinerary) ? tour.itinerary : [],
        fitnessRequirements: tour.fitnessRequirements || {
          level: 'Easy',
          description: 'Suitable for all fitness levels',
          walkingDistance: 'Minimal',
          terrain: 'Easy',
          ageRestrictions: 'None',
          medicalConditions: []
        },
        equipment: tour.equipment || {
          provided: [],
          recommended: [],
          required: []
        },
        groupOptions: Array.isArray(tour.groupOptions) ? tour.groupOptions : [],
        specialFeatures: Array.isArray(tour.specialFeatures) ? tour.specialFeatures : [],
        difficultyDetails: tour.difficultyDetails || '',
        createdAt: tour.createdAt,
        updatedAt: tour.updatedAt
      })) as Tour[];
      setTours(tourData);
    } catch (err) {
      setError('Failed to load tours');
      console.error('Error loading tours:', err);
    } finally {
      setLoading(false);
    }
  };

  const searchTours = async (searchTerm: string, searchFilters?: SearchFilters) => {
    setLoading(true);
    setError(null);
    try {
      const data = await FirebaseService.searchTours(searchTerm, searchFilters);
      // Apply the same mapping as loadTours
      const tourData = data.map((tour: any) => ({
        id: tour.id,
        title: tour.title || 'Untitled Tour',
        description: tour.description || 'No description available',
        price: tour.price || 0,
        duration: tour.duration || 'Duration not specified',
        location: tour.location || '',
        destinations: Array.isArray(tour.destinations) ? tour.destinations : [],
        activities: Array.isArray(tour.activities) ? tour.activities : [],
        accommodations: Array.isArray(tour.accommodations) ? tour.accommodations : [],
        maxGroupSize: tour.maxGroupSize || 12,
        minGroupSize: tour.minGroupSize || 1,
        difficulty: tour.difficulty || 'easy',
        includes: Array.isArray(tour.includes) ? tour.includes : [],
        excludes: Array.isArray(tour.excludes) ? tour.excludes : [],
        images: Array.isArray(tour.images) ? tour.images : [],
        featured: tour.featured || false,
        status: tour.status || 'active',
        rating: tour.rating || 0,
        reviewCount: tour.reviewCount || 0,
        tourType: tour.tourType || 'standard',
        category: tour.category || 'Safari',
        accommodationLevel: tour.accommodationLevel || 'Standard',
        seasonality: tour.seasonality || {
          greenSeason: true,
          drySeason: true,
          bestMonths: []
        },
        itinerary: Array.isArray(tour.itinerary) ? tour.itinerary : [],
        fitnessRequirements: tour.fitnessRequirements || {
          level: 'Easy',
          description: 'Suitable for all fitness levels',
          walkingDistance: 'Minimal',
          terrain: 'Easy',
          ageRestrictions: 'None',
          medicalConditions: []
        },
        equipment: tour.equipment || {
          provided: [],
          recommended: [],
          required: []
        },
        groupOptions: Array.isArray(tour.groupOptions) ? tour.groupOptions : [],
        specialFeatures: Array.isArray(tour.specialFeatures) ? tour.specialFeatures : [],
        difficultyDetails: tour.difficultyDetails || '',
        createdAt: tour.createdAt,
        updatedAt: tour.updatedAt
      })) as Tour[];
      setTours(tourData);
    } catch (err) {
      setError('Failed to search tours');
      console.error('Error searching tours:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTours();
  }, []);

  return {
    tours,
    loading,
    error,
    refetch: loadTours,
    searchTours
  };
};

export const useFeaturedTours = (limit: number = 6) => {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadFeaturedTours = async () => {
      try {
        const data = await FirebaseService.getTours();
        // Apply the same safe mapping
        const tourData = data.map((tour: any) => ({
          id: tour.id,
          title: tour.title || 'Untitled Tour',
          description: tour.description || 'No description available',
          price: tour.price || 0,
          duration: tour.duration || 'Duration not specified',
          location: tour.location || '',
          destinations: Array.isArray(tour.destinations) ? tour.destinations : [],
          activities: Array.isArray(tour.activities) ? tour.activities : [],
          accommodations: Array.isArray(tour.accommodations) ? tour.accommodations : [],
          maxGroupSize: tour.maxGroupSize || 12,
          minGroupSize: tour.minGroupSize || 1,
          difficulty: tour.difficulty || 'easy',
          includes: Array.isArray(tour.includes) ? tour.includes : [],
          excludes: Array.isArray(tour.excludes) ? tour.excludes : [],
          images: Array.isArray(tour.images) ? tour.images : [],
          featured: tour.featured || false,
          status: tour.status || 'active',
          rating: tour.rating || 0,
          reviewCount: tour.reviewCount || 0,
          tourType: tour.tourType || 'standard',
          category: tour.category || 'Safari',
          accommodationLevel: tour.accommodationLevel || 'Standard',
          seasonality: tour.seasonality || {
            greenSeason: true,
            drySeason: true,
            bestMonths: []
          },
          itinerary: Array.isArray(tour.itinerary) ? tour.itinerary : [],
          fitnessRequirements: tour.fitnessRequirements || {
            level: 'Easy',
            description: 'Suitable for all fitness levels',
            walkingDistance: 'Minimal',
            terrain: 'Easy',
            ageRestrictions: 'None',
            medicalConditions: []
          },
          equipment: tour.equipment || {
            provided: [],
            recommended: [],
            required: []
          },
          groupOptions: Array.isArray(tour.groupOptions) ? tour.groupOptions : [],
          specialFeatures: Array.isArray(tour.specialFeatures) ? tour.specialFeatures : [],
          difficultyDetails: tour.difficultyDetails || '',
          createdAt: tour.createdAt,
          updatedAt: tour.updatedAt
        })) as Tour[];
        
        const featured = tourData.filter(tour => tour.featured).slice(0, limit);
        setTours(featured);
      } catch (error) {
        console.error('Error loading featured tours:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedTours();
  }, [limit]);

  return { tours, loading };
};

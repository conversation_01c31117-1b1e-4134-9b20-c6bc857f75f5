
import React, { useState, useEffect } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarIcon, Home, Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { FirebaseService } from '@/services/firebase';
import { EmailService, CustomTourEmailData } from '@/services/emailService';
import { DestinationService } from '@/services/destinationService';
import { Destination } from '@/types/firebase';
import { format } from 'date-fns';

const TourBuilder = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [step, setStep] = useState(1); // Start with step 1
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [destinationsLoading, setDestinationsLoading] = useState(true);
  const [destinationsError, setDestinationsError] = useState<string | null>(null);

  const [tourData, setTourData] = useState({
    // Basic Info
    duration: 5,
    participants: 6,
    budget: [4000],
    startDate: null as Date | null,

    // Destinations
    destinations: [] as string[],

    // Interests
    interests: [] as string[],

    // Accommodation
    accommodation: 'midrange' as 'budget' | 'midrange' | 'luxury' | 'ultraluxury',

    // Activities
    activities: [] as string[],

    // Special Requirements
    specialRequests: '',
    fitnessLevel: 'moderate' as 'easy' | 'moderate' | 'challenging',
    photographyInterest: false,

    // Contact Info
    name: '',
    email: '',
    phone: ''
  });

  // Fetch destinations from Firebase
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        setDestinationsLoading(true);
        setDestinationsError(null);
        const fetchedDestinations = await DestinationService.getDestinations();
        setDestinations(fetchedDestinations);
      } catch (error) {
        console.error('Error fetching destinations:', error);
        setDestinationsError('Failed to load destinations. Please try again.');
        // Fallback to hardcoded destinations if Firebase fails
        const fallbackDestinations: Destination[] = [
          'SERENGETI', 'NGORONGORO', 'TARANGIRE', 'MIKUMI', 'KILIMANJARO',
          'ZANZIBAR', 'MERU', 'USAMBARA', 'RUAHA'
        ].map((name, index) => ({
          id: `fallback-${index}`,
          name: name,
          description: '',
          country: 'Tanzania',
          region: '',
          coordinates: { lat: 0, lng: 0 },
          bestTimeToVisit: [],
          climate: '',
          wildlife: [],
          images: [],
          activities: [],
          accommodations: [],
          featured: false,
          detailedGuide: {
            overview: '',
            geography: '',
            history: '',
            bestTimeToVisit: {
              drySeason: '',
              greenSeason: '',
              photography: '',
              birding: ''
            },
            gettingThere: '',
            accommodation: '',
            packingTips: [],
            healthSafety: '',
            travelTips: []
          },
          seasonalInfo: {
            drySeason: {
              months: [],
              description: '',
              wildlife: '',
              photography: '',
              advantages: [],
              disadvantages: []
            },
            greenSeason: {
              months: [],
              description: '',
              wildlife: '',
              photography: '',
              advantages: [],
              disadvantages: []
            }
          },
          conservationInfo: {
            initiatives: [],
            challenges: [],
            howTouristsHelp: [],
            conservationFee: 0
          },
          culturalInfo: {
            tribes: [],
            languages: [],
            traditions: [],
            etiquette: [],
            culturalSites: []
          },
          createdAt: new Date() as any,
          updatedAt: new Date() as any
        }));
        setDestinations(fallbackDestinations);
      } finally {
        setDestinationsLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  const interests = [
    'Big Five Safari',
    'Great Migration',
    'Bird Watching',
    'Photography',
    'Cultural Experiences',
    'Adventure Sports',
    'Relaxation',
    'Conservation Learning'
  ];

  const activities = [
    'Game Drives',
    'Walking Safaris',
    'Hot Air Balloon',
    'Cultural Village Visits',
    'Night Drives',
    'Bush Camping',
    'Photography Workshops',
    'Conservation Activities'
  ];

  const handleArrayToggle = (array: string[], item: string, field: keyof typeof tourData) => {
    const newArray = array.includes(item)
      ? array.filter(i => i !== item)
      : [...array, item];
    
    setTourData(prev => ({
      ...prev,
      [field]: newArray
    }));
  };

  // Helper function to send custom tour email notification
  const sendCustomTourEmailNotification = async (tourData: any, requestId: string) => {
    try {
      const emailData: CustomTourEmailData = {
        customerName: tourData.name,
        customerEmail: tourData.email,
        customerPhone: tourData.phone,
        duration: tourData.duration,
        participants: tourData.participants,
        budget: tourData.budget,
        startDate: tourData.startDate ? format(tourData.startDate, 'yyyy-MM-dd') : '',
        destinations: tourData.destinations,
        interests: tourData.interests,
        accommodation: tourData.accommodation,
        activities: tourData.activities,
        specialRequests: tourData.specialRequests,
        fitnessLevel: tourData.fitnessLevel,
        photographyInterest: tourData.photographyInterest,
        requestId
      };

      await EmailService.sendCustomTourNotification(emailData);
      console.log('✅ Custom tour email notification sent successfully');
    } catch (error) {
      console.error('❌ Error sending custom tour email notification:', error);
      // Don't throw error to avoid breaking the submission process
    }
  };

  const handleSubmit = async () => {
    if (!tourData.name || !tourData.email) {
      toast({
        title: "Please fill in your contact information",
        variant: "destructive"
      });
      return;
    }

    try {
      // Send the tour request to Firebase
      const requestId = await FirebaseService.createCustomTourRequest({
        duration: tourData.duration,
        participants: tourData.participants,
        budget: tourData.budget,
        startDate: tourData.startDate ? format(tourData.startDate, 'yyyy-MM-dd') : '',
        destinations: tourData.destinations,
        interests: tourData.interests,
        accommodation: tourData.accommodation,
        activities: tourData.activities,
        specialRequests: tourData.specialRequests,
        fitnessLevel: tourData.fitnessLevel,
        photographyInterest: tourData.photographyInterest,
        name: tourData.name,
        email: tourData.email,
        phone: tourData.phone
      });

      // Send email notification to admin
      await sendCustomTourEmailNotification(tourData, requestId);

      toast({
        title: "Custom Tour Request Sent!",
        description: "We'll contact you within 24 hours with a personalized itinerary."
      });

      // Reset form or redirect
      setStep(1);
      setTourData({
        duration: 5,
        participants: 2,
        budget: [2000],
        startDate: null,
        destinations: [],
        interests: [],
        accommodation: 'midrange',
        activities: [],
        specialRequests: '',
        fitnessLevel: 'moderate',
        photographyInterest: false,
        name: '',
        email: '',
        phone: ''
      });

    } catch (error) {
      console.error('Error sending custom tour request:', error);
      toast({
        title: "Failed to send request",
        description: "Please try again or contact us directly.",
        variant: "destructive"
      });
    }
  };

  const nextStep = () => setStep(prev => Math.min(prev + 1, 6));
  const prevStep = () => setStep(prev => Math.max(prev - 1, 1));

  return (
    <div
      className="min-h-screen relative"
      style={{
        backgroundImage: 'url("https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/NEW%20SHIT/Generated%20image%203%20(1).png")',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Background overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-30"></div>

      {/* Home Icon - Top Left */}
      <div className="absolute top-6 left-6 z-20">
        <button
          onClick={() => navigate('/')}
          className="flex items-center justify-center w-12 h-12 bg-white bg-opacity-90 backdrop-blur-sm rounded-full shadow-lg hover:bg-opacity-100 hover:shadow-xl transition-all duration-300 group"
          aria-label="Go to Homepage"
        >
          <Home className="w-5 h-5 text-gray-700 group-hover:text-gray-900 transition-colors" />
        </button>
      </div>

      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-4xl">
          {/* Floating Card - Compact and elegant */}
          <div className="bg-white rounded-2xl shadow-2xl p-4 md:p-6 mx-auto max-w-lg">
            {/* Logo */}
            <div className="flex justify-center mb-4">
              <img
                src="/photos/heroLogo.svg"
                alt="Warriors of Africa Safari"
                className="h-6 w-auto"
                onError={(e) => {
                  // Fallback to text if logo doesn't load
                  e.currentTarget.style.display = 'none';
                  const textLogo = document.createElement('div');
                  textLogo.innerHTML = '<span class="text-lg font-bold text-gray-800">Warriors of Africa Safari</span>';
                  e.currentTarget.parentNode?.appendChild(textLogo);
                }}
              />
            </div>

            {/* Step Content */}
            {step === 1 && (
              <div>
                <div className="text-center mb-4">
                  <h1 className="text-xl md:text-2xl font-cormorant text-gray-800 mb-2">
                    Let's plan your <em className="italic">dream</em> safari
                  </h1>
                  <p className="text-gray-600 text-sm font-open-sans">
                    Tell us about your preferences
                  </p>
                </div>
                <div className="space-y-2">
                  <div>
                    <Label className="text-[12px] font-open-sans mb-2 block">Duration: {tourData.duration} Days</Label>
                    <Slider
                      value={[tourData.duration]}
                      onValueChange={(value) => setTourData(prev => ({ ...prev, duration: value[0] }))}
                      max={14}
                      min={2}
                      step={1}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <Label className="text-[12px] font-open-sans mb-2 block">Participants: {tourData.participants}</Label>
                    <Slider
                      value={[tourData.participants]}
                      onValueChange={(value) => setTourData(prev => ({ ...prev, participants: value[0] }))}
                      max={12}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <Label className="text-[12px] font-open-sans mb-2 block">Budget: ${tourData.budget[0].toLocaleString()}</Label>
                    <Slider
                      value={tourData.budget}
                      onValueChange={(value) => setTourData(prev => ({ ...prev, budget: value }))}
                      max={10000}
                      min={500}
                      step={100}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <Label className="text-[12px] font-open-sans mb-2 block">Preferred Start Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal h-10 bg-white border-gray-300 hover:bg-gray-50"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4 text-gray-500" />
                          {tourData.startDate ? (
                            <span className="text-gray-900">{format(tourData.startDate, "PPP")}</span>
                          ) : (
                            <span className="text-gray-500">Pick your departure date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 bg-white shadow-xl border border-gray-200" align="start">
                        <div className="p-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
                          <h3 className="font-cormorant text-lg font-semibold text-gray-800 mb-1">
                            Choose Your <em className="italic">Adventure</em> Date
                          </h3>
                          <p className="text-xs text-gray-600 font-open-sans">
                            Select the perfect time for your safari experience
                          </p>
                        </div>
                        <div className="p-3">
                          <Calendar
                            mode="single"
                            selected={tourData.startDate || undefined}
                            onSelect={(date) => setTourData(prev => ({ ...prev, startDate: date || null }))}
                            disabled={(date) => date < new Date()}
                            initialFocus
                            className="rounded-lg"
                            classNames={{
                              months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                              month: "space-y-4",
                              caption: "flex justify-center pt-1 relative items-center mb-4",
                              caption_label: "text-base font-cormorant font-semibold text-gray-800",
                              nav: "space-x-1 flex items-center",
                              nav_button: "h-8 w-8 bg-transparent p-0 opacity-60 hover:opacity-100 hover:bg-gray-100 rounded-md transition-all",
                              nav_button_previous: "absolute left-1",
                              nav_button_next: "absolute right-1",
                              table: "w-full border-collapse space-y-1",
                              head_row: "flex mb-2",
                              head_cell: "text-gray-500 rounded-md w-8 font-normal text-xs font-open-sans",
                              row: "flex w-full mt-1",
                              cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-gray-100 first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                              day: "h-8 w-8 p-0 font-normal font-open-sans text-sm hover:bg-gray-100 hover:text-gray-900 rounded-md transition-all duration-200 focus:bg-gray-100 focus:text-gray-900",
                              day_selected: "bg-gray-800 text-white hover:bg-gray-700 hover:text-white focus:bg-gray-800 focus:text-white rounded-md shadow-sm",
                              day_today: "bg-orange-100 text-orange-800 font-medium",
                              day_outside: "text-gray-400 opacity-50",
                              day_disabled: "text-gray-400 opacity-30 cursor-not-allowed",
                              day_range_middle: "aria-selected:bg-gray-100 aria-selected:text-gray-900",
                              day_hidden: "invisible",
                            }}
                          />
                        </div>
                        <div className="p-3 border-t border-gray-100 bg-gray-50">
                          <div className="flex items-center justify-between text-xs text-gray-600 font-open-sans">
                            <span className="flex items-center">
                              <div className="w-3 h-3 bg-orange-100 rounded-full mr-2"></div>
                              Today
                            </span>
                            <span className="flex items-center">
                              <div className="w-3 h-3 bg-gray-800 rounded-full mr-2"></div>
                              Selected
                            </span>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>
            )}

            {step === 2 && (
              <div>
                <div className="text-center mb-6">
                  <h1 className="text-2xl md:text-2.5xl font-cormorant text-gray-800 mb-3">
                    Do you know <em className="italic">where</em> you want to travel to?
                  </h1>
                  <p className="text-gray-600 text-[12px] font-open-sans">
                    You can select more than one destination
                  </p>
                </div>

                {/* Destinations Grid - Now scrollable and fetched from Firebase */}
                <div className="mb-8">
                  {destinationsLoading ? (
                    <div className="flex justify-center items-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin text-gray-600" />
                      <span className="ml-2 text-sm text-gray-600">Loading destinations...</span>
                    </div>
                  ) : destinationsError ? (
                    <div className="text-center py-4">
                      <p className="text-red-600 text-sm mb-2">{destinationsError}</p>
                      <button
                        onClick={() => window.location.reload()}
                        className="text-orange-600 hover:text-orange-700 text-sm underline"
                      >
                        Retry
                      </button>
                    </div>
                  ) : (
                    <div className="max-h-48 overflow-y-auto destinations-scrollbar">
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 pr-2">
                        {destinations.map((destination) => (
                          <button
                            key={destination.id}
                            onClick={() => handleArrayToggle(tourData.destinations, destination.name, 'destinations')}
                            className={`px-3 py-2 border text-[10px] font-open-sans transition-all duration-200 rounded ${
                              tourData.destinations.includes(destination.name)
                                ? 'bg-gray-800 text-white border-gray-800'
                                : 'bg-transparent text-gray-700 border-gray-300 hover:border-gray-400'
                            }`}
                          >
                            {destination.name}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Expert Call Link */}
                <div className="text-center mb-6">
                  <a
                    href="tel:+1234567890"
                    className="text-orange-600 hover:text-orange-700 text-[12px] "
                  >
                    Call our Expert for More Explanations
                  </a>
                </div>
              </div>
            )}

            {step === 3 && (
              <div>
                <div className="text-center mb-6">
                  <h1 className="text-2xl md:text-3xl font-cormorant text-gray-800 mb-3">
                    What <em className="italic">interests</em> you most?
                  </h1>
                  <p className="text-gray-600 text-[12px] font-open-sans">
                    Select all that apply
                  </p>
                </div>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {interests.map((interest) => (
                      <button
                        key={interest}
                        onClick={() => handleArrayToggle(tourData.interests, interest, 'interests')}
                        className={`p-3 border rounded-lg text-[12px] transition-all ${
                          tourData.interests.includes(interest)
                            ? 'border-gray-800 bg-gray-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        {interest}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {step === 4 && (
              <div>
                <div className="text-center mb-6">
                  <h1 className="text-2xl md:text-3xl font-cormorant text-gray-800 mb-3">
                    Your <em className="italic">travel</em> preferences
                  </h1>
                  <p className="text-gray-600 text-[12px] font-open-sans">
                    Help us customize your experience
                  </p>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label className="text-[14px] text-gray-600 mb-3 block">Accommodation Level</Label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      {[
                        { value: 'budget', label: 'Budget', desc: '$300-400/night' },
                        { value: 'midrange', label: 'Mid-range', desc: '$400-600/night' },
                        { value: 'luxury', label: 'Luxury', desc: '$600-1000/night' },
                        { value: 'Ultraluxury', label: 'Ultra Luxury', desc: '$1000+/night' }
                      ].map((option) => (
                        <button
                          key={option.value}
                          onClick={() => setTourData(prev => ({ ...prev, accommodation: option.value as 'budget' | 'midrange' | 'luxury'| 'ultraluxury' }))}
                          className={`p-3 border rounded-lg transition-all ${
                            tourData.accommodation === option.value
                              ? 'border-gray-800 bg-gray-50'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <div className="text-center">
                            <div className="font-semibold text-sm">{option.label}</div>
                            <div className="text-xs text-gray-600">{option.desc}</div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                  <div>
                    <Label className="text-base font-semibold mb-3 block">Fitness Level</Label>
                    <Select
                      value={tourData.fitnessLevel}
                      onValueChange={(value) => setTourData(prev => ({ ...prev, fitnessLevel: value as 'easy' | 'moderate' | 'challenging' }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem className='text-gray-600 hover:bg-gray-50' value="easy">Easy - Minimal walking</SelectItem>
                        <SelectItem className='text-gray-600 hover:bg-gray-50' value="moderate" >Moderate - Some walking</SelectItem>
                        <SelectItem className='text-gray-600 hover:bg-gray-50' value="challenging">Challenging - Extensive walking/hiking</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}

            {step === 5 && (
              <div>
                <div className="text-center mb-6">
                  <h1 className="text-2xl md:text-3xl font-cormorant text-gray-800 mb-3">
                    What <em className="italic">activities</em> interest you?
                  </h1>
                  <p className="text-gray-600 text-[12px] font-open-sans">
                    Select all that apply
                  </p>
                </div>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {['Game Drives', 'Walking Safaris', 'Hot Air Balloon', 'Cultural Village Visits', 'Night Drives', 'Bush Camping', 'Photography Workshops', 'Conservation Activities'].map((activity) => (
                      <button
                        key={activity}
                        onClick={() => handleArrayToggle(tourData.activities, activity, 'activities')}
                        className={`p-3 border rounded-lg text-[13px] font-open-sans transition-all ${
                          tourData.activities.includes(activity)
                            ? 'border-gray-800 bg-gray-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        {activity}
                      </button>
                    ))}
                  </div>
                  <div>
                    <Label htmlFor="special" className="text-[12px] font-semibold">Special Requests</Label>
                    <Textarea
                      id="special"
                      value={tourData.specialRequests}
                      onChange={(e) => setTourData(prev => ({ ...prev, specialRequests: e.target.value }))}
                      placeholder="Any dietary restrictions, mobility requirements, or special requests..."
                      className="mt-2 min-h-[80px]"
                      rows={3}
                    />
                  </div>
                </div>
              </div>
            )}

            {step === 6 && (
              <div>
                <div className="text-center mb-6">
                  <h1 className="text-2xl md:text-3xl font-cormorant text-gray-800 mb-3">
                    Almost <em className="italic">done!</em>
                  </h1>
                  <p className="text-gray-600 text-[12px] font-open-sans">
                    We'll contact you with a personalized itinerary
                  </p>
                </div>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name" className="text-[12px] font-semibold">Full Name *</Label>
                      <Input
                        id="name"
                        value={tourData.name}
                        onChange={(e) => setTourData(prev => ({ ...prev, name: e.target.value }))}
                        required
                        className="mt-2"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className="text-[12px] font-semibold">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={tourData.email}
                        onChange={(e) => setTourData(prev => ({ ...prev, email: e.target.value }))}
                        required
                        className="mt-2"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-[12px]  font-semibold">Phone Number</Label>
                    <Input
                      id="phone"
                      value={tourData.phone}
                      onChange={(e) => setTourData(prev => ({ ...prev, phone: e.target.value }))}
                      className="mt-2"
                    />
                  </div>
                </div>
              </div>
            )}

          </div>

          {/* Navigation Buttons - Outside the card like in the reference image */}
          <div className="flex justify-between mt-6 max-w-2xl mx-auto">
            <button
              onClick={prevStep}
              disabled={step === 1}
              className="px-6 py-3 bg-gray-800 text-white  font-medium hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-[12px]"
            >
              BACK
            </button>
            {step < 6 ? (
              <button
                onClick={nextStep}
                className="px-6 py-3 bg-gray-200 text-gray-800  font-medium hover:bg-gray-300 transition-colors text-[12px]"
              >
                NEXT
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                className="px-6 py-3 bg-orange-600 text-white  font-medium hover:bg-orange-700 transition-colors text-[12px]"
              >
                SEND REQUEST
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TourBuilder;

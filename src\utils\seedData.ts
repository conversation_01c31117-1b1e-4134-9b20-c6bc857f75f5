import { FirebaseService } from '@/services/firebase';
import { Timestamp } from 'firebase/firestore';

// Sample Destinations
export const sampleDestinations = [
  {
    name: 'Serengeti National Park',
    description: 'The Serengeti is a vast ecosystem in east-central Africa spanning about 12,000 square miles (30,000 square kilometers). It\'s known for its massive annual migration of wildebeest and zebra.',
    country: 'Tanzania',
    region: 'East Africa',
    coordinates: { lat: -2.333333, lng: 34.833332 },
    bestTimeToVisit: ['June to October (Dry Season)'],
    climate: 'Tropical Savannah',
    wildlife: [
      {
        species: 'African Elephant',
        scientificName: 'Loxodonta africana',
        category: 'Mammal',
        abundance: 'Common',
        bestSpottingTime: 'Early morning or late afternoon',
        behavior: 'Grazing, social interaction',
        conservationStatus: 'Vulnerable',
        photographyTips: 'Capture them against the sunset for a silhouette'
      },
      {
        species: 'Lion',
        scientificName: 'Panthera leo',
        category: 'Mammal',
        abundance: 'Common',
        bestSpottingTime: 'Night or early morning',
        behavior: 'Hunting, resting',
        conservationStatus: 'Vulnerable',
        photographyTips: 'Use a telephoto lens to capture their intensity'
      },
      {
        species: 'Cheetah',
        scientificName: 'Acinonyx jubatus',
        category: 'Mammal',
        abundance: 'Uncommon',
        bestSpottingTime: 'Daytime',
        behavior: 'Hunting',
        conservationStatus: 'Vulnerable',
        photographyTips: 'Capture their speed with a fast shutter speed'
      }
    ],
    images: ['serengeti_1.jpg', 'serengeti_2.jpg'],
    activities: ['Game Drives', 'Hot Air Balloon Safaris', 'Walking Safaris'],
    accommodations: ['Luxury Lodges', 'Tented Camps'],
    featured: true,
    detailedGuide: {
      overview: 'The Serengeti is one of the most famous wildlife areas in the world.',
      geography: 'Endless plains dotted with rocky outcrops.',
      history: 'Maasai people have grazed their cattle here for centuries.',
      bestTimeToVisit: {
        drySeason: 'June to October for the Great Migration.',
        greenSeason: 'November to May for lush landscapes and bird watching.',
        photography: 'Golden light during the dry season.',
        birding: 'Green season for migratory birds.'
      },
      gettingThere: 'Fly into Kilimanjaro International Airport and transfer by road or air.',
      accommodation: 'From budget camping to luxury lodges.',
      packingTips: ['Light clothing', 'Sunscreen', 'Binoculars'],
      healthSafety: 'Consult your doctor about vaccinations and malaria prevention.',
      travelTips: ['Respect wildlife', 'Hire a local guide', 'Be patient for sightings']
    },
    seasonalInfo: {
      drySeason: {
        months: ['June', 'July', 'August', 'September', 'October'],
        description: 'The best time for wildlife viewing as animals congregate around water sources.',
        wildlife: 'Great Migration, predators hunting prey.',
        photography: 'Clear skies and golden light.',
        advantages: ['Easy wildlife spotting', 'Fewer mosquitoes'],
        disadvantages: ['Crowded', 'Dusty']
      },
      greenSeason: {
        months: ['November', 'December', 'January', 'February', 'March', 'April', 'May'],
        description: 'Lush landscapes and the calving season for wildebeest.',
        wildlife: 'Calving season, migratory birds.',
        photography: 'Dramatic skies and green landscapes.',
        advantages: ['Fewer tourists', 'Lower prices'],
        disadvantages: ['More mosquitoes', 'Some roads may be impassable']
      }
    },
    conservationInfo: {
      initiatives: ['Anti-poaching patrols', 'Community education programs'],
      challenges: ['Poaching', 'Habitat loss'],
      howTouristsHelp: ['Support eco-lodges', 'Respect park rules'],
      conservationFee: 60
    },
    culturalInfo: {
      tribes: ['Maasai'],
      languages: ['Maasai', 'Swahili'],
      traditions: ['Cattle herding', 'Traditional dances'],
      etiquette: ['Ask permission before taking photos', 'Respect their customs'],
      culturalSites: ['Maasai villages']
    }
  },
  {
    name: 'Masai Mara National Reserve',
    description: 'The Masai Mara is a large wildlife reserve in Narok County, Kenya, contiguous with the Serengeti National Park in Tanzania. It is globally famous for its exceptional populations of Masai lions, African leopards and cheetahs, and its annual migration of zebra, wildebeest, and gazelle.',
    country: 'Kenya',
    region: 'East Africa',
    coordinates: { lat: -1.583333, lng: 35.083332 },
    bestTimeToVisit: ['July to October (Dry Season)'],
    climate: 'Tropical Savannah',
    wildlife: [
      {
        species: 'African Elephant',
        scientificName: 'Loxodonta africana',
        category: 'Mammal',
        abundance: 'Common',
        bestSpottingTime: 'Early morning or late afternoon',
        behavior: 'Grazing, social interaction',
        conservationStatus: 'Vulnerable',
        photographyTips: 'Capture them against the sunset for a silhouette'
      },
      {
        species: 'Lion',
        scientificName: 'Panthera leo',
        category: 'Mammal',
        abundance: 'Common',
        bestSpottingTime: 'Night or early morning',
        behavior: 'Hunting, resting',
        conservationStatus: 'Vulnerable',
        photographyTips: 'Use a telephoto lens to capture their intensity'
      },
      {
        species: 'Cheetah',
        scientificName: 'Acinonyx jubatus',
        category: 'Mammal',
        abundance: 'Uncommon',
        bestSpottingTime: 'Daytime',
        behavior: 'Hunting',
        conservationStatus: 'Vulnerable',
        photographyTips: 'Capture their speed with a fast shutter speed'
      }
    ],
    images: ['masai_mara_1.jpg', 'masai_mara_2.jpg'],
    activities: ['Game Drives', 'Hot Air Balloon Safaris', 'Cultural Tours'],
    accommodations: ['Luxury Lodges', 'Tented Camps', 'Budget Campsites'],
    featured: true,
    detailedGuide: {
      overview: 'The Masai Mara is known for its stunning wildlife and cultural experiences.',
      geography: 'Open grasslands and acacia woodlands.',
      history: 'Named after the Maasai people and the Mara River.',
      bestTimeToVisit: {
        drySeason: 'July to October for the Great Migration.',
        greenSeason: 'November to June for fewer crowds and lush landscapes.',
        photography: 'Golden light during the dry season.',
        birding: 'Green season for migratory birds.'
      },
      gettingThere: 'Fly into Jomo Kenyatta International Airport and transfer by road or air.',
      accommodation: 'From budget campsites to luxury lodges.',
      packingTips: ['Light clothing', 'Sunscreen', 'Binoculars'],
      healthSafety: 'Consult your doctor about vaccinations and malaria prevention.',
      travelTips: ['Respect wildlife', 'Hire a local guide', 'Learn some Swahili phrases']
    },
    seasonalInfo: {
      drySeason: {
        months: ['July', 'August', 'September', 'October'],
        description: 'The best time for wildlife viewing as animals congregate around water sources.',
        wildlife: 'Great Migration, predators hunting prey.',
        photography: 'Clear skies and golden light.',
        advantages: ['Easy wildlife spotting', 'Fewer mosquitoes'],
        disadvantages: ['Crowded', 'Dusty']
      },
      greenSeason: {
        months: ['November', 'December', 'January', 'February', 'March', 'April', 'May', 'June'],
        description: 'Lush landscapes and the calving season for wildebeest.',
        wildlife: 'Calving season, migratory birds.',
        photography: 'Dramatic skies and green landscapes.',
        advantages: ['Fewer tourists', 'Lower prices'],
        disadvantages: ['More mosquitoes', 'Some roads may be impassable']
      }
    },
    conservationInfo: {
      initiatives: ['Anti-poaching patrols', 'Community conservancies'],
      challenges: ['Poaching', 'Human-wildlife conflict'],
      howTouristsHelp: ['Support community conservancies', 'Respect park rules'],
      conservationFee: 80
    },
    culturalInfo: {
      tribes: ['Maasai'],
      languages: ['Maasai', 'Swahili'],
      traditions: ['Cattle herding', 'Traditional dances'],
      etiquette: ['Ask permission before taking photos', 'Respect their customs'],
      culturalSites: ['Maasai villages']
    }
  }
];

// Sample Activities
export const sampleActivities = [
  {
    name: 'Game Drive',
    description: 'Explore the African savannah in a 4x4 vehicle, spotting wildlife along the way.',
    category: 'Wildlife Viewing',
    duration: '3-4 hours',
    difficulty: 'Easy',
    equipment: ['Binoculars', 'Camera'],
    price: 75,
    images: ['game_drive_1.jpg', 'game_drive_2.jpg']
  },
  {
    name: 'Hot Air Balloon Safari',
    description: 'Soar above the plains in a hot air balloon for a unique perspective on the landscape and wildlife.',
    category: 'Scenic',
    duration: '1 hour',
    difficulty: 'Easy',
    equipment: ['Camera'],
    price: 550,
    images: ['hot_air_balloon_1.jpg', 'hot_air_balloon_2.jpg']
  },
  {
    name: 'Walking Safari',
    description: 'Embark on a guided walking tour to get up close and personal with the flora and fauna of the African bush.',
    category: 'Adventure',
    duration: '2-3 hours',
    difficulty: 'Moderate',
    equipment: ['Hiking boots', 'Binoculars'],
    price: 60,
    images: ['walking_safari_1.jpg', 'walking_safari_2.jpg']
  }
];

// Sample Accommodations
export const sampleAccommodations = [
  {
    name: 'Serengeti Serena Safari Lodge',
    description: 'A luxury lodge in the heart of the Serengeti, offering stunning views and world-class amenities.',
    type: 'lodge',
    category: 'luxury',
    location: 'Serengeti National Park, Tanzania',
    coordinates: { lat: -2.333333, lng: 34.833332 },
    amenities: ['Swimming pool', 'Restaurant', 'Bar', 'Spa'],
    roomTypes: [
      {
        name: 'Standard Room',
        description: 'Comfortable room with a private balcony.',
        maxOccupancy: 2,
        price: 400,
        amenities: ['Private balcony', 'En-suite bathroom', 'Air conditioning'],
        images: ['serengeti_serena_standard_room.jpg'],
        wildlifeViewing: true
      },
      {
        name: 'Suite',
        description: 'Spacious suite with a separate living area.',
        maxOccupancy: 4,
        price: 600,
        amenities: ['Private balcony', 'En-suite bathroom', 'Air conditioning', 'Living area'],
        images: ['serengeti_serena_suite.jpg'],
        wildlifeViewing: true
      }
    ],
    images: ['serengeti_serena_lodge_1.jpg', 'serengeti_serena_lodge_2.jpg'],
    rating: 4.5,
    reviewCount: 120,
    pricePerNight: 400,
    maxGuests: 4,
    sustainability: {
      ecoFriendly: true,
      localCommunitySupport: true,
      conservationEfforts: ['Water conservation', 'Waste reduction'],
      sustainablePractices: ['Solar power', 'Local sourcing'],
      certifications: ['Green Globe']
    },
    wildlifeViewing: true,
    photographyFacilities: ['Photography workshops', 'Viewing platforms'],
    conservationPrograms: ['Wildlife monitoring', 'Habitat restoration'],
    culturalExperiences: ['Maasai cultural visits'],
    accessibility: ['Wheelchair accessible rooms'],
  },
  {
    name: 'Mara Serena Safari Lodge',
    description: 'A luxury lodge in the heart of the Masai Mara, offering stunning views and world-class amenities.',
    type: 'lodge',
    category: 'luxury',
    location: 'Masai Mara National Reserve, Kenya',
    coordinates: { lat: -1.583333, lng: 35.083332 },
    amenities: ['Swimming pool', 'Restaurant', 'Bar', 'Spa'],
    roomTypes: [
      {
        name: 'Standard Room',
        description: 'Comfortable room with a private balcony.',
        maxOccupancy: 2,
        price: 450,
        amenities: ['Private balcony', 'En-suite bathroom', 'Air conditioning'],
        images: ['mara_serena_standard_room.jpg'],
        wildlifeViewing: true
      },
      {
        name: 'Suite',
        description: 'Spacious suite with a separate living area.',
        maxOccupancy: 4,
        price: 650,
        amenities: ['Private balcony', 'En-suite bathroom', 'Air conditioning', 'Living area'],
        images: ['mara_serena_suite.jpg'],
        wildlifeViewing: true
      }
    ],
    images: ['mara_serena_lodge_1.jpg', 'mara_serena_lodge_2.jpg'],
    rating: 4.6,
    reviewCount: 150,
    pricePerNight: 450,
    maxGuests: 4,
    sustainability: {
      ecoFriendly: true,
      localCommunitySupport: true,
      conservationEfforts: ['Water conservation', 'Waste reduction'],
      sustainablePractices: ['Solar power', 'Local sourcing'],
      certifications: ['Green Globe']
    },
    wildlifeViewing: true,
    photographyFacilities: ['Photography workshops', 'Viewing platforms'],
    conservationPrograms: ['Wildlife monitoring', 'Habitat restoration'],
    culturalExperiences: ['Maasai cultural visits'],
    accessibility: ['Wheelchair accessible rooms'],
  }
];

// Sample Tours
export const sampleTours = [
  {
    title: '7-Day Serengeti and Ngorongoro Safari',
    description: 'Experience the best of Tanzania\'s wildlife on this 7-day safari adventure.',
    price: 3500,
    duration: '7 Days',
    location: 'Tanzania',
    destinations: ['Serengeti National Park', 'Ngorongoro Crater'],
    activities: ['Game Drives', 'Wildlife Viewing', 'Cultural Visits'],
    accommodations: ['Luxury Lodges', 'Tented Camps'],
    maxGroupSize: 6,
    minGroupSize: 2,
    difficulty: 'moderate',
    includes: ['Accommodation', 'Meals', 'Park Fees', 'Transportation'],
    excludes: ['Flights', 'Visa Fees', 'Personal Expenses'],
    images: ['serengeti_safari_1.jpg', 'serengeti_safari_2.jpg'],
    featured: true,
    status: 'active',
    rating: 4.7,
    reviewCount: 80,
    tourType: 'luxury',
    category: 'Wildlife Safari',
    accommodationLevel: 'Luxury',
    seasonality: {
      greenSeason: false,
      drySeason: true,
      bestMonths: ['June', 'July', 'August', 'September', 'October']
    },
    itinerary: [
      {
        day: 1,
        title: 'Arrival in Arusha',
        description: 'Arrive at Kilimanjaro International Airport and transfer to your hotel in Arusha.',
        accommodation: 'Mount Meru Hotel',
        meals: ['Dinner'],
        activities: [
          {
            time: 'Afternoon',
            activity: 'Relax and unwind at the hotel.',
            description: 'Enjoy the hotel amenities and prepare for your safari adventure.',
            duration: '4 hours',
            location: 'Arusha'
          }
        ],
        drivingTime: '0 hours',
        highlights: ['Relaxation', 'Preparation']
      },
      {
        day: 2,
        title: 'Arusha to Serengeti National Park',
        description: 'Fly from Arusha to the Serengeti National Park and enjoy an afternoon game drive.',
        accommodation: 'Serengeti Serena Safari Lodge',
        meals: ['Breakfast', 'Lunch', 'Dinner'],
        activities: [
          {
            time: 'Morning',
            activity: 'Fly from Arusha to Serengeti.',
            description: 'Enjoy the scenic flight over the African landscape.',
            duration: '1 hour',
            location: 'Arusha to Serengeti'
          },
          {
            time: 'Afternoon',
            activity: 'Afternoon game drive.',
            description: 'Explore the Serengeti and spot wildlife.',
            duration: '4 hours',
            location: 'Serengeti National Park'
          }
        ],
        drivingTime: '0 hours',
        highlights: ['Wildlife Viewing', 'Game Drive']
      }
    ],
    fitnessRequirements: {
      level: 'moderate',
      description: 'Good physical condition required for walking and game drives.',
      walkingDistance: 'Up to 3 km per day',
      terrain: 'Uneven terrain',
      ageRestrictions: '12+',
      medicalConditions: ['Consult your doctor']
    },
    equipment: {
      provided: [
        {
          name: 'Binoculars',
          description: 'High-quality binoculars for wildlife viewing.',
          category: 'Wildlife Viewing',
          optional: false
        }
      ],
      recommended: [
        {
          name: 'Camera',
          description: 'DSLR camera with a telephoto lens.',
          category: 'Photography',
          optional: true
        }
      ],
      required: [
        {
          name: 'Hiking boots',
          description: 'Sturdy hiking boots for walking safaris.',
          category: 'Footwear',
          optional: false
        }
      ]
    },
    groupOptions: [
      {
        type: 'private',
        minParticipants: 2,
        maxParticipants: 6,
        pricePerPerson: 4500,
        description: 'Enjoy a private safari experience with your group.'
      }
    ],
    specialFeatures: ['Expert Guides', 'Luxury Accommodations', 'Wildlife Guarantee'],
    difficultyDetails: 'Suitable for travelers with a moderate level of fitness.'
  }
];

export const seedFirebaseData = async () => {
  try {
    // Create destinations with timestamps
    for (const destination of sampleDestinations) {
      await FirebaseService.createDestination({
        ...destination,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    }

    // Create activities with timestamps
    for (const activity of sampleActivities) {
      await FirebaseService.createActivity({
        ...activity,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    }

    // Create accommodations with timestamps
    for (const accommodation of sampleAccommodations) {
      await FirebaseService.createAccommodation({
        ...accommodation,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    }

    // Create tours with timestamps
    for (const tour of sampleTours) {
      await FirebaseService.createTour({
        ...tour,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    }

    console.log('Sample data seeded successfully!');
  } catch (error) {
    console.error('Error seeding data:', error);
    throw error;
  }
};

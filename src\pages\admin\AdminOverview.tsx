
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, Users, Star, MessageSquare, Plane, FileText, Images } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import PageLoader from '@/components/ui/PageLoader';

const AdminOverview = () => {
  const [stats, setStats] = useState({
    totalTours: 0,
    totalBookings: 0,
    totalUsers: 0,
    totalReviews: 0,
    totalMessages: 0,
    totalBlogPosts: 0,
    totalGalleryImages: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const [tours, bookings, users, reviews, messages, blogPosts, galleryImages] = await Promise.all([
        FirebaseService.getTours(),
        FirebaseService.getAllBookings(),
        FirebaseService.getAllUsers(),
        FirebaseService.getAllReviews(),
        FirebaseService.getAllMessages(),
        FirebaseService.getBlogPosts(),
        FirebaseService.getGalleryImages()
      ]);

      setStats({
        totalTours: tours.length,
        totalBookings: bookings.length,
        totalUsers: users.length,
        totalReviews: reviews.length,
        totalMessages: messages.length,
        totalBlogPosts: blogPosts.length,
        totalGalleryImages: galleryImages.length
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      title: 'Total Tours',
      value: stats.totalTours,
      icon: Plane,
      color: 'text-safari-sunset'
    },
    {
      title: 'Total Bookings',
      value: stats.totalBookings,
      icon: Calendar,
      color: 'text-safari-coffee-brown'
    },
    {
      title: 'Total Users',
      value: stats.totalUsers,
      icon: Users,
      color: 'text-safari-burnt-sienna'
    },
    {
      title: 'Total Reviews',
      value: stats.totalReviews,
      icon: Star,
      color: 'text-safari-golden-sun'
    },
    {
      title: 'Messages',
      value: stats.totalMessages,
      icon: MessageSquare,
      color: 'text-safari-deep-rust'
    },
    {
      title: 'Blog Posts',
      value: stats.totalBlogPosts,
      icon: FileText,
      color: 'text-safari-warm-amber'
    },
    {
      title: 'Gallery Images',
      value: stats.totalGalleryImages,
      icon: Images,
      color: 'text-safari-dusty-rose'
    }
  ];

  if (loading) {
    return (
      <PageLoader
        title="Loading Admin Dashboard..."
        subtitle="Gathering your administrative data and statistics..."
      />
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl md:text-3xl font-bold text-foreground">Admin Dashboard</h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        {statCards.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs md:text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-xl md:text-2xl font-bold">{stat.value.toLocaleString()}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
              <button className="p-3 md:p-4 border border-border rounded-lg hover:bg-muted text-left transition-colors">
                <div className="font-medium text-sm md:text-base text-foreground">Add New Tour</div>
                <div className="text-xs md:text-sm text-muted-foreground">Create a new safari tour</div>
              </button>
              <button className="p-3 md:p-4 border border-border rounded-lg hover:bg-muted text-left transition-colors">
                <div className="font-medium text-sm md:text-base text-foreground">Add Blog Post</div>
                <div className="text-xs md:text-sm text-muted-foreground">Write a new blog article</div>
              </button>
              <button className="p-3 md:p-4 border rounded-lg hover:bg-gray-50 text-left">
                <div className="font-medium text-sm md:text-base">Add Gallery Image</div>
                <div className="text-xs md:text-sm text-gray-600">Upload new photos</div>
              </button>
              <button className="p-3 md:p-4 border rounded-lg hover:bg-gray-50 text-left">
                <div className="font-medium text-sm md:text-base">View Messages</div>
                <div className="text-xs md:text-sm text-gray-600">Check customer inquiries</div>
              </button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                No recent activity to display. Start managing your content to see updates here.
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminOverview;

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/contexts/AuthContext';
import { UserProfile } from '@/types/firebase';
import { calculateProfileCompletion, getProfileCompletionBadge } from '@/utils/profileCompletion';
import {
  Camera,
  Binoculars,
  Heart,
  Mountain,
  Utensils,
  Activity,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface UserProfilePreferencesProps {
  onSave?: () => void;
  showCompletionProgress?: boolean;
}

const UserProfilePreferences: React.FC<UserProfilePreferencesProps> = ({ 
  onSave, 
  showCompletionProgress = true 
}) => {
  const { userProfile, updateUserProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [preferences, setPreferences] = useState({
    accommodation: userProfile?.preferences?.accommodation || 'midrange',
    activities: userProfile?.preferences?.activities || [],
    dietaryRestrictions: userProfile?.preferences?.dietaryRestrictions || [],
    fitnessLevel: userProfile?.preferences?.fitnessLevel || 'moderate',
    photographyInterest: userProfile?.preferences?.photographyInterest || false,
    birdingInterest: userProfile?.preferences?.birdingInterest || false,
  });

  // Available options based on the codebase
  const accommodationOptions = [
    {
      value: 'budget',
      label: 'Budget Safari',
      description: 'Comfortable camping and budget lodges with essential amenities',
      icon: '🏕️'
    },
    {
      value: 'midrange',
      label: 'Mid-Range Safari',
      description: 'Comfortable lodges and tented camps with good amenities',
      icon: '🏨'
    },
    {
      value: 'luxury',
      label: 'Luxury Safari',
      description: 'Premium lodges and camps with exceptional service',
      icon: '✨'
    }
  ];

  const activityOptions = [
    'Game Drives',
    'Walking Safaris',
    'Hot Air Balloon',
    'Cultural Village Visits',
    'Night Drives',
    'Bush Camping',
    'Photography Workshops',
    'Conservation Activities',
    'Bird Watching',
    'Bush Breakfast',
    'Sundowner',
    'Big Five Safari',
    'Great Migration'
  ];

  const dietaryOptions = [
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Dairy-Free',
    'Nut Allergies',
    'Halal',
    'Kosher',
    'Low Sodium',
    'Diabetic Friendly',
    'No Seafood',
    'No Pork',
    'No Beef'
  ];

  const fitnessLevelOptions = [
    {
      value: 'low',
      label: 'Low Fitness',
      description: 'Prefer minimal walking, comfortable vehicle-based activities',
      icon: '🚗'
    },
    {
      value: 'moderate',
      label: 'Moderate Fitness',
      description: 'Comfortable with some walking and light physical activities',
      icon: '🚶'
    },
    {
      value: 'high',
      label: 'High Fitness',
      description: 'Enjoy challenging walks, hiking, and active adventures',
      icon: '🥾'
    }
  ];

  // Calculate profile completion percentage using the utility
  const getCompletionData = () => {
    // Create a temporary profile with current preferences for calculation
    const tempProfile = userProfile ? {
      ...userProfile,
      preferences: preferences
    } : null;

    return calculateProfileCompletion(tempProfile);
  };

  const handleActivityToggle = (activity: string) => {
    setPreferences(prev => ({
      ...prev,
      activities: prev.activities.includes(activity)
        ? prev.activities.filter(a => a !== activity)
        : [...prev.activities, activity]
    }));
  };

  const handleDietaryToggle = (dietary: string) => {
    setPreferences(prev => ({
      ...prev,
      dietaryRestrictions: prev.dietaryRestrictions.includes(dietary)
        ? prev.dietaryRestrictions.filter(d => d !== dietary)
        : [...prev.dietaryRestrictions, dietary]
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      await updateUserProfile({ preferences });
      onSave?.();
    } catch (error) {
      console.error('Error updating preferences:', error);
    } finally {
      setLoading(false);
    }
  };

  const completionData = getCompletionData();
  const badge = getProfileCompletionBadge(completionData.percentage);

  return (
    <div className="space-y-6">
      {showCompletionProgress && (
        <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-[#F2EEE6] font-['Cormorant_Garamond'] font-light">
              <CheckCircle className="h-5 w-5 text-[#D4C2A4]" />
              Safari Preferences
              <Badge className={`${badge.color} border-0 ml-auto bg-[#D4C2A4]/20 text-[#D4C2A4]`}>
                <span className="mr-1">{badge.icon}</span>
                {badge.label}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm font-['Open_Sans']">
                <span className="text-[#A9A9A9]">Complete your preferences to get personalized recommendations</span>
                <span className="font-medium text-[#D4C2A4]">{completionData.percentage}%</span>
              </div>
              <Progress value={completionData.percentage} className="h-2 bg-[#16191D]/50" />
              {completionData.suggestions.length > 0 && (
                <div className="mt-3">
                  <p className="text-sm font-medium text-[#D4C2A4] mb-1 font-['Open_Sans']">Next steps:</p>
                  <ul className="text-sm text-[#A9A9A9] space-y-1 font-['Open_Sans']">
                    {completionData.suggestions.map((suggestion, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <AlertCircle className="h-3 w-3 text-[#D4C2A4]" />
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Accommodation Preferences */}
      <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F2EEE6] font-['Cormorant_Garamond'] font-light">
            <Heart className="h-5 w-5 text-[#D4C2A4]" />
            Accommodation Preferences
          </CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={preferences.accommodation}
            onValueChange={(value) => setPreferences(prev => ({ ...prev, accommodation: value as any }))}
            className="space-y-4"
          >
            {accommodationOptions.map((option) => (
              <div key={option.value} className="flex items-start space-x-3 p-3 border border-[#D4C2A4]/20 rounded-lg hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/40 transition-all duration-300 bg-[#16191D]/30">
                <RadioGroupItem value={option.value} id={option.value} className="mt-1 border-[#D4C2A4]/50 text-[#D4C2A4]" />
                <div className="flex-1">
                  <Label htmlFor={option.value} className="flex items-center gap-2 font-medium cursor-pointer text-[#F2EEE6] font-['Open_Sans']">
                    <span className="text-lg">{option.icon}</span>
                    {option.label}
                  </Label>
                  <p className="text-sm text-[#A9A9A9] mt-1 font-['Open_Sans']">{option.description}</p>
                </div>
              </div>
            ))}
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Activity Interests */}
      <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F2EEE6] font-['Cormorant_Garamond'] font-light">
            <Activity className="h-5 w-5 text-[#D4C2A4]" />
            Activity Interests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-[#A9A9A9] mb-4 font-['Open_Sans']">
            Select the activities you're most interested in during your safari
          </p>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {activityOptions.map((activity) => (
              <div key={activity} className="flex items-center space-x-2">
                <Checkbox
                  id={activity}
                  checked={preferences.activities.includes(activity)}
                  onCheckedChange={() => handleActivityToggle(activity)}
                  className="border-[#D4C2A4]/50 data-[state=checked]:bg-[#D4C2A4] data-[state=checked]:border-[#D4C2A4]"
                />
                <Label htmlFor={activity} className="text-sm cursor-pointer text-[#F2EEE6] font-['Open_Sans']">
                  {activity}
                </Label>
              </div>
            ))}
          </div>
          {preferences.activities.length > 0 && (
            <div className="mt-4">
              <p className="text-sm font-medium mb-2 text-[#D4C2A4] font-['Open_Sans']">Selected activities:</p>
              <div className="flex flex-wrap gap-2">
                {preferences.activities.map((activity) => (
                  <Badge key={activity} className="bg-[#D4C2A4]/20 text-[#D4C2A4] border-[#D4C2A4]/30 font-['Open_Sans']">
                    {activity}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Fitness Level */}
      <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F2EEE6] font-['Cormorant_Garamond'] font-light">
            <Mountain className="h-5 w-5 text-[#D4C2A4]" />
            Fitness Level
          </CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={preferences.fitnessLevel}
            onValueChange={(value) => setPreferences(prev => ({ ...prev, fitnessLevel: value as any }))}
            className="space-y-4"
          >
            {fitnessLevelOptions.map((option) => (
              <div key={option.value} className="flex items-start space-x-3 p-3 border border-[#D4C2A4]/20 rounded-lg hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/40 transition-all duration-300 bg-[#16191D]/30">
                <RadioGroupItem value={option.value} id={option.value} className="mt-1 border-[#D4C2A4]/50 text-[#D4C2A4]" />
                <div className="flex-1">
                  <Label htmlFor={option.value} className="flex items-center gap-2 font-medium cursor-pointer text-[#F2EEE6] font-['Open_Sans']">
                    <span className="text-lg">{option.icon}</span>
                    {option.label}
                  </Label>
                  <p className="text-sm text-[#A9A9A9] mt-1 font-['Open_Sans']">{option.description}</p>
                </div>
              </div>
            ))}
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Special Interests */}
      <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F2EEE6] font-['Cormorant_Garamond'] font-light">
            <Camera className="h-5 w-5 text-[#D4C2A4]" />
            Special Interests
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between p-4 border border-[#D4C2A4]/20 rounded-lg bg-[#16191D]/30 hover:border-[#D4C2A4]/40 transition-all duration-300">
            <div className="flex items-center gap-3">
              <Camera className="h-5 w-5 text-[#D4C2A4]" />
              <div>
                <Label className="font-medium text-[#F2EEE6] font-['Open_Sans']">Photography Interest</Label>
                <p className="text-sm text-[#A9A9A9] font-['Open_Sans']">
                  Interested in photography workshops and wildlife photography opportunities
                </p>
              </div>
            </div>
            <Switch
              checked={preferences.photographyInterest}
              onCheckedChange={(checked) =>
                setPreferences(prev => ({ ...prev, photographyInterest: checked }))
              }
              className="data-[state=checked]:bg-[#D4C2A4] data-[state=unchecked]:bg-[#16191D]/50"
            />
          </div>

          <div className="flex items-center justify-between p-4 border border-[#D4C2A4]/20 rounded-lg bg-[#16191D]/30 hover:border-[#D4C2A4]/40 transition-all duration-300">
            <div className="flex items-center gap-3">
              <Binoculars className="h-5 w-5 text-[#D4C2A4]" />
              <div>
                <Label className="font-medium text-[#F2EEE6] font-['Open_Sans']">Bird Watching Interest</Label>
                <p className="text-sm text-[#A9A9A9] font-['Open_Sans']">
                  Interested in bird watching activities and specialized birding tours
                </p>
              </div>
            </div>
            <Switch
              checked={preferences.birdingInterest}
              onCheckedChange={(checked) =>
                setPreferences(prev => ({ ...prev, birdingInterest: checked }))
              }
              className="data-[state=checked]:bg-[#D4C2A4] data-[state=unchecked]:bg-[#16191D]/50"
            />
          </div>
        </CardContent>
      </Card>

      {/* Dietary Restrictions */}
      <Card className="bg-[#1a1e23]/50 backdrop-blur-sm border border-[#D4C2A4]/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F2EEE6] font-['Cormorant_Garamond'] font-light">
            <Utensils className="h-5 w-5 text-[#D4C2A4]" />
            Dietary Restrictions & Preferences
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-[#A9A9A9] mb-4 font-['Open_Sans']">
            Let us know about any dietary restrictions or preferences you have
          </p>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {dietaryOptions.map((dietary) => (
              <div key={dietary} className="flex items-center space-x-2">
                <Checkbox
                  id={dietary}
                  checked={preferences.dietaryRestrictions.includes(dietary)}
                  onCheckedChange={() => handleDietaryToggle(dietary)}
                  className="border-[#D4C2A4]/50 data-[state=checked]:bg-[#D4C2A4] data-[state=checked]:border-[#D4C2A4]"
                />
                <Label htmlFor={dietary} className="text-sm cursor-pointer text-[#F2EEE6] font-['Open_Sans']">
                  {dietary}
                </Label>
              </div>
            ))}
          </div>
          {preferences.dietaryRestrictions.length > 0 && (
            <div className="mt-4">
              <p className="text-sm font-medium mb-2 text-[#D4C2A4] font-['Open_Sans']">Your dietary restrictions:</p>
              <div className="flex flex-wrap gap-2">
                {preferences.dietaryRestrictions.map((dietary) => (
                  <Badge key={dietary} className="bg-[#D4C2A4]/20 text-[#D4C2A4] border-[#D4C2A4]/30 font-['Open_Sans']">
                    {dietary}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={loading}
          className="min-w-32 bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 transition-all duration-300 font-['Open_Sans'] font-medium"
        >
          {loading ? 'Saving...' : 'Save Preferences'}
        </Button>
      </div>
    </div>
  );
};

export default UserProfilePreferences;

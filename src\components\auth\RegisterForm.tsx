
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { Mail, Lock, User, Eye, EyeOff } from 'lucide-react';

const RegisterForm = () => {
  const [formData, setFormData] = useState({
    displayName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  
  const { register } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    setLoading(true);

    try {
      await register(formData.email, formData.password, formData.displayName);
      // Small delay to ensure profile is created before navigation
      setTimeout(() => {
        navigate('/user-dashboard');
      }, 100);
    } catch (error: any) {
      let errorMessage = 'Failed to create account';

      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'An account with this email already exists.';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'Password should be at least 6 characters long.';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Please enter a valid email address.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }));
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Ultra-Luxury Glass Container */}
      <div className="relative group">
        {/* Premium Glass Effect Background */}
        <div className="luxury-glass-container rounded-2xl shadow-2xl overflow-hidden transition-all duration-700">
          {/* Multi-layer Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#D4C2A4]/8 via-transparent to-[#D4C2A4]/3 pointer-events-none"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-white/3 via-transparent to-white/1 pointer-events-none"></div>

          {/* Luxury Border Glow */}
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#D4C2A4]/20 via-transparent to-[#D4C2A4]/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>

          <div className="relative p-6 md:p-8 lg:p-10">
            {/* Premium Header */}
            <div className="text-center mb-8 md:mb-10">
              <div className="relative inline-block mb-3 md:mb-4">
               
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-12 h-0.5 bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent"></div>
              </div>
              <p className="text-[#A9A9A9] font-['Open_Sans'] text-xs md:text-sm tracking-wider uppercase opacity-80">
                Create your exclusive safari account
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6 md:space-y-8">
              {error && (
                <div className="bg-red-500/10 backdrop-blur-sm border border-red-500/30 rounded-xl p-4 md:p-5">
                  <p className="text-red-300 text-sm md:text-base font-['Open_Sans'] text-center">{error}</p>
                </div>
              )}

              {/* Premium Full Name Field */}
              <div className="space-y-3">
                <label htmlFor="displayName" className="block text-[#A9A9A9] font-['Open_Sans'] text-xs md:text-sm tracking-wider uppercase font-medium">
                  Full Name
                </label>
                <div className="relative group luxury-input-field">
                  <User className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#A9A9A9] group-focus-within:text-[#D4C2A4] transition-colors duration-300" />
                  <input
                    id="displayName"
                    type="text"
                    placeholder="Enter your distinguished name"
                    value={formData.displayName}
                    onChange={handleInputChange('displayName')}
                    className="w-full bg-[#16191D]/60 backdrop-blur-sm border border-[#A9A9A9]/30 rounded-xl pl-12 pr-4 py-4 md:py-5 text-[#F2EEE6] placeholder-[#A9A9A9]/50 focus:border-[#D4C2A4] focus:bg-[#16191D]/80 focus:outline-none focus:ring-2 focus:ring-[#D4C2A4]/20 transition-all duration-500 font-['Open_Sans'] text-sm md:text-base hover:border-[#A9A9A9]/50 luxury-typography"
                    required
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-[#D4C2A4]/5 to-transparent opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>

              {/* Premium Email Field */}
              <div className="space-y-3">
                <label htmlFor="email" className="block text-[#A9A9A9] font-['Open_Sans'] text-xs md:text-sm tracking-wider uppercase font-medium">
                  Email Address
                </label>
                <div className="relative group">
                  <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#A9A9A9] group-focus-within:text-[#D4C2A4] transition-colors duration-300" />
                  <input
                    id="email"
                    type="email"
                    placeholder="Enter your exclusive email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    className="w-full bg-[#16191D]/60 backdrop-blur-sm border border-[#A9A9A9]/30 rounded-xl pl-12 pr-4 py-4 md:py-5 text-[#F2EEE6] placeholder-[#A9A9A9]/50 focus:border-[#D4C2A4] focus:bg-[#16191D]/80 focus:outline-none focus:ring-2 focus:ring-[#D4C2A4]/20 transition-all duration-500 font-['Open_Sans'] text-sm md:text-base hover:border-[#A9A9A9]/50"
                    required
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-[#D4C2A4]/5 to-transparent opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>

              {/* Premium Password Field */}
              <div className="space-y-3">
                <label htmlFor="password" className="block text-[#A9A9A9] font-['Open_Sans'] text-xs md:text-sm tracking-wider uppercase font-medium">
                  Password
                </label>
                <div className="relative group">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#A9A9A9] group-focus-within:text-[#D4C2A4] transition-colors duration-300" />
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Create a secure password"
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    className="w-full bg-[#16191D]/60 backdrop-blur-sm border border-[#A9A9A9]/30 rounded-xl pl-12 pr-12 py-4 md:py-5 text-[#F2EEE6] placeholder-[#A9A9A9]/50 focus:border-[#D4C2A4] focus:bg-[#16191D]/80 focus:outline-none focus:ring-2 focus:ring-[#D4C2A4]/20 transition-all duration-500 font-['Open_Sans'] text-sm md:text-base hover:border-[#A9A9A9]/50"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[#A9A9A9] hover:text-[#D4C2A4] transition-all duration-300 p-1 rounded-lg hover:bg-[#D4C2A4]/10"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-[#D4C2A4]/5 to-transparent opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>

              {/* Premium Confirm Password Field */}
              <div className="space-y-3">
                <label htmlFor="confirmPassword" className="block text-[#A9A9A9] font-['Open_Sans'] text-xs md:text-sm tracking-wider uppercase font-medium">
                  Confirm Password
                </label>
                <div className="relative group">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#A9A9A9] group-focus-within:text-[#D4C2A4] transition-colors duration-300" />
                  <input
                    id="confirmPassword"
                    type="password"
                    placeholder="Confirm your secure password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange('confirmPassword')}
                    className="w-full bg-[#16191D]/60 backdrop-blur-sm border border-[#A9A9A9]/30 rounded-xl pl-12 pr-4 py-4 md:py-5 text-[#F2EEE6] placeholder-[#A9A9A9]/50 focus:border-[#D4C2A4] focus:bg-[#16191D]/80 focus:outline-none focus:ring-2 focus:ring-[#D4C2A4]/20 transition-all duration-500 font-['Open_Sans'] text-sm md:text-base hover:border-[#A9A9A9]/50"
                    required
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-[#D4C2A4]/5 to-transparent opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>

              {/* Ultra-Premium Submit Button */}
              <button
                type="submit"
                disabled={loading}
                className="luxury-button group relative w-full text-[#16191D] font-['Open_Sans'] font-semibold py-4 md:py-5 px-6 rounded-xl transition-all duration-500 transform hover:scale-[1.02] hover:shadow-xl hover:shadow-[#D4C2A4]/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none tracking-wider uppercase text-sm md:text-base overflow-hidden luxury-typography"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <span className="relative z-10">
                  {loading ? 'Creating Account...' : 'Create Account'}
                </span>
                {loading && (
                  <div className="absolute inset-0 bg-gradient-to-r from-[#D4C2A4]/80 to-[#D4C2A4]/60 animate-pulse"></div>
                )}
              </button>

              {/* Premium Footer */}
              <div className="text-center pt-6 md:pt-8 border-t border-[#D4C2A4]/20">
                <p className="text-[#A9A9A9] font-['Open_Sans'] text-sm md:text-base">
                  Already have an account?{' '}
                  <Link
                    to="/login"
                    className="text-[#D4C2A4] hover:text-[#F2EEE6] transition-all duration-300 font-medium hover:underline underline-offset-4 decoration-[#D4C2A4]/50"
                  >
                    Sign In
                  </Link>
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterForm;

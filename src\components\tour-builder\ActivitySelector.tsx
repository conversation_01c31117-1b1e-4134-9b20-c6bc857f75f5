
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Camera, Binoculars, Mountain, Users, Heart, Sun } from 'lucide-react';

interface Activity {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  price: number;
  destinations: string[];
  category: string;
}

interface ActivitySelectorProps {
  selectedActivities: string[];
  onActivitiesChange: (activities: string[]) => void;
  destinations: string[];
}

const ActivitySelector: React.FC<ActivitySelectorProps> = ({
  selectedActivities,
  onActivitiesChange,
  destinations
}) => {
  const activities: Activity[] = [
    {
      id: 'game-drives',
      name: 'Game Drives',
      description: 'Traditional safari game drives to spot wildlife',
      icon: Binoculars,
      price: 0, // Included
      destinations: ['serengeti', 'ngorongoro', 'tarangire', 'manyara'],
      category: 'Wildlife'
    },
    {
      id: 'photography-tour',
      name: 'Photography Workshop',
      description: 'Professional photography guidance and workshops',
      icon: Camera,
      price: 150,
      destinations: ['serengeti', 'ngorongoro'],
      category: 'Photography'
    },
    {
      id: 'cultural-visit',
      name: 'Cultural Village Visit',
      description: 'Visit local Maasai villages and learn about their culture',
      icon: Users,
      price: 75,
      destinations: ['ngorongoro', 'manyara'],
      category: 'Cultural'
    },
    {
      id: 'walking-safari',
      name: 'Walking Safari',
      description: 'Guided walking safaris for closer wildlife encounters',
      icon: Mountain,
      price: 100,
      destinations: ['tarangire', 'manyara'],
      category: 'Adventure'
    },
    {
      id: 'balloon-safari',
      name: 'Hot Air Balloon Safari',
      description: 'Aerial views of the Serengeti with champagne breakfast',
      icon: Sun,
      price: 450,
      destinations: ['serengeti'],
      category: 'Luxury'
    },
    {
      id: 'conservation-tour',
      name: 'Conservation Experience',
      description: 'Learn about wildlife conservation efforts',
      icon: Heart,
      price: 50,
      destinations: ['serengeti', 'ngorongoro', 'tarangire'],
      category: 'Educational'
    }
  ];

  // Filter activities based on selected destinations
  const availableActivities = activities.filter(activity => 
    activity.destinations.some(dest => destinations.includes(dest))
  );

  const handleActivityToggle = (activityId: string) => {
    if (selectedActivities.includes(activityId)) {
      onActivitiesChange(selectedActivities.filter(id => id !== activityId));
    } else {
      onActivitiesChange([...selectedActivities, activityId]);
    }
  };

  const categories = [...new Set(availableActivities.map(a => a.category))];

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-4">Select Your Activities</h2>
        <p className="text-gray-600">
          Choose from a variety of activities to enhance your safari experience
        </p>
      </div>

      {categories.map(category => (
        <div key={category}>
          <h3 className="text-lg font-semibold mb-4 text-gray-800">{category} Activities</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableActivities
              .filter(activity => activity.category === category)
              .map((activity) => {
                const IconComponent = activity.icon;
                const isSelected = selectedActivities.includes(activity.id);
                
                return (
                  <Card 
                    key={activity.id}
                    className={`cursor-pointer transition-all ${
                      isSelected ? 'ring-2 ring-orange-500 bg-orange-50' : 'hover:shadow-md'
                    }`}
                    onClick={() => handleActivityToggle(activity.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg ${
                            isSelected ? 'bg-orange-600 text-white' : 'bg-gray-100 text-gray-600'
                          }`}>
                            <IconComponent className="h-5 w-5" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-sm">{activity.name}</h4>
                          </div>
                        </div>
                        <Checkbox 
                          checked={isSelected}
                          onChange={() => {}} // Handled by card click
                        />
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">{activity.description}</p>
                      
                      <div className="flex justify-between items-center">
                        <div className="text-sm">
                          {activity.price === 0 ? (
                            <Badge variant="secondary">Included</Badge>
                          ) : (
                            <span className="font-semibold text-orange-600">
                              +${activity.price}
                            </span>
                          )}
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {activity.category}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
          </div>
        </div>
      ))}

      {selectedActivities.length > 0 && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <h4 className="font-semibold mb-2">Selected Activities</h4>
            <div className="flex flex-wrap gap-2">
              {selectedActivities.map((id) => {
                const activity = activities.find(a => a.id === id);
                return activity ? (
                  <Badge key={id} className="bg-orange-600">
                    {activity.name}
                    {activity.price > 0 && ` (+$${activity.price})`}
                  </Badge>
                ) : null;
              })}
            </div>
            <div className="mt-2 text-sm text-gray-600">
              Additional cost: $
              {selectedActivities.reduce((total, id) => {
                const activity = activities.find(a => a.id === id);
                return total + (activity?.price || 0);
              }, 0)}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ActivitySelector;

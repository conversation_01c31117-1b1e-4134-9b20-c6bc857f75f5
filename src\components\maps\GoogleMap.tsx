
import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MapPin, Navigation, Route } from 'lucide-react';

interface Destination {
  id: string;
  name: string;
  lat: number;
  lng: number;
  description: string;
  image: string;
}

interface GoogleMapProps {
  destinations: Destination[];
  onDestinationClick?: (destination: Destination) => void;
  showRoutes?: boolean;
  height?: string;
}

const GoogleMap: React.FC<GoogleMapProps> = ({ 
  destinations, 
  onDestinationClick, 
  showRoutes = false,
  height = "400px" 
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [apiKey, setApiKey] = useState('');
  const [isApiKeySet, setIsApiKeySet] = useState(false);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);

  const initializeMap = () => {
    if (!mapRef.current || !window.google || !isApiKeySet) return;

    const mapInstance = new window.google.maps.Map(mapRef.current, {
      center: { lat: -3.5, lng: 35.0 }, // Central Tanzania
      zoom: 7,
      styles: [
        {
          featureType: "poi",
          elementType: "labels",
          stylers: [{ visibility: "off" }]
        }
      ]
    });

    setMap(mapInstance);

    // Add markers for each destination
    const newMarkers = destinations.map(destination => {
      const marker = new window.google.maps.Marker({
        position: { lat: destination.lat, lng: destination.lng },
        map: mapInstance,
        title: destination.name,
        icon: {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="#ea580c" stroke="#ffffff" stroke-width="2">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
              <circle cx="12" cy="10" r="3"/>
            </svg>
          `),
          scaledSize: new window.google.maps.Size(32, 32),
        }
      });

      const infoWindow = new window.google.maps.InfoWindow({
        content: `
          <div style="padding: 10px; max-width: 200px;">
            <img src="https://images.unsplash.com/${destination.image}?auto=format&fit=crop&w=200&h=120" 
                 style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px; margin-bottom: 8px;" />
            <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 16px; font-weight: bold;">${destination.name}</h3>
            <p style="margin: 0; color: #6b7280; font-size: 14px; line-height: 1.4;">${destination.description}</p>
          </div>
        `
      });

      marker.addListener('click', () => {
        infoWindow.open(mapInstance, marker);
        if (onDestinationClick) {
          onDestinationClick(destination);
        }
      });

      return marker;
    });

    setMarkers(newMarkers);

    // Show routes if enabled
    if (showRoutes && destinations.length > 1) {
      const directionsService = new window.google.maps.DirectionsService();
      const directionsRenderer = new window.google.maps.DirectionsRenderer({
        suppressMarkers: true,
        polylineOptions: {
          strokeColor: '#ea580c',
          strokeWeight: 3,
          strokeOpacity: 0.8
        }
      });
      directionsRenderer.setMap(mapInstance);

      const waypoints = destinations.slice(1, -1).map(dest => ({
        location: { lat: dest.lat, lng: dest.lng },
        stopover: true
      }));

      directionsService.route({
        origin: { lat: destinations[0].lat, lng: destinations[0].lng },
        destination: { lat: destinations[destinations.length - 1].lat, lng: destinations[destinations.length - 1].lng },
        waypoints: waypoints,
        travelMode: window.google.maps.TravelMode.DRIVING,
      }, (result, status) => {
        if (status === 'OK' && result) {
          directionsRenderer.setDirections(result);
        }
      });
    }
  };

  const loadGoogleMapsScript = () => {
    if (window.google || !apiKey) return;

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
    script.async = true;
    script.defer = true;
    script.onload = initializeMap;
    document.head.appendChild(script);
  };

  useEffect(() => {
    if (isApiKeySet) {
      loadGoogleMapsScript();
    }
  }, [isApiKeySet, destinations]);

  const handleApiKeySubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (apiKey.trim()) {
      setIsApiKeySet(true);
      loadGoogleMapsScript();
    }
  };

  if (!isApiKeySet) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="mr-2 h-5 w-5 text-orange-600" />
            Interactive Safari Map
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-4">
            <p className="text-gray-600">
              To display the interactive map, please enter your Google Maps API key.
            </p>
            <form onSubmit={handleApiKeySubmit} className="flex gap-2">
              <Input
                type="text"
                placeholder="Enter Google Maps API Key"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                className="flex-1"
              />
              <Button type="submit" className="bg-orange-600 hover:bg-orange-700">
                Load Map
              </Button>
            </form>
            <p className="text-xs text-gray-500">
              Get your API key from{' '}
              <a 
                href="https://console.cloud.google.com/google/maps-apis" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-orange-600 hover:underline"
              >
                Google Cloud Console
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <MapPin className="mr-2 h-5 w-5 text-orange-600" />
            Interactive Safari Map
          </span>
          {showRoutes && (
            <Button variant="outline" size="sm">
              <Route className="mr-2 h-4 w-4" />
              Route Planning
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div 
          ref={mapRef} 
          style={{ height, width: '100%' }}
          className="rounded-lg border border-gray-200"
        />
        <div className="mt-4 text-sm text-gray-600">
          Click on any marker to see destination details and tour options.
        </div>
      </CardContent>
    </Card>
  );
};

export default GoogleMap;


import React, { useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface TourBuilderData {
  destinations: string[];
  accommodation: string;
  activities: string[];
  duration: number;
  groupSize: number;
  startDate: Date | null;
  itinerary: Array<{
    day: number;
    destination: string;
    activities: string[];
    accommodation: string;
  }>;
  totalPrice: number;
}

interface PricingCalculatorProps {
  tourData: TourBuilderData;
  onPriceChange: (price: number) => void;
}

const PricingCalculator: React.FC<PricingCalculatorProps> = ({ 
  tourData, 
  onPriceChange 
}) => {
  const basePricePerDay = 200; // Base price per person per day
  
  const accommodationMultipliers = {
    budget: 1.0,
    midrange: 1.5,
    luxury: 2.5
  };

  const destinationPrices = {
    serengeti: 150,
    ngorongoro: 100,
    tarangire: 80,
    manyara: 70
  };

  const activityPrices = {
    'game-drives': 0,
    'photography-tour': 150,
    'cultural-visit': 75,
    'walking-safari': 100,
    'balloon-safari': 450,
    'conservation-tour': 50
  };

  const calculatePrice = () => {
    let totalPrice = 0;

    // Base price calculation
    const basePrice = basePricePerDay * tourData.duration;
    
    // Accommodation multiplier
    const accommodationMultiplier = accommodationMultipliers[tourData.accommodation as keyof typeof accommodationMultipliers] || 1;
    
    // Destination fees
    const destinationFees = tourData.destinations.reduce((total, dest) => {
      return total + (destinationPrices[dest as keyof typeof destinationPrices] || 0);
    }, 0);

    // Activity fees
    const activityFees = tourData.activities.reduce((total, activity) => {
      return total + (activityPrices[activity as keyof typeof activityPrices] || 0);
    }, 0);

    // Group size discount (for groups larger than 4)
    const groupDiscount = tourData.groupSize > 4 ? 0.1 : 0;

    totalPrice = (basePrice * accommodationMultiplier) + destinationFees + activityFees;
    totalPrice = totalPrice * (1 - groupDiscount);

    return Math.round(totalPrice);
  };

  useEffect(() => {
    const price = calculatePrice();
    onPriceChange(price);
  }, [tourData]);

  const breakdown = {
    basePrice: basePricePerDay * tourData.duration,
    accommodationMultiplier: accommodationMultipliers[tourData.accommodation as keyof typeof accommodationMultipliers] || 1,
    destinationFees: tourData.destinations.reduce((total, dest) => {
      return total + (destinationPrices[dest as keyof typeof destinationPrices] || 0);
    }, 0),
    activityFees: tourData.activities.reduce((total, activity) => {
      return total + (activityPrices[activity as keyof typeof activityPrices] || 0);
    }, 0),
    groupDiscount: tourData.groupSize > 4 ? 0.1 : 0
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">Price Breakdown</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Base Price */}
        <div className="flex justify-between">
          <span>Base price ({tourData.duration} days)</span>
          <span>${breakdown.basePrice.toLocaleString()}</span>
        </div>

        {/* Accommodation Multiplier */}
        <div className="flex justify-between">
          <span>Accommodation ({tourData.accommodation})</span>
          <span>x{breakdown.accommodationMultiplier}</span>
        </div>

        {/* Destination Fees */}
        {breakdown.destinationFees > 0 && (
          <div className="flex justify-between">
            <span>Park fees & permits</span>
            <span>${breakdown.destinationFees.toLocaleString()}</span>
          </div>
        )}

        {/* Activity Fees */}
        {breakdown.activityFees > 0 && (
          <>
            <div className="flex justify-between">
              <span>Additional activities</span>
              <span>${breakdown.activityFees.toLocaleString()}</span>
            </div>
            <div className="pl-4 space-y-1 text-sm text-gray-600">
              {tourData.activities.map(activity => {
                const price = activityPrices[activity as keyof typeof activityPrices];
                if (price > 0) {
                  return (
                    <div key={activity} className="flex justify-between">
                      <span>• {activity.replace('-', ' ')}</span>
                      <span>${price}</span>
                    </div>
                  );
                }
                return null;
              })}
            </div>
          </>
        )}

        {/* Group Discount */}
        {breakdown.groupDiscount > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Group discount ({tourData.groupSize}+ people)</span>
            <span>-{(breakdown.groupDiscount * 100).toFixed(0)}%</span>
          </div>
        )}

        <Separator />

        {/* Total */}
        <div className="flex justify-between text-xl font-bold">
          <span>Total per person</span>
          <span className="text-orange-600">${tourData.totalPrice.toLocaleString()}</span>
        </div>

        <div className="text-sm text-gray-600 text-center">
          Total for {tourData.groupSize} {tourData.groupSize === 1 ? 'person' : 'people'}: 
          <span className="font-semibold ml-1">
            ${(tourData.totalPrice * tourData.groupSize).toLocaleString()}
          </span>
        </div>

        {/* What's Included */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2">What's Included:</h4>
          <ul className="text-sm space-y-1 text-gray-600">
            <li>• Professional safari guide</li>
            <li>• Safari vehicle with pop-up roof</li>
            <li>• All selected accommodation</li>
            <li>• All meals as per itinerary</li>
            <li>• Park entry fees</li>
            <li>• All selected activities</li>
            <li>• Airport transfers</li>
            <li>• Bottled water during game drives</li>
          </ul>
        </div>

        {/* Seasonal Pricing Note */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> Prices may vary based on seasonal demand and availability. 
            High season (June-October & December-January) may have premium rates.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default PricingCalculator;

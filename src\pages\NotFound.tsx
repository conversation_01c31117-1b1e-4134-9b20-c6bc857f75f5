import { useLocation, Link } from "react-router-dom";
import { useEffect, useState } from "react";
import { Crown, Home, ArrowLeft, <PERSON>rk<PERSON>, Star } from "lucide-react";

const NotFound = () => {
  const location = useLocation();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Track 404 errors for analytics if needed
    setIsVisible(true);
  }, [location.pathname]);

  return (
    <div className="min-h-screen relative overflow-hidden bg-[#16191D]">
      {/* Luxury Background Elements */}
      <div className="absolute inset-0">
        {/* Animated Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 2px, transparent 2px),
                               radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px),
                               radial-gradient(circle at 50% 50%, #D4C2A4 1.5px, transparent 1.5px)`,
              backgroundSize: '150px 150px, 100px 100px, 200px 200px'
            }}
          ></div>
        </div>

        {/* Luxury Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#D4C2A4]/5 via-transparent to-[#D4C2A4]/10"></div>
        <div className="absolute inset-0 bg-gradient-to-tl from-[#16191D] via-transparent to-[#16191D]/90"></div>

        {/* Floating Luxury Elements */}
        <div className="absolute top-20 left-10 w-2 h-2 bg-[#D4C2A4]/30 rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-20 w-1 h-1 bg-[#D4C2A4]/40 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-1/4 w-1.5 h-1.5 bg-[#D4C2A4]/20 rounded-full animate-pulse delay-2000"></div>
        <div className="absolute bottom-20 right-1/3 w-1 h-1 bg-[#D4C2A4]/30 rounded-full animate-pulse delay-500"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className={`text-center max-w-4xl mx-auto transition-all duration-1000 ease-out ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>

          {/* Royal Crown Icon */}
          <div className="flex justify-center mb-8">
            <div className="relative">
              <div className="absolute inset-0 bg-[#D4C2A4]/20 rounded-full blur-xl scale-150 animate-pulse"></div>
              <div className="relative bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/30 rounded-full p-6 sm:p-8">
                <Crown className="h-16 w-16 sm:h-20 sm:w-20 text-[#D4C2A4] animate-pulse" />
              </div>
            </div>
          </div>

          {/* Luxury 404 Display */}
          <div className="mb-8 sm:mb-12">
            <h1 className="font-cormorant text-8xl sm:text-9xl md:text-[12rem] lg:text-[14rem] font-light text-[#D4C2A4] leading-none tracking-wider mb-4 relative">
              <span className="relative inline-block">
                4
                <div className="absolute inset-0 bg-gradient-to-r from-[#D4C2A4]/20 to-transparent blur-2xl"></div>
              </span>
              <span className="relative inline-block mx-2 sm:mx-4">
                0
                <div className="absolute inset-0 bg-gradient-to-r from-[#D4C2A4]/30 to-[#D4C2A4]/10 blur-3xl"></div>
              </span>
              <span className="relative inline-block">
                4
                <div className="absolute inset-0 bg-gradient-to-l from-[#D4C2A4]/20 to-transparent blur-2xl"></div>
              </span>
            </h1>

            {/* Decorative Stars */}
            <div className="flex justify-center items-center gap-4 mb-6">
              <Star className="h-4 w-4 text-[#D4C2A4]/60 animate-pulse" />
              <Sparkles className="h-6 w-6 text-[#D4C2A4] animate-pulse delay-300" />
              <Star className="h-4 w-4 text-[#D4C2A4]/60 animate-pulse delay-600" />
            </div>
          </div>

          {/* Luxury Message */}
          <div className="mb-12 sm:mb-16">
            <h2 className="font-cormorant text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-light text-[#F2EEE6] mb-6 leading-tight">
              <span className="italic">Oops!</span> This  Path
              <br className="hidden sm:block" />
              <span className="text-[#D4C2A4]">Doesn't Exist</span>
            </h2>
            <p className="font-open-sans text-lg sm:text-xl text-[#F2EEE6]/70 max-w-2xl mx-auto leading-relaxed">
              It seems you've wandered into uncharted territory. Even the most luxurious expeditions
              sometimes take unexpected turns. Let us guide you back to your royal adventure.
            </p>
          </div>

          {/* Luxury Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            {/* Primary CTA - Return Home */}
            <Link
              to="/"
              className="group relative inline-flex items-center gap-3 px-8 sm:px-10 py-4 sm:py-5 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/30 rounded-2xl text-[#F2EEE6] font-open-sans text-base sm:text-lg font-medium transition-all duration-500 hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4]/50 hover:scale-105 hover:shadow-2xl hover:shadow-[#D4C2A4]/20"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-[#D4C2A4]/5 to-[#D4C2A4]/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <Home className="h-5 w-5 text-[#D4C2A4] group-hover:scale-110 transition-transform duration-300" />
              <span className="relative z-10">Return to Warrior Safari</span>
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-[#D4C2A4]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
            </Link>

            {/* Secondary CTA - Go Back */}
            <button
              onClick={() => window.history.back()}
              className="group relative inline-flex items-center gap-3 px-8 sm:px-10 py-4 sm:py-5 bg-transparent border border-[#D4C2A4]/20 rounded-2xl text-[#D4C2A4] font-open-sans text-base sm:text-lg font-medium transition-all duration-500 hover:bg-[#D4C2A4]/5 hover:border-[#D4C2A4]/40 hover:scale-105"
            >
              <ArrowLeft className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
              <span>Go Back</span>
            </button>
          </div>

          {/* Luxury Decorative Element */}
          <div className="mt-16 sm:mt-20 flex justify-center">
            <div className="w-32 sm:w-48 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/30 to-transparent"></div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default NotFound;


import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import PageLoader from '@/components/ui/PageLoader';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, User, ArrowLeft, BookOpen, Share2, Heart, Tag } from 'lucide-react';
import { useBlogPost } from '@/hooks/useBlog';

const BlogPost = () => {
  const { slug } = useParams<{ slug: string }>();
  const { post, loading, error } = useBlogPost(slug || '');

  if (loading) {
    return (
      <PageLoader
        title="Unveiling Safari Chronicles..."
        subtitle="Preparing an extraordinary tale from the African wilderness"
      />
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen bg-[#16191D]">
        <Header />
        <main className="">
          <div className="relative overflow-hidden bg-[#16191D] pt-16 sm:pt-20 py-12 sm:py-16 md:py-20 lg:py-24">
            {/* Elegant Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                                 radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
                backgroundSize: '40px 40px'
              }} />
            </div>
            <div className="relative z-10 container mx-auto px-4 text-center">
              <BookOpen className="w-16 h-16 sm:w-20 sm:h-20 text-[#D4C2A4]/60 mx-auto mb-6 sm:mb-8" />
              <h1 className="font-['Cormorant_Garamond'] text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-semibold mb-4 sm:mb-6 tracking-wide text-[#F2EEE6]">
                Story Not Found
              </h1>
              <p className="font-['Open_Sans'] text-sm sm:text-base md:text-lg lg:text-xl text-[#F2EEE6]/80 max-w-xl sm:max-w-2xl mx-auto leading-relaxed px-2 mb-8 sm:mb-12">
                The safari story you're looking for doesn't exist or has been removed from our collection.
              </p>
              <Link to="/blog">
                <Button className="bg-[#D4C2A4] hover:bg-[#D4C2A4]/80 text-[#16191D] font-['Open_Sans'] px-6 sm:px-8 py-3 sm:py-4 rounded-xl transition-all duration-300 hover:shadow-lg text-sm sm:text-base inline-flex items-center gap-2 sm:gap-3">
                  <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5" />
                  Return to Stories
                </Button>
              </Link>
            </div>
          </div>
        </main>
        <Footer isDarkBackground={true} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#16191D] font-['Open_Sans'] text-[#F2EEE6] antialiased">
      <Header />
      <main className="">
        {/* Luxury Hero Section - Mobile Optimized */}
        <div className="relative overflow-hidden bg-[#16191D] pt-16 sm:pt-20">
          {/* Background Video */}
          <div className="absolute inset-0 w-full h-full">
            <video
              autoPlay
              muted
              loop
              playsInline
              className="absolute inset-0 w-full h-full object-cover"
            >
              <source src="https://videos.pexels.com/video-files/3571264/3571264-uhd_2560_1440_30fps.mp4" type="video/mp4" />
            </video>
            {/* Elegant Overlay */}
            <div className="absolute inset-0 bg-gradient-to-b from-[#16191D]/90 via-[#16191D]/70 to-[#16191D]/95"></div>
          </div>

          {/* Elegant Background Pattern */}
          <div className="absolute inset-0 opacity-5 z-[1]">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                               radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
              backgroundSize: '40px 40px'
            }} />
          </div>

          <div className="relative z-10 container mx-auto px-4 sm:px-6 py-8 sm:py-12 md:py-16">
            {/* Luxury Back Button - Mobile Optimized */}
            <div className="mb-6 sm:mb-8 md:mb-12">
              <Link to="/blog">
                <Button className="bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4] hover:text-[#16191D] font-['Open_Sans'] px-4 sm:px-6 py-2 sm:py-3 rounded-lg transition-all duration-300 text-sm sm:text-base inline-flex items-center gap-2 sm:gap-3">
                  <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span className="hidden xs:inline">Return to</span> Stories
                </Button>
              </Link>
            </div>

            {/* Luxury Featured Image - Mobile Optimized */}
            {post.featuredImage && (
              <div className="aspect-video sm:aspect-[21/9] mb-8 sm:mb-12 md:mb-16 rounded-xl sm:rounded-2xl overflow-hidden relative group">
                <img
                  src={post.featuredImage.includes('http')
                    ? post.featuredImage
                    : `https://images.unsplash.com/${post.featuredImage}?auto=format&fit=crop&w=1200&h=600`
                  }
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
                />
                {/* Elegant Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/60 via-transparent to-transparent"></div>

                {/* Category Badge - Mobile Responsive */}
                <div className="absolute top-4 sm:top-6 left-4 sm:left-6">
                  <span className="px-3 sm:px-4 py-1.5 sm:py-2 bg-[#D4C2A4]/90 backdrop-blur-sm text-[#16191D] font-['Open_Sans'] text-xs sm:text-sm font-medium rounded-full">
                    {post.category}
                  </span>
                </div>

                {/* Read Time - Mobile Responsive */}
                <div className="absolute top-4 sm:top-6 right-4 sm:right-6">
                  <div className="flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 bg-[#16191D]/70 backdrop-blur-sm rounded-full">
                    <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-[#D4C2A4]" />
                    <span className="text-[#F2EEE6] font-['Open_Sans'] text-xs sm:text-sm">
                      {Math.ceil(post.content.length / 200)} min read
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Luxury Article Header - Mobile First */}
            <header className="mb-8 sm:mb-12 md:mb-16 text-center">
              {/* Premium Badge - Mobile Responsive */}
              <div className="inline-flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6 md:mb-8 px-4 sm:px-6 py-2 sm:py-3 rounded-full bg-[#D4C2A4]/10 backdrop-blur-md border border-[#D4C2A4]/20">
                <BookOpen className="w-3 h-3 sm:w-4 sm:h-4 text-[#D4C2A4]" />
                <span className="text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm tracking-wider uppercase">
                  Safari Chronicle
                </span>
              </div>

              {/* Elegant Title - Mobile Optimized */}
              <h1 className="font-['Cormorant_Garamond'] text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-light leading-tight mb-6 sm:mb-8 md:mb-10 text-[#F2EEE6] tracking-wide px-2 sm:px-0">
                {post.title}
              </h1>

              {/* Sophisticated Meta Information - Mobile First */}
              <div className="flex flex-col xs:flex-row xs:items-center xs:justify-center gap-3 xs:gap-6 sm:gap-8 text-sm sm:text-base">
                <div className="flex items-center justify-center gap-2">
                  <User className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4]" />
                  <span className="font-['Open_Sans'] text-[#F2EEE6]/80">
                    {post.authorId || 'Safari Expert'}
                  </span>
                </div>
                <div className="hidden xs:block w-1 h-1 bg-[#D4C2A4] rounded-full"></div>
                <div className="flex items-center justify-center gap-2">
                  <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-[#D4C2A4]" />
                  <span className="font-['Open_Sans'] text-[#F2EEE6]/80">
                    {(() => {
                      const date = post.publishedAt || post.createdAt;
                      if (date && typeof date.toDate === 'function') {
                        return date.toDate().toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        });
                      }
                      if (date instanceof Date) {
                        return date.toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        });
                      }
                      return new Date().toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      });
                    })()}
                  </span>
                </div>
              </div>
            </header>
          </div>
        </div>

        {/* Luxury Content Section - Mobile Optimized */}
        <div className="relative bg-[#16191D] py-8 sm:py-12 md:py-16 lg:py-20">
          <div className="container mx-auto px-4 sm:px-6 max-w-4xl">
            <article className="bg-[#D4C2A4]/5 backdrop-blur-md border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl p-6 sm:p-8 md:p-12 lg:p-16">

              {/* Luxury Article Content - Mobile First */}
              <div className="prose prose-lg max-w-none">
                {post.content.split('\n').map((paragraph, index) => (
                  paragraph.trim() && (
                    <p key={index} className="font-['Open_Sans'] text-sm sm:text-base md:text-lg leading-relaxed text-[#F2EEE6]/90 mb-4 sm:mb-6 md:mb-8 first:text-lg sm:first:text-xl md:first:text-2xl first:leading-relaxed first:text-[#F2EEE6] first:font-light">
                      {paragraph}
                    </p>
                  )
                ))}
              </div>

              {/* Elegant Tags Section - Mobile Responsive */}
              {post.tags && post.tags.length > 0 && (
                <div className="mt-8 sm:mt-12 md:mt-16 pt-6 sm:pt-8 md:pt-12 border-t border-[#D4C2A4]/20">
                  <div className="flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
                    <Tag className="w-4 h-4 sm:w-5 sm:h-5 text-[#D4C2A4]" />
                    <h3 className="font-['Cormorant_Garamond'] text-lg sm:text-xl md:text-2xl font-medium text-[#F2EEE6]">
                      Story Tags
                    </h3>
                  </div>
                  <div className="flex flex-wrap gap-2 sm:gap-3">
                    {post.tags.map((tag) => (
                      <span key={tag} className="px-3 sm:px-4 py-1.5 sm:py-2 bg-[#D4C2A4]/10 border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm rounded-full hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4] transition-all duration-300">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Social Sharing Section - Mobile Optimized */}
              <div className="mt-8 sm:mt-12 md:mt-16 pt-6 sm:pt-8 md:pt-12 border-t border-[#D4C2A4]/20">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-6">
                  <div className="flex items-center gap-2 sm:gap-3">
                    <Share2 className="w-4 h-4 sm:w-5 sm:h-5 text-[#D4C2A4]" />
                    <span className="font-['Open_Sans'] text-sm sm:text-base text-[#F2EEE6]/80">
                      Share this story
                    </span>
                  </div>
                  <div className="flex items-center gap-3 sm:gap-4">
                    <button className="flex items-center gap-2 px-3 sm:px-4 py-2 sm:py-2.5 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm rounded-lg transition-all duration-300">
                      <Heart className="w-3 h-3 sm:w-4 sm:h-4" />
                      <span className="hidden xs:inline">Like</span>
                    </button>
                    <button className="flex items-center gap-2 px-3 sm:px-4 py-2 sm:py-2.5 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] font-['Open_Sans'] text-xs sm:text-sm rounded-lg transition-all duration-300">
                      <Share2 className="w-3 h-3 sm:w-4 sm:h-4" />
                      <span className="hidden xs:inline">Share</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Luxury Navigation - Mobile Optimized */}
              <div className="mt-8 sm:mt-12 md:mt-16 pt-6 sm:pt-8 md:pt-12 border-t border-[#D4C2A4]/20 text-center">
                <Link to="/blog">
                  <Button className="bg-[#D4C2A4] hover:bg-[#D4C2A4]/80 text-[#16191D] font-['Open_Sans'] px-6 sm:px-8 py-3 sm:py-4 rounded-xl transition-all duration-300 hover:shadow-lg text-sm sm:text-base inline-flex items-center gap-2 sm:gap-3 transform hover:-translate-y-1">
                    <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5" />
                    Return to All Stories
                  </Button>
                </Link>
              </div>
            </article>
          </div>
        </div>

        {/* Luxury Footer Transition - Mobile Optimized */}
        <div className="relative bg-[#16191D] py-8 sm:py-12 lg:py-16">
          <div className="container mx-auto px-4 sm:px-6 text-center">
            {/* Elegant Divider - Mobile Responsive */}
            <div className="flex items-center justify-center mb-6 sm:mb-8 lg:mb-12">
              <div className="w-16 sm:w-24 lg:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
              <div className="mx-2 sm:mx-4 w-2 h-2 sm:w-3 sm:h-3 bg-[#D4C2A4] rounded-full"></div>
              <div className="w-16 sm:w-24 lg:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
            </div>

            {/* Luxury CTA - Mobile First */}
            <h2 className="font-['Cormorant_Garamond'] text-2xl xs:text-3xl sm:text-3xl md:text-4xl lg:text-5xl font-light text-[#F2EEE6] mb-4 sm:mb-6">
              Discover More
              <span className="block text-[#D4C2A4] italic">Safari Stories</span>
            </h2>

            <p className="font-['Open_Sans'] text-sm xs:text-base sm:text-lg text-[#F2EEE6]/80 mb-6 sm:mb-8 lg:mb-10 max-w-xs xs:max-w-sm sm:max-w-2xl mx-auto px-2 sm:px-0">
              Explore our collection of extraordinary tales from the African wilderness.
              <span className="hidden sm:inline"> Each story captures the magic and wonder of safari adventures.</span>
            </p>

            <div className="flex flex-col xs:flex-row gap-3 sm:gap-4 lg:gap-6 justify-center">
              <Link to="/blog">
                <button className="w-full xs:w-auto px-6 sm:px-8 lg:px-10 py-3 sm:py-4 bg-[#D4C2A4] text-[#16191D] font-['Open_Sans'] font-semibold text-xs sm:text-sm tracking-wide uppercase rounded-sm transition-all duration-500 hover:bg-[#F2EEE6] hover:shadow-2xl hover:shadow-[#D4C2A4]/20 transform hover:-translate-y-1">
                  Explore Stories
                </button>
              </Link>
              <Link to="/destinations">
                <button className="w-full xs:w-auto px-6 sm:px-8 lg:px-10 py-3 sm:py-4 bg-transparent border border-[#D4C2A4]/30 text-[#D4C2A4] font-['Open_Sans'] font-medium text-xs sm:text-sm tracking-wide uppercase rounded-sm backdrop-blur-sm transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transform hover:-translate-y-1">
                  View Destinations
                </button>
              </Link>
            </div>
          </div>
        </div>
      </main>
      <Footer isDarkBackground={true} />
    </div>
  );
};

export default BlogPost;

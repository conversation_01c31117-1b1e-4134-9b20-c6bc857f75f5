
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Leaf, Droplets, Zap, TreePine, Users, Award, Globe, Heart, TrendingUp, Target, Sparkles } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const SustainabilityDashboard = () => {
  const { t } = useLanguage();

  const sustainabilityMetrics = [
    {
      icon: <Leaf className="h-6 w-6 text-emerald-600" />,
      title: 'Carbon Footprint',
      value: '2.3 tons CO₂',
      offset: '100%',
      progress: 100,
      description: 'Fully offset through tree planting initiatives in Kilimanjaro region',
      color: 'emerald',
      trend: '+15%'
    },
    {
      icon: <Droplets className="h-6 w-6 text-blue-600" />,
      title: 'Water Conservation',
      value: '1,200L saved',
      offset: '85%',
      progress: 85,
      description: 'Through efficient lodge practices and rainwater harvesting systems',
      color: 'blue',
      trend: '+8%'
    },
    {
      icon: <Zap className="h-6 w-6 text-amber-600" />,
      title: 'Renewable Energy',
      value: '78% solar',
      offset: '78%',
      progress: 78,
      description: 'Solar-powered accommodations and eco-friendly camp operations',
      color: 'amber',
      trend: '+12%'
    },
    {
      icon: <TreePine className="h-6 w-6 text-green-700" />,
      title: 'Local Community',
      value: '$450 contributed',
      offset: '92%',
      progress: 92,
      description: 'Direct economic impact supporting Maasai communities',
      color: 'green',
      trend: '+22%'
    },
    {
      icon: <Users className="h-6 w-6 text-purple-600" />,
      title: 'Community Employment',
      value: '85 local jobs',
      offset: '95%',
      progress: 95,
      description: 'Local guides, drivers, and support staff employed',
      color: 'purple',
      trend: '+5%'
    },
    {
      icon: <Heart className="h-6 w-6 text-rose-500" />,
      title: 'Wildlife Protection',
      value: '12 species',
      offset: '88%',
      progress: 88,
      description: 'Endangered species conservation programs supported',
      color: 'rose',
      trend: '+18%'
    }
  ];

  const certifications = [
    {
      icon: <Award className="h-5 w-5" />,
      title: 'TATO Certified',
      description: 'Tanzania Association of Tour Operators member',
      color: 'amber'
    },
    {
      icon: <Globe className="h-5 w-5" />,
      title: 'Sustainable Tourism',
      description: 'UN Global Compact participant',
      color: 'blue'
    },
    {
      icon: <Leaf className="h-5 w-5" />,
      title: 'Carbon Neutral',
      description: 'Verified carbon offset program',
      color: 'emerald'
    }
  ];

  const impactStats = [
    { label: 'Trees Planted', value: '2,500+', icon: <TreePine className="h-4 w-4" /> },
    { label: 'Species Protected', value: '12', icon: <Heart className="h-4 w-4" /> },
    { label: 'Jobs Created', value: '85', icon: <Users className="h-4 w-4" /> },
    { label: 'CO₂ Offset', value: '100%', icon: <Leaf className="h-4 w-4" /> }
  ];

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-emerald-50/50 via-blue-50/30 to-green-50/50 p-4 sm:p-6 lg:p-8">
      {/* Glass Morphism Header */}
      <div className="relative mb-8 lg:mb-12">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-blue-500/10 to-green-500/10 rounded-3xl blur-xl"></div>
        <Card className="relative backdrop-blur-xl bg-white/70 border-white/20 shadow-2xl rounded-3xl overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-white/20 to-transparent"></div>
          <CardHeader className="relative pb-8 pt-8 px-6 sm:px-8 lg:px-12">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              <div className="space-y-4">
                <CardTitle className="flex items-center text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-emerald-600 via-blue-600 to-green-600 bg-clip-text text-transparent">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-emerald-500/20 to-green-500/20 backdrop-blur-sm mr-4">
                    <Leaf className="h-8 w-8 sm:h-10 sm:w-10 text-emerald-600" />
                  </div>
                  Eco Impact & Sustainability Dashboard
                </CardTitle>
                <p className="text-base sm:text-lg text-gray-700 max-w-3xl leading-relaxed">
                  Our commitment to responsible tourism and environmental conservation in Tanzania.
                  Every journey creates positive impact for wildlife, communities, and our planet.
                </p>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 min-w-fit">
                {impactStats.map((stat, index) => (
                  <div key={index} className="text-center p-4 rounded-2xl bg-white/50 backdrop-blur-sm border border-white/30">
                    <div className="flex justify-center mb-2 text-emerald-600">
                      {stat.icon}
                    </div>
                    <div className="text-xl sm:text-2xl font-bold text-gray-800">{stat.value}</div>
                    <div className="text-xs sm:text-sm text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      {/* Main Content */}
      <div className="space-y-8 lg:space-y-12">
        {/* Main Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8">
          {sustainabilityMetrics.map((metric, index) => (
            <div key={index} className="group relative">
              {/* Glass Morphism Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/60 via-white/40 to-white/20 rounded-2xl blur-sm group-hover:blur-none transition-all duration-500"></div>
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-blue-500/5 to-green-500/5 rounded-2xl"></div>

              <Card className="relative backdrop-blur-xl bg-white/70 border-white/30 shadow-xl rounded-2xl overflow-hidden group-hover:shadow-2xl group-hover:scale-[1.02] transition-all duration-500">
                <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-transparent to-transparent"></div>

                <CardContent className="relative p-6 lg:p-8 space-y-6">
                  {/* Header with Icon and Title */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-xl bg-gradient-to-br from-${metric.color}-500/20 to-${metric.color}-600/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300`}>
                        {metric.icon}
                      </div>
                      <div className="space-y-1">
                        <h3 className="font-bold text-lg text-gray-800">{metric.title}</h3>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant="secondary"
                            className={`font-bold text-sm bg-${metric.color}-100 text-${metric.color}-800 border-${metric.color}-200 hover:bg-${metric.color}-200`}
                          >
                            {metric.value}
                          </Badge>
                          <div className="flex items-center text-emerald-600 text-sm font-medium">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            {metric.trend}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Progress Section */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 font-medium">Progress</span>
                      <span className="font-bold text-gray-800">{metric.progress}%</span>
                    </div>
                    <div className="relative">
                      <Progress
                        value={metric.progress}
                        className={`h-4 bg-${metric.color}-100/50 rounded-full overflow-hidden backdrop-blur-sm`}
                      />
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full"></div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-sm text-gray-700 leading-relaxed bg-white/30 p-4 rounded-xl backdrop-blur-sm">
                    {metric.description}
                  </p>

                  {/* Achievement Badge */}
                  <div className="flex items-center justify-center">
                    <div className={`inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-${metric.color}-500/10 to-${metric.color}-600/10 border border-${metric.color}-200/50 backdrop-blur-sm`}>
                      <Target className="h-4 w-4 mr-2 text-emerald-600" />
                      <span className="text-sm font-medium text-gray-700">
                        {metric.progress >= 90 ? 'Excellent Impact' : metric.progress >= 70 ? 'Good Progress' : 'Growing Impact'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>

        {/* Certifications Section */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/5 via-blue-500/5 to-emerald-500/5 rounded-3xl blur-xl"></div>
          <Card className="relative backdrop-blur-xl bg-white/70 border-white/20 shadow-xl rounded-3xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-white/20 to-transparent"></div>

            <CardHeader className="relative pb-6 pt-8 px-6 sm:px-8 lg:px-12">
              <CardTitle className="flex items-center text-2xl sm:text-3xl font-bold bg-gradient-to-r from-amber-600 via-blue-600 to-emerald-600 bg-clip-text text-transparent">
                <div className="p-3 rounded-2xl bg-gradient-to-br from-amber-500/20 to-emerald-500/20 backdrop-blur-sm mr-4">
                  <Award className="h-6 w-6 sm:h-8 sm:w-8 text-amber-600" />
                </div>
                Certifications & Partnerships
              </CardTitle>
              <p className="text-gray-700 mt-2 text-base sm:text-lg">
                Recognized excellence in sustainable tourism and environmental stewardship
              </p>
            </CardHeader>

            <CardContent className="relative px-6 sm:px-8 lg:px-12 pb-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
                {certifications.map((cert, index) => (
                  <div key={index} className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/60 via-white/40 to-white/20 rounded-2xl blur-sm group-hover:blur-none transition-all duration-300"></div>

                    <div className={`relative p-6 lg:p-8 rounded-2xl backdrop-blur-sm bg-gradient-to-br from-${cert.color}-50/80 to-white/60 border border-${cert.color}-200/30 group-hover:shadow-xl group-hover:scale-105 transition-all duration-300`}>
                      <div className="flex items-start space-x-4">
                        <div className={`p-3 rounded-xl bg-gradient-to-br from-${cert.color}-500/20 to-${cert.color}-600/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300`}>
                          <div className={`text-${cert.color}-600`}>{cert.icon}</div>
                        </div>
                        <div className="flex-1 space-y-2">
                          <h4 className={`font-bold text-lg text-${cert.color}-800`}>{cert.title}</h4>
                          <p className={`text-sm text-${cert.color}-700 leading-relaxed`}>{cert.description}</p>

                          {/* Verification Badge */}
                          <div className="flex items-center mt-3">
                            <div className={`inline-flex items-center px-3 py-1 rounded-full bg-${cert.color}-100/80 border border-${cert.color}-200/50 backdrop-blur-sm`}>
                              <Sparkles className="h-3 w-3 mr-1 text-emerald-600" />
                              <span className="text-xs font-medium text-gray-700">Verified</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Impact Summary */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
          {/* Environmental Impact */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 via-green-500/10 to-teal-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>

            <Card className="relative backdrop-blur-xl bg-white/70 border-white/20 shadow-xl rounded-3xl overflow-hidden group-hover:shadow-2xl group-hover:scale-[1.02] transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-green-500/5 to-transparent"></div>

              <CardContent className="relative p-8 lg:p-10 space-y-6">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-emerald-500/20 to-green-500/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300">
                    <Leaf className="h-8 w-8 text-emerald-600" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent">
                      Environmental Impact
                    </h3>
                    <p className="text-emerald-700 font-medium">Protecting Tanzania's Ecosystems</p>
                  </div>
                </div>

                <p className="text-base text-gray-700 leading-relaxed bg-white/40 p-4 rounded-xl backdrop-blur-sm">
                  Every safari contributes to conservation efforts and sustainable practices that protect Tanzania's unique ecosystems for future generations.
                </p>

                <div className="space-y-4">
                  <h4 className="font-bold text-lg text-emerald-800">Key Initiatives:</h4>
                  <div className="grid gap-3">
                    {[
                      { icon: <TreePine className="h-4 w-4" />, text: 'Tree planting in deforested areas', stat: '2,500+ trees' },
                      { icon: <Heart className="h-4 w-4" />, text: 'Wildlife corridor protection', stat: '50km protected' },
                      { icon: <Leaf className="h-4 w-4" />, text: 'Plastic-free safari operations', stat: '100% plastic-free' }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-xl bg-emerald-50/80 backdrop-blur-sm border border-emerald-200/30">
                        <div className="flex items-center space-x-3">
                          <div className="text-emerald-600">{item.icon}</div>
                          <span className="text-sm font-medium text-emerald-800">{item.text}</span>
                        </div>
                        <Badge className="bg-emerald-100 text-emerald-800 border-emerald-200 text-xs font-bold">
                          {item.stat}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Community Impact */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-indigo-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>

            <Card className="relative backdrop-blur-xl bg-white/70 border-white/20 shadow-xl rounded-3xl overflow-hidden group-hover:shadow-2xl group-hover:scale-[1.02] transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-transparent"></div>

              <CardContent className="relative p-8 lg:p-10 space-y-6">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300">
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      Community Impact
                    </h3>
                    <p className="text-blue-700 font-medium">Empowering Local Communities</p>
                  </div>
                </div>

                <p className="text-base text-gray-700 leading-relaxed bg-white/40 p-4 rounded-xl backdrop-blur-sm">
                  Supporting local communities through employment, education, and cultural preservation programs that create lasting positive change.
                </p>

                <div className="space-y-4">
                  <h4 className="font-bold text-lg text-blue-800">Key Programs:</h4>
                  <div className="grid gap-3">
                    {[
                      { icon: <Users className="h-4 w-4" />, text: 'Local guide training programs', stat: '85 guides' },
                      { icon: <Award className="h-4 w-4" />, text: 'School construction projects', stat: '3 schools built' },
                      { icon: <Heart className="h-4 w-4" />, text: 'Traditional craft support', stat: '45 artisans' }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-xl bg-blue-50/80 backdrop-blur-sm border border-blue-200/30">
                        <div className="flex items-center space-x-3">
                          <div className="text-blue-600">{item.icon}</div>
                          <span className="text-sm font-medium text-blue-800">{item.text}</span>
                        </div>
                        <Badge className="bg-blue-100 text-blue-800 border-blue-200 text-xs font-bold">
                          {item.stat}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-blue-500/10 to-purple-500/10 rounded-3xl blur-2xl"></div>

          <Card className="relative backdrop-blur-xl bg-white/70 border-white/20 shadow-2xl rounded-3xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-blue-500/5 to-purple-500/5"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-white/20 to-transparent"></div>

            <CardContent className="relative p-8 lg:p-12 text-center space-y-8">
              <div className="space-y-4">
                <div className="flex justify-center mb-6">
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-emerald-500/20 via-blue-500/20 to-purple-500/20 backdrop-blur-sm">
                    <Sparkles className="h-12 w-12 text-emerald-600" />
                  </div>
                </div>

                <h3 className="text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Join Our Conservation Mission
                </h3>

                <p className="text-lg sm:text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
                  When you book with us, you're not just experiencing Tanzania's wildlife - you're becoming part of a movement
                  that preserves these incredible ecosystems for future generations.
                </p>
              </div>

              {/* Impact Highlights */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8 max-w-4xl mx-auto">
                {[
                  { icon: <TreePine className="h-6 w-6" />, value: '2,500+', label: 'Trees Planted', color: 'emerald' },
                  { icon: <Heart className="h-6 w-6" />, value: '12', label: 'Species Protected', color: 'rose' },
                  { icon: <Users className="h-6 w-6" />, value: '85', label: 'Jobs Created', color: 'blue' },
                  { icon: <Globe className="h-6 w-6" />, value: '100%', label: 'Carbon Neutral', color: 'purple' }
                ].map((item, index) => (
                  <div key={index} className="group">
                    <div className={`p-6 rounded-2xl bg-gradient-to-br from-${item.color}-50/80 to-white/60 backdrop-blur-sm border border-${item.color}-200/30 group-hover:shadow-xl group-hover:scale-105 transition-all duration-300`}>
                      <div className={`flex justify-center mb-3 text-${item.color}-600 group-hover:scale-110 transition-transform duration-300`}>
                        {item.icon}
                      </div>
                      <div className={`text-2xl sm:text-3xl font-bold text-${item.color}-800 mb-1`}>
                        {item.value}
                      </div>
                      <div className="text-sm text-gray-600 font-medium">
                        {item.label}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Action Message */}
              <div className="bg-white/50 backdrop-blur-sm rounded-2xl p-6 lg:p-8 border border-white/30">
                <p className="text-base sm:text-lg text-gray-700 font-medium mb-4">
                  Every safari booking contributes directly to conservation efforts and community development
                </p>
                <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600">
                  <span className="flex items-center bg-emerald-100/80 px-3 py-2 rounded-full backdrop-blur-sm">
                    <Leaf className="h-4 w-4 mr-2 text-emerald-600" />
                    Carbon Offset Included
                  </span>
                  <span className="flex items-center bg-blue-100/80 px-3 py-2 rounded-full backdrop-blur-sm">
                    <Users className="h-4 w-4 mr-2 text-blue-600" />
                    Local Community Support
                  </span>
                  <span className="flex items-center bg-purple-100/80 px-3 py-2 rounded-full backdrop-blur-sm">
                    <Heart className="h-4 w-4 mr-2 text-purple-600" />
                    Wildlife Conservation
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SustainabilityDashboard;

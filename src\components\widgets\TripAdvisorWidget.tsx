import React from 'react';

interface TripAdvisorWidgetProps {
  profileUrl?: string;
  rating?: 'Excellent' | 'Very Good' | 'Average' | 'Poor' | 'Terrible';
  certificateYear?: number;
  reviewCount?: number;
  className?: string;
}

const TripAdvisorWidget: React.FC<TripAdvisorWidgetProps> = ({
  profileUrl = 'https://www.tripadvisor.com/YOUR_TRIPADVISOR_PROFILE',
  rating = 'Excellent',
  certificateYear = 2024,
  reviewCount = 120,
  className = ''
}) => {
  const handleViewProfile = () => {
    window.open(profileUrl, '_blank');
  };

  const handleWriteReview = () => {
    window.open(`${profileUrl}#REVIEWS`, '_blank');
  };

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'Excellent': return 'text-green-400';
      case 'Very Good': return 'text-green-300';
      case 'Average': return 'text-yellow-400';
      case 'Poor': return 'text-orange-400';
      case 'Terrible': return 'text-red-400';
      default: return 'text-green-400';
    }
  };

  const getRatingDots = (rating: string) => {
    switch (rating) {
      case 'Excellent': return 5;
      case 'Very Good': return 4;
      case 'Average': return 3;
      case 'Poor': return 2;
      case 'Terrible': return 1;
      default: return 5;
    }
  };

  return (
    <div className={`bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 p-4 hover:bg-white/15 transition-all duration-300 shadow-lg hover:shadow-xl ${className}`}>
      <div className="flex items-center justify-between">
        {/* TripAdvisor Logo and Rating */}
        <div className="flex items-center space-x-3">
          {/* Real TripAdvisor Logo */}
          <div className="w-7 h-7 bg-white rounded-lg flex items-center justify-center shadow-sm">
          <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px"><path d="M 25 11 C 19.167969 11 13.84375 12.511719 9.789063 15 L 2 15 C 2 15 3.753906 17.152344 4.578125 19.578125 C 2.96875 21.621094 2 24.195313 2 27 C 2 33.628906 7.371094 39 14 39 C 17.496094 39 20.636719 37.492188 22.828125 35.105469 L 25 38 L 27.171875 35.105469 C 29.363281 37.492188 32.503906 39 36 39 C 42.628906 39 48 33.628906 48 27 C 48 24.195313 47.03125 21.621094 45.421875 19.578125 C 46.246094 17.152344 48 15 48 15 L 40.203125 15 C 36.148438 12.511719 30.828125 11 25 11 Z M 25 13 C 28.882813 13 32.585938 13.707031 35.800781 15.011719 C 30.964844 15.089844 26.824219 18.027344 25 22.214844 C 23.171875 18.019531 19.023438 15.078125 14.171875 15.011719 L 14.242188 14.988281 C 17.453125 13.699219 21.144531 13 25 13 Z M 14 17 C 19.523438 17 24 21.476563 24 27 C 24 32.523438 19.523438 37 14 37 C 8.476563 37 4 32.523438 4 27 C 4 21.476563 8.476563 17 14 17 Z M 36 17 C 41.523438 17 46 21.476563 46 27 C 46 32.523438 41.523438 37 36 37 C 30.476563 37 26 32.523438 26 27 C 26 21.476563 30.476563 17 36 17 Z M 14 21 C 10.6875 21 8 23.6875 8 27 C 8 30.3125 10.6875 33 14 33 C 17.3125 33 20 30.3125 20 27 C 20 23.6875 17.3125 21 14 21 Z M 36 21 C 32.6875 21 30 23.6875 30 27 C 30 30.3125 32.6875 33 36 33 C 39.3125 33 42 30.3125 42 27 C 42 23.6875 39.3125 21 36 21 Z M 14 23 C 16.210938 23 18 24.789063 18 27 C 18 29.210938 16.210938 31 14 31 C 11.789063 31 10 29.210938 10 27 C 10 24.789063 11.789063 23 14 23 Z M 36 23 C 38.210938 23 40 24.789063 40 27 C 40 29.210938 38.210938 31 36 31 C 33.789063 31 32 29.210938 32 27 C 32 24.789063 33.789063 23 36 23 Z M 14 25 C 12.894531 25 12 25.894531 12 27 C 12 28.105469 12.894531 29 14 29 C 15.105469 29 16 28.105469 16 27 C 16 25.894531 15.105469 25 14 25 Z M 36 25 C 34.894531 25 34 25.894531 34 27 C 34 28.105469 34.894531 29 36 29 C 37.105469 29 38 28.105469 38 27 C 38 25.894531 37.105469 25 36 25 Z"/></svg>
          </div>
          <div>
            <div className="flex items-center space-x-1 mb-1">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className={`w-3.5 h-3.5 ${i < getRatingDots(rating) ? getRatingColor(rating) : 'text-white/30'} drop-shadow-sm`}
                >
                  ●
                </div>
              ))}
            </div>
            <p className="text-black text-xs font-medium">{rating} • {reviewCount}+ reviews</p>
          </div>
        </div>

        {/* Action Button */}
        <button
          onClick={handleViewProfile}
          className="text-black hover:text-black text-xs font-medium hover:underline transition-colors px-2 py-1 rounded hover:bg-white/10"
        >
          View Profile
        </button>
      </div>
    </div>
  );
};

export default TripAdvisorWidget;

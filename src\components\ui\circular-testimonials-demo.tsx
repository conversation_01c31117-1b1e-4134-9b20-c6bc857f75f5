import React from "react";
import { CircularTestimonials } from '@/components/ui/circular-testimonials';

const testimonials = [
  {
    quote:
      "With over 20 years of experience in Tanzanian wildlife conservation and tourism, <PERSON> founded Warrior of Africa Safari to create authentic and sustainable safari experiences.",
    name: "<PERSON>",
    designation: "Founder & Safari Director",
    src:
      "https://images.unsplash.com/photo-1580489944761-15a19d654956?auto=format&fit=crop&w=400&h=400",
  },
  {
    quote:
      "Born and raised near Serengeti, <PERSON> brings unparalleled knowledge of wildlife behavior and tracking skills, with certification in wildlife biology.",
    name: "<PERSON>",
    designation: "Head Safari Guide",
    src:
      "https://images.unsplash.com/photo-1506277886164-e25aa3f4ef7f?auto=format&fit=crop&w=400&h=400",
  },
  {
    quote:
      "A passionate conservationist with a PhD in Wildlife Ecology, <PERSON> ensures our safaris contribute positively to wildlife conservation and community development.",
    name: "<PERSON>",
    designation: "Conservation Director",
    src:
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=400&h=400",
  },
];

export const CircularTestimonialsDemo = () => (
  <section className="py-20">
    {/* Light testimonials section */}
    <div className="bg-[#f7f7fa] p-8 md:p-20 rounded-lg min-h-[300px] flex flex-wrap gap-6 items-center justify-center relative">
      <div
        className="items-center justify-center relative flex w-full"
        style={{ maxWidth: "1456px" }}
      >
        <CircularTestimonials
          testimonials={testimonials}
          autoplay={true}
          colors={{
            name: "#0a0a0a",
            designation: "#ea580c",
            testimony: "#374151",
            arrowBackground: "#ea580c",
            arrowForeground: "#ffffff",
            arrowHoverBackground: "#dc2626",
          }}
          fontSizes={{
            name: "1.75rem",
            designation: "1.125rem",
            quote: "1rem",
          }}
        />
      </div>
    </div>

    {/* Dark testimonials section */}
    <div className="bg-[#060507] p-8 md:p-16 rounded-lg min-h-[300px] flex flex-wrap gap-6 items-center justify-center relative mt-8">
      <div
        className="items-center justify-center relative flex w-full"
        style={{ maxWidth: "1024px" }}
      >
        <CircularTestimonials
          testimonials={testimonials}
          autoplay={true}
          colors={{
            name: "#f7f7ff",
            designation: "#e1e1e1",
            testimony: "#f1f1f7",
            arrowBackground: "#0582CA",
            arrowForeground: "#141414",
            arrowHoverBackground: "#f7f7ff",
          }}
          fontSizes={{
            name: "1.75rem",
            designation: "1.125rem",
            quote: "1rem",
          }}
        />
      </div>
    </div>
  </section>
);

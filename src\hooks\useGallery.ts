
import { useState, useEffect } from 'react';
import { FirebaseService } from '@/services/firebase';
import { GalleryImage } from '@/types/firebase';

export const useGallery = () => {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadGalleryImages = async () => {
      try {
        setLoading(true);
        const data = await FirebaseService.getGalleryImages();

        const galleryImages = data.map((image: any) => ({
          id: image.id,
          url: image.imageUrl || image.url || '', // Map imageUrl to url for frontend compatibility
          imageUrl: image.imageUrl || '',
          title: image.title || 'Untitled Image',
          description: image.description || '',
          category: image.category || 'Safari',
          location: image.location || '',
          photographer: image.photographer || 'Unknown',
          dateTaken: image.dateTaken || '',
          tags: Array.isArray(image.tags) ? image.tags : [],
          featured: image.featured || false,
          createdAt: image.createdAt,
          updatedAt: image.updatedAt
        })) as GalleryImage[];

        setImages(galleryImages);
      } catch (err) {
        setError('Failed to load gallery images');
        console.error('Error loading gallery images:', err);
      } finally {
        setLoading(false);
      }
    };

    loadGalleryImages();
  }, []);

  return { images, loading, error };
};

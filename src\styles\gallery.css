/* Gallery Masonry Layout Styles */
.masonry-container {
  column-count: 1;
  column-gap: 1rem;
  column-fill: balance;
}

@media (min-width: 640px) {
  .masonry-container {
    column-count: 2;
  }
}

@media (min-width: 768px) {
  .masonry-container {
    column-count: 3;
  }
}

@media (min-width: 1024px) {
  .masonry-container {
    column-count: 4;
  }
}

@media (min-width: 1280px) {
  .masonry-container {
    column-count: 5;
  }
}

.masonry-item {
  break-inside: avoid;
  margin-bottom: 1rem;
  display: inline-block;
  width: 100%;
}

/* Smooth image loading */
.gallery-image {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

.gallery-image:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading skeleton */
.image-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Modal animations */
.modal-backdrop {
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Luxury scrollbar for modal */
.modal-sidebar::-webkit-scrollbar {
  width: 6px;
}

.modal-sidebar::-webkit-scrollbar-track {
  background: rgba(212, 194, 164, 0.1);
  border-radius: 8px;
}

.modal-sidebar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #D4C2A4, #D4C2A4);
  border-radius: 8px;
  border: 1px solid rgba(212, 194, 164, 0.2);
}

.modal-sidebar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #D4C2A4, rgba(212, 194, 164, 0.8));
}

/* Responsive improvements */
@media (max-width: 768px) {
  .modal-content {
    flex-direction: column;
    max-height: 90vh;
  }

  .modal-sidebar {
    max-height: 40vh;
    width: 100%;
  }
}

/* Scrolling Gallery Styles */
.scrolling-image {
  will-change: transform;
}

.scrolling-image img {
  transition: filter 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

.scrolling-image:hover img {
  filter: saturate(1);
  transform: scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(212, 194, 164, 0.3), 0 10px 10px -5px rgba(212, 194, 164, 0.1);
}

/* Enhanced scrolling gallery luxury effects */
.scrolling-image .group:hover {
  box-shadow:
    0 25px 50px -12px rgba(212, 194, 164, 0.25),
    0 0 0 1px rgba(212, 194, 164, 0.1),
    inset 0 1px 0 rgba(212, 194, 164, 0.05);
}

/* Luxury glow animation for scrolling gallery */
@keyframes luxuryGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(212, 194, 164, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(212, 194, 164, 0.2);
  }
}

.scrolling-image:hover .group {
  animation: luxuryGlow 2s ease-in-out infinite;
}

/* Responsive scrolling gallery */
@media (max-width: 768px) {
  .scrolling-image {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .scrolling-image {
    padding: 0.375rem;
  }
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Luxury scrollbar for the page */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(22, 25, 29, 0.8);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #D4C2A4, rgba(212, 194, 164, 0.8));
  border-radius: 8px;
  border: 1px solid rgba(212, 194, 164, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #D4C2A4, #D4C2A4);
}

/* View mode toggle styles */
.view-mode-toggle {
  backdrop-filter: blur(10px);
  background: rgba(212, 194, 164, 0.1);
  border: 1px solid rgba(212, 194, 164, 0.2);
}

/* Ensure proper z-index for scrolling gallery */
.scrolling-gallery-container {
  position: relative;
  z-index: 1;
}

/* Luxury gallery enhancements */
.luxury-gallery-card {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(212, 194, 164, 0.1), rgba(212, 194, 164, 0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(212, 194, 164, 0.2);
  transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

.luxury-gallery-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px -12px rgba(212, 194, 164, 0.25),
    0 0 0 1px rgba(212, 194, 164, 0.2),
    inset 0 1px 0 rgba(212, 194, 164, 0.1);
}

.luxury-gallery-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(212, 194, 164, 0.4), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.luxury-gallery-card:hover::before {
  opacity: 1;
}

/* Elegant loading animation */
@keyframes luxuryShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.luxury-loading {
  background: linear-gradient(
    90deg,
    rgba(212, 194, 164, 0.1) 25%,
    rgba(212, 194, 164, 0.2) 50%,
    rgba(212, 194, 164, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: luxuryShimmer 2s infinite;
}

/* Responsive masonry improvements */
@media (max-width: 640px) {
  .masonry-container {
    column-gap: 0.75rem;
  }

  .masonry-item {
    margin-bottom: 0.75rem;
  }
}

@media (max-width: 480px) {
  .masonry-container {
    column-gap: 0.5rem;
  }

  .masonry-item {
    margin-bottom: 0.5rem;
  }
}

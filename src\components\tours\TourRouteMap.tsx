import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { MapPin, Route, Navigation, Clock, Compass, Mountain } from 'lucide-react';
import LeafletMap from '@/components/maps/LeafletMap';
import { Tour } from '@/types/firebase';

interface TourRouteMapProps {
  tour: Tour;
}

const TourRouteMap: React.FC<TourRouteMapProps> = ({ tour }) => {
  const [mapHeight, setMapHeight] = useState('500px');

  useEffect(() => {
    const updateMapHeight = () => {
      if (typeof window !== 'undefined') {
        if (window.innerWidth < 640) {
          setMapHeight('300px'); // Mobile
        } else if (window.innerWidth < 1024) {
          setMapHeight('400px'); // Tablet
        } else {
          setMapHeight('500px'); // Desktop
        }
      }
    };

    updateMapHeight();
    window.addEventListener('resize', updateMapHeight);
    return () => window.removeEventListener('resize', updateMapHeight);
  }, []);

  // Create route points from tour data
  const createRoutePoints = () => {
    const points = [];
    
    // Add start point if available
    if (tour.routeMap?.startPoint) {
      points.push({
        ...tour.routeMap.startPoint,
        type: 'start' as const,
        description: 'Tour starting point'
      });
    }
    
    // Add waypoints if available
    if (tour.routeMap?.waypoints) {
      tour.routeMap.waypoints
        .sort((a, b) => a.order - b.order)
        .forEach(waypoint => {
          points.push({
            ...waypoint,
            type: 'waypoint' as const
          });
        });
    }
    
    // Add end point if available
    if (tour.routeMap?.endPoint) {
      points.push({
        ...tour.routeMap.endPoint,
        type: 'end' as const,
        description: 'Tour ending point'
      });
    }
    
    // If no route data is available, create default points from destinations
    if (points.length === 0 && tour.destinations) {
      const defaultDestinations = [
        { name: 'Serengeti National Park', lat: -2.153389, lng: 34.6857 },
        { name: 'Ngorongoro Crater', lat: -3.2175, lng: 35.5 },
        { name: 'Tarangire National Park', lat: -3.9, lng: 35.85 },
        { name: 'Lake Manyara', lat: -3.48, lng: 35.83 }
      ];
      
      tour.destinations.forEach((destName, index) => {
        const defaultDest = defaultDestinations.find(d => 
          d.name.toLowerCase().includes(destName.toLowerCase()) ||
          destName.toLowerCase().includes(d.name.toLowerCase())
        ) || defaultDestinations[index % defaultDestinations.length];
        
        points.push({
          lat: defaultDest.lat,
          lng: defaultDest.lng,
          name: destName,
          type: index === 0 ? 'start' : index === tour.destinations.length - 1 ? 'end' : 'waypoint',
          description: `Visit ${destName}`
        });
      });
    }
    
    return points;
  };

  const routePoints = createRoutePoints();
  
  // Convert route points to destinations format for the map
  const mapDestinations = routePoints.map((point, index) => ({
    id: `point-${index}`,
    name: point.name,
    lat: point.lat,
    lng: point.lng,
    description: point.description || `${point.type} point`,
    image: 'https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=800&h=600' // Default safari image
  }));

  const getPointTypeIcon = (type: string) => {
    switch (type) {
      case 'start':
        return <Navigation className="h-5 w-5 text-green-400" />;
      case 'end':
        return <Mountain className="h-5 w-5 text-red-400" />;
      case 'waypoint':
        return <MapPin className="h-5 w-5 text-[#D4C2A4]" />;
      default:
        return <MapPin className="h-5 w-5 text-[#D4C2A4]" />;
    }
  };

  const getPointTypeColor = (type: string) => {
    switch (type) {
      case 'start':
        return 'bg-green-400/20 text-green-300 border-green-400/30';
      case 'end':
        return 'bg-red-400/20 text-red-300 border-red-400/30';
      case 'waypoint':
        return 'bg-[#D4C2A4]/20 text-[#D4C2A4] border-[#D4C2A4]/30';
      default:
        return 'bg-[#D4C2A4]/20 text-[#D4C2A4] border-[#D4C2A4]/30';
    }
  };

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Luxury Route Overview */}
      <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30">
        <div className="flex items-center mb-4 sm:mb-6">
          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-[#D4C2A4] rounded-full mr-3 sm:mr-4"></div>
          <h3 className="font-cormorant text-xl sm:text-2xl lg:text-3xl font-light text-[#F2EEE6]">Safari Route Overview</h3>
        </div>
        <div className="w-16 sm:w-20 h-px bg-gradient-to-r from-[#D4C2A4] to-transparent mb-6 sm:mb-8"></div>

        {/* Luxury Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
          <div className="group">
            <div className="bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-2xl p-4 sm:p-6 text-center transition-all duration-300 hover:bg-[#D4C2A4]/15 hover:border-[#D4C2A4]/30 hover:scale-105">
              <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-[#D4C2A4] to-[#D4C2A4]/80 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300">
                <MapPin className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-[#16191D]" />
              </div>
              <div className="font-cormorant text-2xl sm:text-3xl font-light text-[#F2EEE6] mb-1 sm:mb-2">{routePoints.length}</div>
              <div className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]/70">Exclusive Destinations</div>
            </div>
          </div>
          <div className="group">
            <div className="bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-2xl p-4 sm:p-6 text-center transition-all duration-300 hover:bg-[#D4C2A4]/15 hover:border-[#D4C2A4]/30 hover:scale-105">
              <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-[#D4C2A4] to-[#D4C2A4]/80 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300">
                <Clock className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-[#16191D]" />
              </div>
              <div className="font-cormorant text-2xl sm:text-3xl font-light text-[#F2EEE6] mb-1 sm:mb-2">{tour.duration}</div>
              <div className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]/70">Journey Duration</div>
            </div>
          </div>
          <div className="group sm:col-span-2 lg:col-span-1">
            <div className="bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-2xl p-4 sm:p-6 text-center transition-all duration-300 hover:bg-[#D4C2A4]/15 hover:border-[#D4C2A4]/30 hover:scale-105">
              <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-[#D4C2A4] to-[#D4C2A4]/80 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300">
                <Compass className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-[#16191D]" />
              </div>
              <div className="font-cormorant text-2xl sm:text-3xl font-light text-[#F2EEE6] mb-1 sm:mb-2">Tanzania</div>
              <div className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]/70">Safari Region</div>
            </div>
          </div>
        </div>

        {/* Luxury Route Points List */}
        <div className="space-y-3 sm:space-y-4">
          <h4 className="font-cormorant text-lg sm:text-xl lg:text-2xl font-light text-[#F2EEE6] mb-4 sm:mb-6">Journey Itinerary</h4>
          {routePoints.map((point, index) => (
            <div key={index} className="group relative">
              <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-2xl p-4 sm:p-6 transition-all duration-300 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30 hover:shadow-lg hover:shadow-[#D4C2A4]/10">
                <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 lg:space-x-6">
                  {/* Mobile: Top row with icon and stop number */}
                  <div className="flex items-center justify-between sm:contents">
                    {/* Icon */}
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 sm:w-14 sm:h-14 bg-[#D4C2A4]/20 backdrop-blur-sm border border-[#D4C2A4]/30 rounded-xl flex items-center justify-center group-hover:bg-[#D4C2A4]/30 transition-all duration-300">
                        {getPointTypeIcon(point.type)}
                      </div>
                    </div>

                    {/* Stop Number - Mobile position */}
                    <div className="flex-shrink-0 sm:hidden">
                      <div className="w-8 h-8 bg-gradient-to-br from-[#D4C2A4] to-[#D4C2A4]/80 rounded-xl flex items-center justify-center shadow-lg">
                        <span className="font-cormorant text-sm font-semibold text-[#16191D]">{index + 1}</span>
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 mb-2">
                      <h5 className="font-cormorant text-lg sm:text-xl font-medium text-[#F2EEE6] truncate">{point.name}</h5>
                      <Badge className={`${getPointTypeColor(point.type)} backdrop-blur-sm border font-open-sans text-xs px-2 sm:px-3 py-1 rounded-full self-start sm:self-auto`}>
                        {point.type}
                      </Badge>
                    </div>
                    {point.description && (
                      <p className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/70 leading-relaxed">{point.description}</p>
                    )}
                  </div>

                  {/* Stop Number - Desktop position */}
                  <div className="hidden sm:flex flex-shrink-0">
                    <div className="w-10 h-10 bg-gradient-to-br from-[#D4C2A4] to-[#D4C2A4]/80 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="font-cormorant text-lg font-semibold text-[#16191D]">{index + 1}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Connection Line - Responsive positioning */}
              {index < routePoints.length - 1 && (
                <div className="absolute left-[30px] sm:left-[37px] top-[76px] sm:top-[88px] w-px h-4 sm:h-6 bg-gradient-to-b from-[#D4C2A4]/50 to-[#D4C2A4]/20"></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Luxury Interactive Map */}
      <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30">
        <div className="flex items-center mb-4 sm:mb-6">
          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-[#D4C2A4] rounded-full mr-3 sm:mr-4"></div>
          <h3 className="font-cormorant text-xl sm:text-2xl lg:text-3xl font-light text-[#F2EEE6]">Interactive Safari Map</h3>
        </div>
        <div className="w-16 sm:w-20 h-px bg-gradient-to-r from-[#D4C2A4] to-transparent mb-4 sm:mb-6"></div>
        <div className="rounded-2xl overflow-hidden border border-[#D4C2A4]/20">
          <LeafletMap
            destinations={mapDestinations}
            showRoutes={true}
            height={mapHeight}
            onDestinationClick={(destination) => {
              console.log('Clicked destination:', destination);
            }}
          />
        </div>
      </div>

      {/* Luxury Route Information */}
      <div className="bg-[#D4C2A4]/5 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-3xl p-4 sm:p-6 lg:p-8 transition-all duration-500 hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/30">
        <div className="flex items-center mb-4 sm:mb-6">
          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-[#D4C2A4] rounded-full mr-3 sm:mr-4"></div>
          <h3 className="font-cormorant text-xl sm:text-2xl lg:text-3xl font-light text-[#F2EEE6]">Safari Experience Details</h3>
        </div>
        <div className="w-16 sm:w-20 h-px bg-gradient-to-r from-[#D4C2A4] to-transparent mb-6 sm:mb-8"></div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
          <div className="space-y-4 sm:space-y-6">
            <h4 className="font-cormorant text-lg sm:text-xl lg:text-2xl text-[#D4C2A4] font-medium">What to Expect</h4>
            <ul className="space-y-3 sm:space-y-4">
              <li className="flex items-start gap-3 sm:gap-4 group">
                <div className="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center mt-0.5 group-hover:bg-[#D4C2A4]/30 transition-colors duration-300">
                  <span className="text-[#D4C2A4] text-xs sm:text-sm">●</span>
                </div>
                <span className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80 leading-relaxed">Professional guide throughout the journey</span>
              </li>
              <li className="flex items-start gap-3 sm:gap-4 group">
                <div className="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center mt-0.5 group-hover:bg-[#D4C2A4]/30 transition-colors duration-300">
                  <span className="text-[#D4C2A4] text-xs sm:text-sm">●</span>
                </div>
                <span className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80 leading-relaxed">Luxury 4WD safari vehicles with panoramic windows</span>
              </li>
              <li className="flex items-start gap-3 sm:gap-4 group">
                <div className="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center mt-0.5 group-hover:bg-[#D4C2A4]/30 transition-colors duration-300">
                  <span className="text-[#D4C2A4] text-xs sm:text-sm">●</span>
                </div>
                <span className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80 leading-relaxed">Strategic stops for optimal wildlife viewing</span>
              </li>
              <li className="flex items-start gap-3 sm:gap-4 group">
                <div className="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center mt-0.5 group-hover:bg-[#D4C2A4]/30 transition-colors duration-300">
                  <span className="text-[#D4C2A4] text-xs sm:text-sm">●</span>
                </div>
                <span className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80 leading-relaxed">Exclusive photo opportunities at scenic viewpoints</span>
              </li>
            </ul>
          </div>
          <div className="space-y-4 sm:space-y-6">
            <h4 className="font-cormorant text-lg sm:text-xl lg:text-2xl text-[#D4C2A4] font-medium">Route Highlights</h4>
            <ul className="space-y-3 sm:space-y-4">
              {tour.destinations?.slice(0, 4).map((destination, index) => (
                <li key={index} className="flex items-start gap-3 sm:gap-4 group">
                  <div className="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 bg-[#D4C2A4]/20 rounded-full flex items-center justify-center mt-0.5 group-hover:bg-[#D4C2A4]/30 transition-colors duration-300">
                    <span className="text-[#D4C2A4] text-xs sm:text-sm">●</span>
                  </div>
                  <span className="font-open-sans text-sm sm:text-base text-[#F2EEE6]/80 leading-relaxed">{destination}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TourRouteMap;


import React from 'react';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { AdminSidebar } from '@/components/admin/AdminSidebar';
import { Routes, Route } from 'react-router-dom';
import AdminOverview from './AdminOverview';
import AdminReviews from './AdminReviews';
import AdminBlog from './AdminBlog';
import AdminGallery from './AdminGallery';
import AdminSubscriptions from './AdminSubscriptions';
import TourManagement from '@/components/admin/TourManagement';
import BookingManagement from '@/components/admin/BookingManagement';
import UserManagement from '@/components/admin/UserManagement';
import MessageManagement from '@/components/admin/MessageManagement';
import DestinationManagement from '@/components/admin/DestinationManagement';
import CustomTours from './CustomTours';

const AdminDashboard = () => {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AdminSidebar />
        <SidebarInset>
          {/* Mobile Navigation Header */}
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 md:hidden">
            <SidebarTrigger className="-ml-1" />
            <div className="flex items-center gap-2">
              <h1 className="text-lg font-semibold">Admin Panel</h1>
            </div>
          </header>
          <main className="flex-1 p-3 md:p-6 overflow-x-auto">
            <Routes>
              <Route index element={<AdminOverview />} />
              <Route path="tours" element={<TourManagement />} />
              <Route path="bookings" element={<BookingManagement />} />
              <Route path="users" element={<UserManagement />} />
              <Route path="reviews" element={<AdminReviews />} />
              <Route path="messages" element={<MessageManagement />} />
              <Route path="blog" element={<AdminBlog />} />
              <Route path="gallery" element={<AdminGallery />} />
              <Route path="destinations" element={<DestinationManagement />} />
              <Route path="custom-tours" element={<CustomTours />} />
              <Route path="subscriptions" element={<AdminSubscriptions />} />
            </Routes>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default AdminDashboard;

/* Premium Destination Carousel Styles */

.carousel-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.glass-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 40%,
    rgba(0, 0, 0, 0.1) 70%,
    transparent 100%
  );
  backdrop-filter: blur(0.5px);
}

.destination-text {
  position: relative;
  z-index: 10;
  /* Ensure text stays on left side and doesn't overlap with cards */
  max-width: 60%;
}

@media (min-width: 1024px) {
  .destination-text {
    max-width: 55%;
  }
}

@media (min-width: 1280px) {
  .destination-text {
    max-width: 50%;
  }
}

.destination-name {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 800;
  letter-spacing: -0.02em;
  line-height: 0.9;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.destination-location {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 300;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.carousel-card {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center center;
  /* Portrait aspect ratio for tall cards */
  aspect-ratio: 3/4;
}

.carousel-card:hover {
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 16px rgba(0, 0, 0, 0.3);
}

.carousel-card.active {
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 10px 20px rgba(0, 0, 0, 0.4),
    0 0 0 2px rgba(255, 255, 255, 0.2);
}

/* Card transition animation styles */
.card-transition-container {
  position: relative;
  transform-origin: center center;
}

.card-expanding {
  position: fixed !important;
  z-index: 100 !important;
  pointer-events: none;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.carousel-card:hover .card-image {
  transform: scale(1.05);
}

.card-gradient {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    180deg,
    transparent 0%,
    transparent 40%,
    rgba(0, 0, 0, 0.3) 70%,
    rgba(0, 0, 0, 0.8) 100%
  );
  pointer-events: none;
}

.active-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 1) 50%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border-radius: 0 0 12px 12px;
}

.navigation-button {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.navigation-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.navigation-button:active {
  transform: scale(0.95);
}

.pagination-counter {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 200;
  font-feature-settings: 'tnum';
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Bottom-right card layout styles - Horizontal Stack of TALL cards */
.cards-container {
  display: flex;
  flex-direction: row;
  gap: 0.75rem;
  align-items: flex-end;
}

/* Bottom-right positioning container - matching photo layout */
.bottom-right-container {
  position: absolute;
  bottom: 1.5rem;
  right: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.75rem;
  z-index: 20;
}

@media (min-width: 1024px) {
  .cards-container {
    gap: 0.75rem;
  }

  .bottom-right-container {
    bottom: 3rem;
    right: 3rem;
    gap: 1rem;
  }
}

@media (min-width: 1280px) {
  .cards-container {
    gap: 1rem;
  }

  .bottom-right-container {
    bottom: 3rem;
    right: 3rem;
    gap: 1rem;
  }
}

/* Ensure text container doesn't interfere with cards */
.text-container-left {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 60%;
  z-index: 15;
}

@media (min-width: 1024px) {
  .text-container-left {
    width: 55%;
  }
}

@media (min-width: 1280px) {
  .text-container-left {
    width: 50%;
  }
}

/* Responsive adjustments - TALL PORTRAIT CARDS (matching photo proportions) */
@media (max-width: 768px) {
  .destination-name {
    font-size: 2.5rem;
    line-height: 0.95;
  }

  .destination-location {
    font-size: 0.875rem;
  }

  .carousel-card {
    width: 48px;
    height: 72px;
  }

  .pagination-counter {
    font-size: 1.25rem;
  }

  .cards-container {
    padding-right: 0.5rem;
    gap: 0.375rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .carousel-card {
    width: 64px;
    height: 96px;
  }

  .destination-name {
    font-size: 4rem;
  }

  .destination-location {
    font-size: 1rem;
  }
}

@media (min-width: 1025px) {
  .carousel-card {
    width: 80px;
    height: 128px;
  }

  .destination-name {
    font-size: 5rem;
  }

  .destination-location {
    font-size: 1.125rem;
  }
}

@media (min-width: 1280px) {
  .carousel-card {
    width: 96px;
    height: 160px;
  }

  .destination-name {
    font-size: 6rem;
  }

  .destination-location {
    font-size: 1.25rem;
  }
}



/* Animation keyframes */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
  pointer-events: none;
}

/* Glass morphism effects */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

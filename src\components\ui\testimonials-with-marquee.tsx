import { cn } from "@/lib/utils"
import { TestimonialCard, TestimonialAuthor } from "@/components/ui/testimonial-card"
import { useRef, useEffect } from "react"

interface TestimonialsSectionProps {
  title: string
  description: string
  testimonials: Array<{
    author: TestimonialAuthor
    text: string
    href?: string
  }>
  className?: string
}

export function TestimonialsSection({ 
  title,
  description,
  testimonials,
  className 
}: TestimonialsSectionProps) {
  const marqueeRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // Calculate the total width of all testimonials
    const calculateWidth = () => {
      if (marqueeRef.current) {
        const firstChild = marqueeRef.current.firstElementChild as HTMLElement;
        if (firstChild) {
          // Set a CSS variable with the width for the animation
          marqueeRef.current.style.setProperty('--marquee-width', `${firstChild.scrollWidth}px`);
        }
      }
    };
    
    calculateWidth();
    window.addEventListener('resize', calculateWidth);
    
    return () => {
      window.removeEventListener('resize', calculateWidth);
    };
  }, [testimonials]);
  
  return (
    <section className={cn(
      "bg-background text-foreground",
      "py-12 sm:py-24 md:py-32 px-0",
      className
    )}>
      <div className="mx-auto flex max-w-container flex-col items-center gap-4 text-center sm:gap-16">
        <div className="flex flex-col items-center gap-4 px-4 sm:gap-8">
          <h2 className="max-w-[720px] text-3xl font-semibold leading-tight sm:text-5xl sm:leading-tight">
            {title}
          </h2>
          <p className="text-md max-w-[600px] font-medium text-muted-foreground sm:text-xl">
            {description}
          </p>
        </div>

        <div className="relative w-full overflow-hidden">
          <div
            ref={marqueeRef}
            className="flex"
            style={{
              '--gap': '1.5rem',
              '--duration': '40s'
            } as React.CSSProperties}
          >
            <div className="flex gap-6 py-4 animate-marquee">
              {testimonials.map((testimonial, i) => (
                <TestimonialCard
                  key={`original-${i}`}
                  {...testimonial}
                  className="flex-shrink-0 w-[320px] sm:w-[350px]"
                />
              ))}
            </div>
            <div className="flex gap-6 py-4 animate-marquee">
              {testimonials.map((testimonial, i) => (
                <TestimonialCard
                  key={`duplicate-${i}`}
                  {...testimonial}
                  className="flex-shrink-0 w-[320px] sm:w-[350px]"
                />
              ))}
            </div>
          </div>
          <div className="pointer-events-none absolute inset-y-0 left-0 w-1/3 bg-gradient-to-r from-background to-transparent" />
          <div className="pointer-events-none absolute inset-y-0 right-0 w-1/3 bg-gradient-to-l from-background to-transparent" />
        </div>
      </div>
    </section>
  )
}

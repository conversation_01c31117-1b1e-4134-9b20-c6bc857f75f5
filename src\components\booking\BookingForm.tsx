
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, Plus, Minus, CreditCard, Phone, Mail } from 'lucide-react';
import { format } from 'date-fns';

interface BookingFormProps {
  currentStep: number;
  bookingData: any;
  onDataChange: (field: string, value: any) => void;
  onNext: () => void;
  onPrev: () => void;
}

const BookingForm: React.FC<BookingFormProps> = ({
  currentStep,
  bookingData,
  onDataChange,
  onNext,
  onPrev
}) => {
  const [availableDates, setAvailableDates] = useState<Date[]>([
    new Date(2024, 6, 15), // July 15, 2024
    new Date(2024, 6, 22), // July 22, 2024
    new Date(2024, 7, 5),  // August 5, 2024
    new Date(2024, 7, 12), // August 12, 2024
    new Date(2024, 7, 19), // August 19, 2024
  ]);

  const addTraveler = () => {
    const newTraveler = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: null,
      passportNumber: '',
      nationality: ''
    };
    onDataChange('travelers', [...bookingData.travelers, newTraveler]);
  };

  const removeTraveler = (index: number) => {
    const updatedTravelers = bookingData.travelers.filter((_: any, i: number) => i !== index);
    onDataChange('travelers', updatedTravelers);
  };

  const updateTraveler = (index: number, field: string, value: any) => {
    const updatedTravelers = [...bookingData.travelers];
    updatedTravelers[index] = { ...updatedTravelers[index], [field]: value };
    onDataChange('travelers', updatedTravelers);
  };

  const addOns = [
    { id: 'airport-transfer', name: 'Airport Transfer', price: 150, description: 'Round trip airport pickup and drop-off' },
    { id: 'cultural-visit', name: 'Cultural Village Visit', price: 200, description: 'Visit local Maasai village and learn about their culture' },
    { id: 'hot-air-balloon', name: 'Hot Air Balloon Safari', price: 450, description: 'Early morning balloon ride over the Serengeti' },
    { id: 'photography-guide', name: 'Photography Guide', price: 300, description: 'Professional photography guide for the entire trip' },
    { id: 'extended-game-drive', name: 'Extended Game Drives', price: 180, description: 'Additional hours for game viewing' }
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <Card>
            <CardHeader>
              <CardTitle>Tour Details & Dates</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Date Selection */}
              <div>
                <Label htmlFor="start-date">Select Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {bookingData.startDate ? format(bookingData.startDate, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={bookingData.startDate}
                      onSelect={(date) => onDataChange('startDate', date)}
                      disabled={(date) => !availableDates.some(availableDate => 
                        availableDate.toDateString() === date.toDateString()
                      )}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <div className="mt-2">
                  <p className="text-sm text-gray-600 mb-2">Available dates:</p>
                  <div className="flex flex-wrap gap-2">
                    {availableDates.map((date, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {format(date, "MMM dd, yyyy")}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Group Size */}
              <div>
                <Label htmlFor="group-size">Group Size</Label>
                <div className="flex items-center space-x-4 mt-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => onDataChange('groupSize', Math.max(1, bookingData.groupSize - 1))}
                    disabled={bookingData.groupSize <= 1}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="text-xl font-semibold w-12 text-center">{bookingData.groupSize}</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => onDataChange('groupSize', Math.min(8, bookingData.groupSize + 1))}
                    disabled={bookingData.groupSize >= 8}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mt-1">Maximum 8 travelers per group</p>
              </div>

              {/* Tour Options */}
              <div>
                <Label>Tour Package</Label>
                <div className="mt-2 space-y-3">
                  <div className="p-4 border rounded-lg bg-orange-50 border-orange-200">
                    <h4 className="font-semibold text-orange-800">5-Day Serengeti & Ngorongoro Safari</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      Complete safari experience including Serengeti National Park and Ngorongoro Crater
                    </p>
                    <div className="mt-2 flex justify-between items-center">
                      <div className="text-2xl font-bold text-orange-600">$2,499</div>
                      <div className="text-sm text-gray-500">per person</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 2:
        return (
          <Card>
            <CardHeader>
              <CardTitle>Traveler Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Ensure we have enough traveler forms */}
              {Array.from({ length: bookingData.groupSize }, (_, index) => {
                const traveler = bookingData.travelers[index] || {
                  firstName: '',
                  lastName: '',
                  email: '',
                  phone: '',
                  dateOfBirth: null,
                  passportNumber: '',
                  nationality: ''
                };

                return (
                  <div key={index} className="p-6 border rounded-lg space-y-4">
                    <div className="flex justify-between items-center">
                      <h4 className="text-lg font-semibold">Traveler {index + 1}</h4>
                      {index === 0 && <Badge>Lead Traveler</Badge>}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor={`firstName-${index}`}>First Name</Label>
                        <Input
                          id={`firstName-${index}`}
                          value={traveler.firstName}
                          onChange={(e) => updateTraveler(index, 'firstName', e.target.value)}
                          placeholder="Enter first name"
                        />
                      </div>
                      <div>
                        <Label htmlFor={`lastName-${index}`}>Last Name</Label>
                        <Input
                          id={`lastName-${index}`}
                          value={traveler.lastName}
                          onChange={(e) => updateTraveler(index, 'lastName', e.target.value)}
                          placeholder="Enter last name"
                        />
                      </div>
                      <div>
                        <Label htmlFor={`email-${index}`}>Email</Label>
                        <Input
                          id={`email-${index}`}
                          type="email"
                          value={traveler.email}
                          onChange={(e) => updateTraveler(index, 'email', e.target.value)}
                          placeholder="Enter email address"
                        />
                      </div>
                      <div>
                        <Label htmlFor={`phone-${index}`}>Phone</Label>
                        <Input
                          id={`phone-${index}`}
                          value={traveler.phone}
                          onChange={(e) => updateTraveler(index, 'phone', e.target.value)}
                          placeholder="Enter phone number"
                        />
                      </div>
                      <div>
                        <Label htmlFor={`passport-${index}`}>Passport Number</Label>
                        <Input
                          id={`passport-${index}`}
                          value={traveler.passportNumber}
                          onChange={(e) => updateTraveler(index, 'passportNumber', e.target.value)}
                          placeholder="Enter passport number"
                        />
                      </div>
                      <div>
                        <Label htmlFor={`nationality-${index}`}>Nationality</Label>
                        <Input
                          id={`nationality-${index}`}
                          value={traveler.nationality}
                          onChange={(e) => updateTraveler(index, 'nationality', e.target.value)}
                          placeholder="Enter nationality"
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        );

      case 3:
        return (
          <Card>
            <CardHeader>
              <CardTitle>Preferences & Add-ons</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Accommodation Level */}
              <div>
                <Label>Accommodation Level</Label>
                <Select value={bookingData.accommodation} onValueChange={(value) => onDataChange('accommodation', value)}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select accommodation level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="budget">Budget - Camping & Basic Lodges</SelectItem>
                    <SelectItem value="midrange">Mid-Range - Tented Camps</SelectItem>
                    <SelectItem value="luxury">Luxury - Premium Lodges</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Add-ons */}
              <div>
                <Label>Optional Add-ons</Label>
                <div className="mt-4 space-y-4">
                  {addOns.map((addon) => (
                    <div key={addon.id} className="flex items-start space-x-3 p-4 border rounded-lg">
                      <Checkbox
                        id={addon.id}
                        checked={bookingData.addOns.includes(addon.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            onDataChange('addOns', [...bookingData.addOns, addon.id]);
                          } else {
                            onDataChange('addOns', bookingData.addOns.filter((id: string) => id !== addon.id));
                          }
                        }}
                      />
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <Label htmlFor={addon.id} className="font-medium cursor-pointer">
                              {addon.name}
                            </Label>
                            <p className="text-sm text-gray-600 mt-1">{addon.description}</p>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-green-600">+${addon.price}</div>
                            <div className="text-xs text-gray-500">per person</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Special Requests */}
              <div>
                <Label htmlFor="special-requests">Special Requests or Dietary Requirements</Label>
                <Textarea
                  id="special-requests"
                  value={bookingData.specialRequests}
                  onChange={(e) => onDataChange('specialRequests', e.target.value)}
                  placeholder="Let us know about any dietary restrictions, accessibility needs, or special occasions..."
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        );

      case 4:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="mr-2 h-5 w-5" />
                Payment & Confirmation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Payment Method */}
              <div>
                <Label>Payment Method</Label>
                <div className="mt-3 space-y-3">
                  <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input type="radio" name="payment" value="card" defaultChecked />
                    <div className="flex-1">
                      <div className="font-medium">Credit/Debit Card</div>
                      <div className="text-sm text-gray-600">Secure payment via Stripe</div>
                    </div>
                    <div className="flex space-x-2">
                      <img src="https://js.stripe.com/v3/fingerprinted/img/visa-729c05c240c4bdb47b03ac81d9945bfe.svg" alt="Visa" className="h-6" />
                      <img src="https://js.stripe.com/v3/fingerprinted/img/mastercard-4d8844094130711885b5e41b28c9848f.svg" alt="Mastercard" className="h-6" />
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input type="radio" name="payment" value="bank" />
                    <div className="flex-1">
                      <div className="font-medium">Bank Transfer</div>
                      <div className="text-sm text-gray-600">Direct bank transfer (3-5 business days)</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input type="radio" name="payment" value="cash" />
                    <div className="flex-1">
                      <div className="font-medium">Cash Payment</div>
                      <div className="text-sm text-gray-600">Pay in cash upon arrival (USD accepted)</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <Label>Emergency Contact</Label>
                <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="emergency-name">Contact Name</Label>
                    <Input
                      id="emergency-name"
                      placeholder="Emergency contact name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="emergency-phone">Contact Phone</Label>
                    <Input
                      id="emergency-phone"
                      placeholder="Emergency contact phone"
                    />
                  </div>
                </div>
              </div>

              {/* Terms and Conditions */}
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Checkbox id="terms" />
                  <Label htmlFor="terms" className="text-sm leading-relaxed cursor-pointer">
                    I agree to the <a href="/terms" className="text-orange-600 hover:underline">Terms and Conditions</a> and <a href="/privacy" className="text-orange-600 hover:underline">Privacy Policy</a>
                  </Label>
                </div>
                <div className="flex items-start space-x-3">
                  <Checkbox id="marketing" />
                  <Label htmlFor="marketing" className="text-sm leading-relaxed cursor-pointer">
                    I would like to receive marketing communications about special offers and safari updates
                  </Label>
                </div>
              </div>

              {/* Booking Confirmation */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <Phone className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-semibold text-green-800">Booking Confirmation</div>
                    <div className="text-sm text-green-600">We'll call you within 24 hours to confirm your booking</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-semibold text-green-800">Email Confirmation</div>
                    <div className="text-sm text-green-600">Detailed itinerary and travel documents will be sent via email</div>
                  </div>
                </div>
              </div>

              <Button className="w-full bg-green-600 hover:bg-green-700 text-white py-6 text-lg">
                Complete Booking - ${bookingData.totalPrice}
              </Button>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  // Ensure travelers array is properly initialized
  React.useEffect(() => {
    if (bookingData.travelers.length < bookingData.groupSize) {
      const newTravelers = [...bookingData.travelers];
      while (newTravelers.length < bookingData.groupSize) {
        newTravelers.push({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          dateOfBirth: null,
          passportNumber: '',
          nationality: ''
        });
      }
      onDataChange('travelers', newTravelers);
    }
  }, [bookingData.groupSize]);

  return (
    <div>
      {renderStepContent()}
    </div>
  );
};

export default BookingForm;

// Third-party script optimization utilities

interface ScriptConfig {
  src: string;
  async?: boolean;
  defer?: boolean;
  crossOrigin?: string;
  integrity?: string;
  onLoad?: () => void;
  onError?: () => void;
  condition?: () => boolean;
}

// Script loading manager
class ScriptManager {
  private loadedScripts = new Set<string>();
  private loadingScripts = new Map<string, Promise<void>>();

  async loadScript(config: ScriptConfig): Promise<void> {
    const { src, async = true, defer = false, crossOrigin, integrity, onLoad, onError, condition } = config;

    // Check condition if provided
    if (condition && !condition()) {
      return Promise.resolve();
    }

    // Return existing promise if script is already loading
    if (this.loadingScripts.has(src)) {
      return this.loadingScripts.get(src)!;
    }

    // Return immediately if script is already loaded
    if (this.loadedScripts.has(src)) {
      return Promise.resolve();
    }

    const promise = new Promise<void>((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.async = async;
      script.defer = defer;
      
      if (crossOrigin) script.crossOrigin = crossOrigin;
      if (integrity) script.integrity = integrity;

      script.onload = () => {
        this.loadedScripts.add(src);
        this.loadingScripts.delete(src);
        onLoad?.();
        resolve();
      };

      script.onerror = () => {
        this.loadingScripts.delete(src);
        onError?.();
        reject(new Error(`Failed to load script: ${src}`));
      };

      document.head.appendChild(script);
    });

    this.loadingScripts.set(src, promise);
    return promise;
  }

  isLoaded(src: string): boolean {
    return this.loadedScripts.has(src);
  }

  async loadMultiple(configs: ScriptConfig[]): Promise<void> {
    await Promise.all(configs.map(config => this.loadScript(config)));
  }
}

export const scriptManager = new ScriptManager();

// Google Analytics optimization
export function loadGoogleAnalytics(measurementId: string) {
  if (!measurementId || typeof window === 'undefined') return;

  // Load only if user hasn't opted out
  const hasConsent = localStorage.getItem('analytics-consent') === 'true';
  if (!hasConsent) return;

  return scriptManager.loadScript({
    src: `https://www.googletagmanager.com/gtag/js?id=${measurementId}`,
    async: true,
    condition: () => !window.gtag,
    onLoad: () => {
      window.dataLayer = window.dataLayer || [];
      function gtag(...args: any[]) {
        window.dataLayer.push(args);
      }
      window.gtag = gtag;
      gtag('js', new Date());
      gtag('config', measurementId, {
        page_title: document.title,
        page_location: window.location.href,
      });
    }
  });
}

// Google Maps optimization
export function loadGoogleMaps(apiKey: string, libraries: string[] = []) {
  if (!apiKey || typeof window === 'undefined') return;

  const librariesParam = libraries.length > 0 ? `&libraries=${libraries.join(',')}` : '';
  
  return scriptManager.loadScript({
    src: `https://maps.googleapis.com/maps/api/js?key=${apiKey}${librariesParam}`,
    async: true,
    defer: true,
    condition: () => !window.google?.maps,
  });
}

// Font optimization
export function optimizeFonts() {
  if (typeof document === 'undefined') return;

  // Preconnect to font providers
  const preconnectLinks = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com'
  ];

  preconnectLinks.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = href;
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });

  // Load fonts with font-display: swap
  const fontLink = document.createElement('link');
  fontLink.rel = 'stylesheet';
  fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap';
  document.head.appendChild(fontLink);
}

// Resource hints for performance
export function addResourceHints() {
  if (typeof document === 'undefined') return;

  const hints = [
    // DNS prefetch for external domains
    { rel: 'dns-prefetch', href: 'https://api.openweathermap.org' },
    { rel: 'dns-prefetch', href: 'https://firebase.googleapis.com' },
    { rel: 'dns-prefetch', href: 'https://firestore.googleapis.com' },
    { rel: 'dns-prefetch', href: 'https://www.google-analytics.com' },
    
    // Preconnect for critical resources
    { rel: 'preconnect', href: 'https://fonts.googleapis.com', crossOrigin: 'anonymous' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossOrigin: 'anonymous' },
  ];

  hints.forEach(hint => {
    const link = document.createElement('link');
    link.rel = hint.rel;
    link.href = hint.href;
    if (hint.crossOrigin) link.crossOrigin = hint.crossOrigin;
    document.head.appendChild(link);
  });
}

// Lazy load non-critical scripts
export function loadNonCriticalScripts() {
  // Load after page is interactive
  if (document.readyState === 'loading') {
    const handleDOMContentLoaded = () => {
      document.removeEventListener('DOMContentLoaded', handleDOMContentLoaded);
      loadNonCriticalScripts();
    };
    document.addEventListener('DOMContentLoaded', handleDOMContentLoaded);
    return;
  }

  // Use requestIdleCallback for better performance
  const loadWhenIdle = (callback: () => void) => {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(callback, { timeout: 5000 });
    } else {
      setTimeout(callback, 1000);
    }
  };

  loadWhenIdle(() => {
    // Load analytics if consent is given
    const analyticsId = import.meta.env.VITE_GA_MEASUREMENT_ID;
    if (analyticsId) {
      loadGoogleAnalytics(analyticsId);
    }

    // Load other non-critical scripts here
    // Example: Chat widgets, social media embeds, etc.
  });
}

// Performance monitoring for third-party scripts
export function monitorThirdPartyPerformance() {
  if (!('PerformanceObserver' in window)) return;

  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.entryType === 'resource') {
        const resourceEntry = entry as PerformanceResourceTiming;
        
        // Monitor third-party script performance
        if (resourceEntry.name.includes('googleapis.com') ||
            resourceEntry.name.includes('google-analytics.com') ||
            resourceEntry.name.includes('googletagmanager.com')) {
          
          const loadTime = resourceEntry.responseEnd - resourceEntry.startTime;
          
          if (loadTime > 1000) { // More than 1 second
            console.warn(`Slow third-party script: ${resourceEntry.name} took ${loadTime}ms`);
          }
          
          // Send to analytics if needed
          if (window.gtag) {
            window.gtag('event', 'third_party_performance', {
              event_category: 'Performance',
              event_label: resourceEntry.name,
              value: Math.round(loadTime),
            });
          }
        }
      }
    });
  });

  observer.observe({ entryTypes: ['resource'] });
}

// Consent management for third-party scripts
export class ConsentManager {
  private consents = new Map<string, boolean>();

  constructor() {
    this.loadConsents();
  }

  private loadConsents() {
    try {
      const stored = localStorage.getItem('user-consents');
      if (stored) {
        const consents = JSON.parse(stored);
        Object.entries(consents).forEach(([key, value]) => {
          this.consents.set(key, value as boolean);
        });
      }
    } catch (error) {
      console.error('Error loading consents:', error);
    }
  }

  private saveConsents() {
    try {
      const consents = Object.fromEntries(this.consents);
      localStorage.setItem('user-consents', JSON.stringify(consents));
    } catch (error) {
      console.error('Error saving consents:', error);
    }
  }

  setConsent(type: string, granted: boolean) {
    this.consents.set(type, granted);
    this.saveConsents();
    
    // Trigger consent change event
    window.dispatchEvent(new CustomEvent('consentchange', {
      detail: { type, granted }
    }));
  }

  hasConsent(type: string): boolean {
    return this.consents.get(type) ?? false;
  }

  getAllConsents(): Record<string, boolean> {
    return Object.fromEntries(this.consents);
  }
}

export const consentManager = new ConsentManager();

// Initialize third-party optimizations
export function initializeThirdPartyOptimizations() {
  // Add resource hints
  addResourceHints();
  
  // Optimize fonts
  optimizeFonts();
  
  // Monitor performance
  monitorThirdPartyPerformance();
  
  // Load non-critical scripts
  loadNonCriticalScripts();
  
  // Listen for consent changes
  const handleConsentChange = (event: any) => {
    const { type, granted } = event.detail;
    
    if (type === 'analytics' && granted) {
      const analyticsId = import.meta.env.VITE_GA_MEASUREMENT_ID;
      if (analyticsId) {
        loadGoogleAnalytics(analyticsId);
      }
    }
  };
  
  window.addEventListener('consentchange', handleConsentChange);
  
  // Return cleanup function for the initialization
  return () => {
    window.removeEventListener('consentchange', handleConsentChange);
  };
}

// Declare global types
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
    google: any;
  }
}

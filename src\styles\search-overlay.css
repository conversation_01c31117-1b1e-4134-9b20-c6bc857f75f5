/* Luxury Search Overlay Styles */

/* Overlay entrance animation */
.luxury-search-overlay {
  animation: overlayFadeIn 0.5s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(20px);
  }
}

/* Custom luxury scrollbar for search overlay */
.luxury-search-overlay::-webkit-scrollbar {
  width: 8px;
}

.luxury-search-overlay::-webkit-scrollbar-track {
  background: rgba(22, 25, 29, 0.8);
  border-radius: 8px;
}

.luxury-search-overlay::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #D4C2A4, rgba(212, 194, 164, 0.7));
  border-radius: 8px;
  border: 1px solid rgba(212, 194, 164, 0.3);
}

.luxury-search-overlay::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #D4C2A4, rgba(212, 194, 164, 0.9));
  box-shadow: 0 0 10px rgba(212, 194, 164, 0.3);
}

/* Luxury glow animation for title */
@keyframes luxuryGlow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(212, 194, 164, 0.3);
  }
  50% {
    text-shadow: 0 0 30px rgba(212, 194, 164, 0.5);
  }
}

.luxury-glow {
  animation: luxuryGlow 3s ease-in-out infinite;
}

/* Luxury loading animation */
@keyframes luxuryShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.luxury-loading {
  background: linear-gradient(
    90deg,
    rgba(212, 194, 164, 0.1) 25%,
    rgba(212, 194, 164, 0.3) 50%,
    rgba(212, 194, 164, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: luxuryShimmer 2s infinite;
}

/* Glass morphism enhancements */
.glass-morphism-luxury {
  background: rgba(212, 194, 164, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(212, 194, 164, 0.2);
  box-shadow: 
    0 8px 32px rgba(22, 25, 29, 0.3),
    inset 0 1px 0 rgba(212, 194, 164, 0.1);
}

.glass-morphism-luxury:hover {
  background: rgba(212, 194, 164, 0.1);
  border-color: rgba(212, 194, 164, 0.4);
  box-shadow: 
    0 20px 40px rgba(22, 25, 29, 0.4),
    0 0 0 1px rgba(212, 194, 164, 0.2),
    inset 0 1px 0 rgba(212, 194, 164, 0.2);
}

/* Line clamp utilities for responsive text */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive enhancements */
@media (max-width: 640px) {
  .luxury-search-overlay {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Reduce glow intensity on mobile for better performance */
  .luxury-glow {
    animation: none;
    text-shadow: 0 0 15px rgba(212, 194, 164, 0.3);
  }

  /* Optimize scrollbar for mobile */
  .luxury-search-overlay::-webkit-scrollbar {
    width: 6px;
  }

  /* Mobile-specific touch optimizations */
  .luxury-search-overlay .group {
    -webkit-tap-highlight-color: rgba(212, 194, 164, 0.1);
  }

  /* Improve touch targets on mobile */
  .luxury-search-overlay button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Reduce backdrop blur on mobile for performance */
  .glass-morphism-luxury {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

@media (max-width: 480px) {
  /* Further mobile optimizations */
  .luxury-search-overlay::-webkit-scrollbar {
    width: 4px;
  }
  
  .luxury-search-overlay::-webkit-scrollbar-thumb {
    border: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-morphism-luxury {
    background: rgba(212, 194, 164, 0.15);
    border-color: rgba(212, 194, 164, 0.5);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .luxury-glow {
    animation: none;
  }
  
  .luxury-loading {
    animation: none;
    background: rgba(212, 194, 164, 0.2);
  }
  
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
  }
}

/* Focus styles for accessibility */
.luxury-search-overlay input:focus {
  outline: 2px solid rgba(212, 194, 164, 0.5);
  outline-offset: 2px;
}

.luxury-search-overlay button:focus {
  outline: 2px solid rgba(212, 194, 164, 0.5);
  outline-offset: 2px;
}

/* Enhanced hover effects for luxury feel */
.luxury-search-overlay .group:hover {
  transform: translateY(-2px);
}

@media (max-width: 640px) {
  .luxury-search-overlay .group:hover {
    transform: translateY(-1px);
  }
}

/* Smooth scrolling */
.luxury-search-overlay {
  scroll-behavior: smooth;
}

/* Safari-specific enhancements */
@supports (-webkit-backdrop-filter: blur(20px)) {
  .glass-morphism-luxury {
    -webkit-backdrop-filter: blur(20px);
  }
}

/* Search results entrance animation */
.search-result-item {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.search-result-item:nth-child(1) { animation-delay: 0.1s; }
.search-result-item:nth-child(2) { animation-delay: 0.2s; }
.search-result-item:nth-child(3) { animation-delay: 0.3s; }
.search-result-item:nth-child(4) { animation-delay: 0.4s; }
.search-result-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Disable animations on mobile for better performance */
@media (max-width: 640px) {
  .search-result-item {
    animation: none;
  }
}

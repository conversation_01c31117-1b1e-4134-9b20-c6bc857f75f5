import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { FirebaseService } from '@/services/firebase';
import { Timestamp } from 'firebase/firestore';

interface NewsletterSignupProps {
  className?: string;
  variant?: 'footer' | 'inline';
}

const NewsletterSignup: React.FC<NewsletterSignupProps> = ({ 
  className = '', 
  variant = 'footer' 
}) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast({
        title: "Email required",
        description: "Please enter your email address.",
        variant: "destructive"
      });
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast({
        title: "Invalid email",
        description: "Please enter a valid email address.",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      // Check if email already exists
      const existingSubscriptions = await FirebaseService.getNewsletterSubscriptions();
      const emailExists = existingSubscriptions.some((sub: any) => sub.email === email);
      
      if (emailExists) {
        toast({
          title: "Already subscribed",
          description: "This email is already subscribed to our newsletter.",
          variant: "destructive"
        });
        setLoading(false);
        return;
      }

      // Add to newsletter subscriptions
      await FirebaseService.addNewsletterSubscription({
        email,
        subscribedAt: Timestamp.now(),
        status: 'active',
        preferences: {
          tourUpdates: true,
          specialOffers: true,
          travelTips: true
        }
      });

      toast({
        title: "Successfully subscribed!",
        description: "Thank you for subscribing to our newsletter. You'll receive travel tips and exclusive offers.",
      });

      setEmail('');
    } catch (error) {
      console.error('Error subscribing to newsletter:', error);
      toast({
        title: "Subscription failed",
        description: "There was an error subscribing to our newsletter. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  if (variant === 'footer') {
    return (
      <div className={className}>
        <h4 className="font-semibold mb-2 text-sm md:text-base">Newsletter</h4>
        <p className="text-gray-300 text-xs md:text-sm mb-3">
          Get travel tips and exclusive offers
        </p>
        <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-2">
          <Input
            type="email"
            placeholder="Your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="bg-gray-800 border-gray-700 text-white placeholder-gray-400 text-sm flex-1"
            disabled={loading}
          />
          <Button
            type="submit"
            disabled={loading}
            className="bg-[#D4C2A4] hover:bg-[#D4C2A4]/90 text-black text-sm px-4 py-2 whitespace-nowrap"
          >
            {loading ? 'Subscribing...' : 'Subscribe'}
          </Button>
        </form>
      </div>
    );
  }

  // Inline variant for other pages
  return (
    <div className={className}>
      <div className="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-lg border border-orange-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Stay Updated</h3>
        <p className="text-gray-600 mb-4">
          Subscribe to our newsletter for the latest safari tips, exclusive offers, and destination insights.
        </p>
        <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
          <Input
            type="email"
            placeholder="Enter your email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="flex-1"
            disabled={loading}
          />
          <Button
            type="submit"
            disabled={loading}
            className="bg-[#D4C2A4] hover:bg-[#D4C2A4]/90 text-black px-6"
          >
            {loading ? 'Subscribing...' : 'Subscribe'}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default NewsletterSignup;

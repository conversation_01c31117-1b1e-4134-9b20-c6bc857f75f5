import { useState, useEffect } from 'react';

interface ResponsiveBreakpoints {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;
  screenWidth: number;
  screenHeight: number;
}

export const useResponsive = (): ResponsiveBreakpoints => {
  const [dimensions, setDimensions] = useState<ResponsiveBreakpoints>({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isLargeDesktop: false,
    screenWidth: 0,
    screenHeight: 0,
  });

  useEffect(() => {
    const updateDimensions = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      setDimensions({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024 && width < 1440,
        isLargeDesktop: width >= 1440,
        screenWidth: width,
        screenHeight: height,
      });
    };

    // Initial call
    updateDimensions();

    // Add event listener
    window.addEventListener('resize', updateDimensions);

    // Cleanup
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  return dimensions;
};

// Utility function for responsive values
export const getResponsiveValue = <T>(
  mobile: T,
  tablet: T,
  desktop: T,
  largeDesktop?: T
) => {
  const { isMobile, isTablet, isDesktop, isLargeDesktop } = useResponsive();

  if (isMobile) return mobile;
  if (isTablet) return tablet;
  if (isDesktop) return desktop;
  if (isLargeDesktop && largeDesktop) return largeDesktop;
  return desktop;
};

// Utility function for responsive classes
export const getResponsiveClasses = (
  baseClasses: string,
  mobileClasses?: string,
  tabletClasses?: string,
  desktopClasses?: string,
  largeDesktopClasses?: string
) => {
  const { isMobile, isTablet, isDesktop, isLargeDesktop } = useResponsive();

  let classes = baseClasses;

  if (isMobile && mobileClasses) {
    classes += ` ${mobileClasses}`;
  } else if (isTablet && tabletClasses) {
    classes += ` ${tabletClasses}`;
  } else if (isDesktop && desktopClasses) {
    classes += ` ${desktopClasses}`;
  } else if (isLargeDesktop && largeDesktopClasses) {
    classes += ` ${largeDesktopClasses}`;
  }

  return classes;
};


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Star, Upload } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ReviewFormProps {
  tourId: string;
  tourName: string;
  onSubmit: (review: any) => void;
}

const ReviewForm: React.FC<ReviewFormProps> = ({ tourId, tourName, onSubmit }) => {
  const { toast } = useToast();
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [photos, setPhotos] = useState<string[]>([]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (rating === 0) {
      toast({
        title: "Rating Required",
        description: "Please select a rating before submitting your review.",
        variant: "destructive"
      });
      return;
    }

    if (!title.trim() || !content.trim()) {
      toast({
        title: "Required Fields",
        description: "Please fill in both title and review content.",
        variant: "destructive"
      });
      return;
    }

    const newReview = {
      id: Date.now().toString(),
      author: "Current User", // In real app, get from auth context
      rating,
      title: title.trim(),
      content: content.trim(),
      date: new Date().toLocaleDateString(),
      tourName,
      verified: true,
      helpful: 0,
      photos
    };

    onSubmit(newReview);
    
    // Reset form
    setRating(0);
    setTitle('');
    setContent('');
    setPhotos([]);

    toast({
      title: "Review Submitted",
      description: "Thank you for your feedback! Your review has been submitted.",
    });
  };

  const handlePhotoUpload = () => {
    // Mock photo upload - in real app, implement file upload
    const mockPhotos = [
      'photo-1472396961693-142e6e269027',
      'photo-1466721591366-2d5fba72006d',
      'photo-1493962853295-0fd70327578a'
    ];
    const randomPhoto = mockPhotos[Math.floor(Math.random() * mockPhotos.length)];
    setPhotos([...photos, randomPhoto]);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Write a Review for {tourName}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Rating */}
          <div>
            <label className="block text-sm font-medium mb-2">Overall Rating</label>
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  className="focus:outline-none"
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= (hoveredRating || rating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium mb-2">Review Title</label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Summarize your experience..."
              maxLength={100}
            />
          </div>

          {/* Content */}
          <div>
            <label className="block text-sm font-medium mb-2">Your Review</label>
            <Textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Share details about your safari experience..."
              rows={5}
              maxLength={1000}
            />
            <div className="text-right text-sm text-gray-500 mt-1">
              {content.length}/1000 characters
            </div>
          </div>

          {/* Photo Upload */}
          <div>
            <label className="block text-sm font-medium mb-2">Add Photos (Optional)</label>
            <Button
              type="button"
              variant="outline"
              onClick={handlePhotoUpload}
              className="flex items-center space-x-2"
            >
              <Upload className="h-4 w-4" />
              <span>Upload Photos</span>
            </Button>
            {photos.length > 0 && (
              <div className="flex space-x-2 mt-2">
                {photos.map((photo, index) => (
                  <img
                    key={index}
                    src={`https://images.unsplash.com/${photo}?auto=format&fit=crop&w=100&h=100`}
                    alt={`Upload ${index + 1}`}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                ))}
              </div>
            )}
          </div>

          <Button type="submit" className="w-full">
            Submit Review
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default ReviewForm;

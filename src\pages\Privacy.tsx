
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Shield, Eye, Lock, Star } from 'lucide-react';

const Privacy = () => {
  return (
    <div className="min-h-screen bg-[#16191D] overflow-x-hidden">
      <Header />

      {/* Luxury Hero Section - Fully Responsive */}
      <div className="relative overflow-hidden bg-[#16191D] pt-12 xs:pt-14 sm:pt-16 md:pt-20">
        {/* Elegant Background Pattern - Responsive */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px),
                             radial-gradient(circle at 75% 75%, #D4C2A4 1px, transparent 1px)`,
            backgroundSize: '30px 30px'
          }} />
        </div>

        {/* Hero Content - Mobile Optimized */}
        <div className="relative z-10 container mx-auto px-3 xs:px-4 sm:px-6 md:px-8 py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 xl:py-24 text-center">
          <div className="max-w-5xl mx-auto">
            {/* Luxury Badge - Mobile Responsive */}
            <div className="inline-flex items-center gap-1 xs:gap-1.5 sm:gap-2 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-2 xs:px-3 sm:px-4 md:px-6 py-1 xs:py-1.5 sm:py-2 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
              <Shield className="w-2.5 h-2.5 xs:w-3 xs:h-3 sm:w-4 sm:h-4 text-[#D4C2A4]" />
              <span className="font-open-sans text-2xs xs:text-xs sm:text-sm text-[#D4C2A4] tracking-wider uppercase">Privacy Protection</span>
            </div>

            {/* Main Title - Highly Responsive */}
            <h1 className="font-cormorant text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl text-[#F2EEE6] mb-3 xs:mb-4 sm:mb-5 md:mb-6 leading-tight">
              Your Privacy
              <span className="block text-[#D4C2A4] italic">Matters to Us</span>
            </h1>

            {/* Description - Mobile Optimized */}
            <p className="font-open-sans text-[#F2EEE6]/70 text-xs xs:text-sm sm:text-base md:text-lg max-w-sm xs:max-w-md sm:max-w-xl md:max-w-2xl mx-auto leading-relaxed px-2 xs:px-3 sm:px-4">
              We are committed to protecting your personal information and ensuring transparency in how we collect, use, and safeguard your data.
            </p>

            {/* Decorative Element - Responsive */}
            <div className="mt-6 xs:mt-7 sm:mt-8 md:mt-10 lg:mt-12 flex justify-center">
              <div className="w-12 xs:w-14 sm:w-16 md:w-20 lg:w-24 h-px bg-gradient-to-r from-transparent via-[#D4C2A4] to-transparent"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Luxury Privacy Content - Fully Responsive */}
      <div className="relative bg-[#16191D] py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 xl:py-24">
        {/* Subtle Background Pattern - Mobile Optimized */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #D4C2A4 1px, transparent 1px)`,
            backgroundSize: '40px 40px'
          }}></div>
        </div>

        <div className="relative z-10 container mx-auto px-3 xs:px-4 sm:px-6 md:px-8">
          <div className="max-w-5xl mx-auto space-y-8 xs:space-y-10 sm:space-y-12 md:space-y-16 lg:space-y-20">

            {/* Information Collection Section - Mobile Responsive */}
            <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              {/* Section Header - Mobile Optimized */}
              <div className="flex items-center gap-2 xs:gap-3 sm:gap-4 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
                <div className="bg-[#D4C2A4]/20 p-1.5 xs:p-2 sm:p-3 rounded-md xs:rounded-lg sm:rounded-xl flex-shrink-0">
                  <Eye className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-[#D4C2A4]" />
                </div>
                <h2 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-[#F2EEE6] leading-tight">
                  Information Collection
                </h2>
              </div>

              {/* Content - Mobile Responsive */}
              <div className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6">
                <div className="relative">
                  <span className="font-cormorant float-left text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl leading-none pt-0.5 xs:pt-1 pr-2 xs:pr-3 sm:pr-4 text-[#D4C2A4] opacity-80">
                    W
                  </span>
                  <p className="font-open-sans text-[#F2EEE6]/90 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                    e collect information you provide directly to us when you create an account, make a booking, or contact us for safari tours. This includes your name, email address, phone number, travel preferences, and payment information. We also automatically collect certain information when you use our services, including your IP address, browser type, device information, and usage patterns. Location data may be collected when you use our mobile applications to enhance your safari experience. We collect this information to provide you with the best possible service and to ensure your safari adventure meets your expectations.
                  </p>
                </div>

                <div className="flex justify-center my-4 xs:my-5 sm:my-6 md:my-8">
                  <div className="w-16 xs:w-20 sm:w-24 md:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
                </div>

                <p className="font-open-sans text-[#F2EEE6]/80 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                  At Warrior of Africa Safari Tours, we are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website or use our services. We understand that planning a safari is a personal journey, and we treat your information with the utmost care and respect. By using our services, you agree to the collection and use of information in accordance with this policy. We will not use or share your information with anyone except as described in this Privacy Policy.
                </p>
              </div>
            </div>

            {/* Data Protection Section - Mobile Responsive */}
            <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              {/* Section Header - Mobile Optimized */}
              <div className="flex items-center gap-2 xs:gap-3 sm:gap-4 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
                <div className="bg-[#D4C2A4]/20 p-1.5 xs:p-2 sm:p-3 rounded-md xs:rounded-lg sm:rounded-xl flex-shrink-0">
                  <Lock className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-[#D4C2A4]" />
                </div>
                <h2 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-[#F2EEE6] leading-tight">
                  Data Protection
                </h2>
              </div>

              {/* Content - Mobile Responsive */}
              <div className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6">
                <div className="relative">
                  <span className="font-cormorant float-left text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl leading-none pt-0.5 xs:pt-1 pr-2 xs:pr-3 sm:pr-4 text-[#D4C2A4] opacity-80">
                    W
                  </span>
                  <p className="font-open-sans text-[#F2EEE6]/90 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                    e use your information to provide and improve our safari services, process bookings, communicate with you about your upcoming adventures, and ensure the security of our platform. We may also use your information for marketing purposes, but only with your explicit consent. We never sell your personal information to third parties. Your data helps us customize safari experiences, recommend suitable tours based on your preferences, and provide you with relevant updates about wildlife conservation efforts and new safari destinations. We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.
                  </p>
                </div>

                <div className="flex justify-center my-4 xs:my-5 sm:my-6 md:my-8">
                  <div className="w-16 xs:w-20 sm:w-24 md:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
                </div>

                <p className="font-open-sans text-[#F2EEE6]/80 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                  This includes encryption, secure servers, regular security audits, and staff training on data protection protocols. However, no method of transmission over the internet is 100% secure, and we cannot guarantee absolute security. We retain your personal information only for as long as necessary to fulfill the purposes outlined in this policy, unless a longer retention period is required by law. When we no longer need your information, we securely delete or anonymize it. We work with trusted third-party service providers who assist us in operating our website and conducting our business, and these parties are bound by strict confidentiality agreements.
                </p>
              </div>
            </div>

            {/* Your Rights Section - Mobile Responsive */}
            <div className="bg-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              {/* Section Header - Mobile Optimized */}
              <div className="flex items-center gap-2 xs:gap-3 sm:gap-4 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
                <div className="bg-[#D4C2A4]/20 p-1.5 xs:p-2 sm:p-3 rounded-md xs:rounded-lg sm:rounded-xl flex-shrink-0">
                  <Shield className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-[#D4C2A4]" />
                </div>
                <h2 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-[#F2EEE6] leading-tight">
                  Your Rights
                </h2>
              </div>

              {/* Content - Mobile Responsive */}
              <div className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6">
                <div className="relative">
                  <span className="font-cormorant float-left text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl leading-none pt-0.5 xs:pt-1 pr-2 xs:pr-3 sm:pr-4 text-[#D4C2A4] opacity-80">
                    Y
                  </span>
                  <p className="font-open-sans text-[#F2EEE6]/90 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                    ou have the right to access, update, or delete your personal information at any time. You can also opt out of marketing communications through the unsubscribe link in our emails or by contacting us directly. If you're in the European Union, you have additional rights under GDPR, including the right to data portability and the right to be forgotten. You can request a copy of all personal data we hold about you, and we will provide this information in a commonly used electronic format. You also have the right to restrict processing of your personal data in certain circumstances and to object to processing based on legitimate interests.
                  </p>
                </div>

                <div className="flex justify-center my-4 xs:my-5 sm:my-6 md:my-8">
                  <div className="w-16 xs:w-20 sm:w-24 md:w-32 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/50 to-transparent"></div>
                </div>

                <p className="font-open-sans text-[#F2EEE6]/80 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed text-justify">
                  If you have any questions about this Privacy Policy or our data practices, please contact <NAME_EMAIL> or through our website contact form. We are committed to resolving any privacy concerns promptly and transparently. This policy may be updated from time to time to reflect changes in our practices or legal requirements. We will notify you of any material changes by posting the new policy on our website and updating the effective date. Your continued use of our services after any changes indicates your acceptance of the updated policy. We encourage you to review this policy periodically to stay informed about how we protect your information.
                </p>
              </div>
            </div>

            {/* Premium Contact Section - Fully Mobile Responsive */}
            <div className="bg-gradient-to-br from-[#D4C2A4]/10 to-[#D4C2A4]/5 backdrop-blur-xl border border-[#D4C2A4]/30 rounded-xl xs:rounded-2xl sm:rounded-3xl p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 xl:p-12 shadow-2xl">
              <div className="text-center">
                <Star className="w-6 h-6 xs:w-7 xs:h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 text-[#D4C2A4] mx-auto mb-3 xs:mb-4 sm:mb-5 md:mb-6" />
                <h3 className="font-cormorant text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl text-[#F2EEE6] mb-2 xs:mb-3 sm:mb-4 leading-tight">
                  Privacy Questions?
                </h3>
                <p className="font-open-sans text-[#F2EEE6]/70 text-xs xs:text-sm sm:text-base md:text-lg leading-relaxed max-w-sm xs:max-w-md sm:max-w-lg md:max-w-xl lg:max-w-2xl mx-auto mb-4 xs:mb-5 sm:mb-6 md:mb-8 px-2">
                  Our privacy team is here to address any concerns and ensure you understand how we protect your personal information.
                </p>
                <div className="inline-flex items-center gap-1.5 xs:gap-2 bg-[#D4C2A4]/20 backdrop-blur-sm border border-[#D4C2A4]/30 rounded-full px-3 xs:px-4 sm:px-5 md:px-6 py-1.5 xs:py-2 sm:py-2.5 md:py-3">
                  <span className="font-open-sans text-[#D4C2A4] text-xs xs:text-sm sm:text-base font-medium break-all xs:break-normal">
                    <EMAIL>
                  </span>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <Footer isDarkBackground={true} />
    </div>
  );
};

export default Privacy;


